package com.wzsec.clean.common.controller;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.ES7Util;
import org.apache.http.HttpHost;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.RandomScoreFunctionBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * [中移在线]Elasticsearch对外接口查询
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
public class ElasticsearchController {

    /**
     * GET 接口：从指定索引随机查询指定条数的数据[提供外部查询取样例]
     *
     * @param index 索引名称
     * @param count 查询条数
     * @return 查询结果列表
     */
    @GetMapping("/api/search")
    public List<Map<String, Object>> searchRandom(@RequestParam("index") String index, @RequestParam("count") int count) {
        List<Map<String, Object>> results = new ArrayList<>();

        // 每次查询时创建新的客户端并在查询完成后关闭
        try (RestHighLevelClient client = getClient(
                ConfigurationManager.getProperty("netflow.es.username").trim(),
                ConfigurationManager.getProperty("netflow.es.password").trim(),
                ConfigurationManager.getProperty("netflow.es.hostlist").trim())) {

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(QueryBuilders.functionScoreQuery(QueryBuilders.matchAllQuery(),
                            new RandomScoreFunctionBuilder().seed(System.currentTimeMillis())))
                    .size(count);

            searchRequest.source(sourceBuilder);

            // 执行搜索
            assert client != null;
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);

            // 解析搜索结果
            response.getHits().forEach(hit -> results.add(hit.getSourceAsMap()));

        } catch (IOException e) {
            // 处理异常
            e.printStackTrace();
        }

        return results;
    }


    /**
     * 获取ES客户端连接
     *
     * @param username 用户名
     * @param password 密码
     * @param hosts    Elasticsearch 集群的IP和端口，格式为 "ip:port,ip:port" 的字符串
     * @return RestHighLevelClient
     */
    public static RestHighLevelClient getClient(String username, String password, String hosts) {
        try {
            // 创建认证对象
            BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    org.apache.http.auth.AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password)
            );

            // 创建连接池管理器
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

            // 配置请求信息，设置更长的连接和读取超时
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(0) // 设置读取数据的超时，0表示无限制
                    .setConnectTimeout(60000) // 设置连接超时，确保能建立连接
                    .build();

            // 创建HttpClient
            HttpClientBuilder httpClientBuilder = HttpClients.custom()
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setDefaultRequestConfig(requestConfig)
                    .setConnectionManager(connectionManager);

            // 解析输入的hosts，支持多个节点，分隔符为逗号
            String[] hostArray = hosts.split(",");
            HttpHost[] httpHosts = new HttpHost[hostArray.length];

            for (int i = 0; i < hostArray.length; i++) {
                String[] ipPort = hostArray[i].split(":");
                if (ipPort.length == 2) {
                    httpHosts[i] = new HttpHost(ipPort[0], Integer.parseInt(ipPort[1]));
                } else {
                    throw new IllegalArgumentException("主机格式无效");
                }
            }

            // 创建 RestClientBuilder 并传入所有的 HttpHost
            RestClientBuilder builder = RestClient.builder(httpHosts);

            // 设置认证和其他配置
            builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                @Override
                public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                }
            });

            // 创建 RestHighLevelClient
            return new RestHighLevelClient(builder);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
