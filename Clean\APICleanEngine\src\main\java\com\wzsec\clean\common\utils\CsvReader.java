/**
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 * <p>
 * [2015] - [2020] China Telecom Corporation Limited,
 * All Rights Reserved.
 * <p>
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 *
 * @Title: CSVReader.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年1月22日
 */
/**
 * @Title: CSVReader.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年1月22日
 */

package com.wzsec.clean.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: CSVReader
 * @Description: TODO
 * @date 2019年1月22日
 */
public class CsvReader {

    private final static Logger logger = LoggerFactory.getLogger(CsvReader.class);

    /**
     * @throws IOException
     * @Description: 读取CSV文件
     * <AUTHOR>
     * @date 2019年1月22日
     */
    public static List<String> readCsv(String filePath) throws IOException {
        InputStreamReader inStreamReader = null;
        BufferedReader bufReader = null;
        try {
            inStreamReader = new InputStreamReader(new FileInputStream(new File(filePath)), "GBK"); // 取决于csv文件编码格式
            bufReader = new BufferedReader(inStreamReader);
        } catch (FileNotFoundException ex) {
            ex.printStackTrace();
        }

        String strLine = ""; // 每行数据
        List<String> strDataList = new ArrayList<String>(); // 拆分后存放到List
        try {
            while ((strLine = bufReader.readLine()) != null) {
                for (String strData : strLine.split("。|，|,| |；|;|：|\t|-|=|:")) {
                    if (!"".equals(strData.trim())) {
                        strDataList.add(strData.trim());
                    }
                }
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            if (inStreamReader != null) {
                inStreamReader.close();
            }
            if (bufReader != null) {
                bufReader.close();
            }
        }
        return strDataList;
    }

}
