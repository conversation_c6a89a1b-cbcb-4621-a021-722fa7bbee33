package com.wzsec.clean.filter.parser;

import com.wzsec.clean.modules.model.ApiEncryptedtraffic;
import org.pcap4j.packet.namednumber.IpNumber;
import org.pcap4j.packet.namednumber.TcpPort;

import java.net.Inet4Address;
import java.util.List;
import java.util.Map;

/**
 * HTTPS采集抽取
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
public class HTTPSCollection {

    /**
     * @description TODO  判断首次进来的请求端口, 响应端口
     * <AUTHOR>
     * @date 2025-01-17
     */
    public static void checkPort(String requestTime,
                                 List<ApiEncryptedtraffic> apiEncryptedtrafficList,
                                 Map<Integer, Integer> firstPortMap,
                                 Inet4Address srcAddr,
                                 Inet4Address dstAddr,
                                 TcpPort srcPort,
                                 TcpPort dstPort,
                                 IpNumber protocol) {
        // 首次调用时存储端口号
        if (firstPortMap.isEmpty()) {
            if (String.valueOf(srcPort.valueAsInt()).length() < 5) {
                firstPortMap.put(srcPort.valueAsInt(), srcPort.valueAsInt());
                doNextOperation(requestTime, apiEncryptedtrafficList, srcAddr
                        , dstAddr, srcPort, dstPort, protocol);
            }
            return;
        }
        // 获取第一次存储的端口号
        Integer firstPort = firstPortMap.values().iterator().next();
        if (firstPort == srcPort.valueAsInt()) {
            doNextOperation(requestTime, apiEncryptedtrafficList, srcAddr
                    , dstAddr, srcPort, dstPort, protocol);
        }
    }

    /**
     * @description TODO 匹配首次进来的请求端口, 响应端口写入接口加密流量表
     * <AUTHOR>
     * @date 2025-01-17
     */
    private static void doNextOperation(String requestTime,
                                        List<ApiEncryptedtraffic> apiEncryptedtrafficList,
                                        Inet4Address srcAddr,
                                        Inet4Address dstAddr,
                                        TcpPort srcPort,
                                        TcpPort dstPort,
                                        IpNumber protocol) {
        ApiEncryptedtraffic apiEncryptedtraffic = new ApiEncryptedtraffic();
        apiEncryptedtraffic.setReqip(dstAddr.getHostAddress());
        apiEncryptedtraffic.setReqport(String.valueOf(dstPort.valueAsInt()));
        //TODO 协议标注
        apiEncryptedtraffic.setProtocol(String.valueOf(protocol));
        apiEncryptedtraffic.setResip(srcAddr.getHostAddress());
        apiEncryptedtraffic.setResport(String.valueOf(srcPort.valueAsInt()));
        apiEncryptedtraffic.setCalltime(requestTime);
        apiEncryptedtrafficList.add(apiEncryptedtraffic);
    }
}
