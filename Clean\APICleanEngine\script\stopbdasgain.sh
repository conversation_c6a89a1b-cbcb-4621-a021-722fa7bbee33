#!/bin/bash
#备注：-eq //equals等于; -ne //no equals不等于
#接口审计采集清洗启动脚本

#1.检查 CleanEngine 服务是否正常
# 需改动地方: CleanEngine-1.2.1.jar , 以实际jar名调整
bdasCleanEngineName=CleanEngine-1.2.1.jar
bdasCleanEngineProID=`ps -ef | grep "$bdasCleanEngineName" | grep -v "$0" | grep -v "grep" | awk '{print $2}'`
kill -9	$bdasCleanEngineProID
sleep 5s
bdasCleanEngineProCount=`ps -ef |grep "$bdasCleanEngineName" |grep -v "grep" |wc -l`
if [ 0 != $bdasCleanEngineProCount ]
then
  kill -9 $bdasCleanEngineProCount
else
  echo "接口审计系统${bdasCleanEngineName}程序停止运行"
fi

#2.检查 CollectTool 服务是否正常
# 需改动地方: CollectTool-0.0.1-SNAPSHOT.jar , 以实际jar名调整
bdasCollectToolName=CollectTool-0.0.1-SNAPSHOT.jar
bdasCollectToolProID=`ps -ef | grep "$bdasCollectToolName" | grep -v "$0" | grep -v "grep" | awk '{print $2}'`
kill -9	$bdasCollectToolProID
sleep 5s
bdasCollectToolProCount=`ps -ef |grep "$bdasCollectToolName" |grep -v "grep" |wc -l`
if [ 0 != $bdasCollectToolProCount ]
then
  kill -9 $bdasCollectToolProCount
else
  echo "接口审计系统${bdasCollectToolName}程序停止运行"
fi