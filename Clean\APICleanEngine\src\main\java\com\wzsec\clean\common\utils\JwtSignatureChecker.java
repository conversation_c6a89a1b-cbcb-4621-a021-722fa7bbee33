package com.wzsec.clean.common.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * jwt签名算法提取(处理请求头中Authorization,Cookie[token=],X-Access-Token,X-JWT-Token)
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
public class JwtSignatureChecker {

    /**
     * 获取jwt签名算法
     *
     * @param headers 请求头
     * @return {@link String }
     */
    public static String getJwtSignatureAlgorithm(Map<String, String> headers) {
        String jwt = extractJwt(headers);
        if (jwt == null) {
            return "";
        }

        String[] jwtParts = jwt.split("\\.");
        if (jwtParts.length > 1) {
            try {
                String header = new String(Base64.getUrlDecoder().decode(jwtParts[0]), StandardCharsets.UTF_8);
                if (header.contains("\"alg\"")) {
                    return header.replaceAll(".*\"alg\":\"([^\"]+)\".*", "$1");
                }
            } catch (IllegalArgumentException e) {
                System.out.println("Base64 解码失败: " + e.getMessage());
                return "";
            }
        }
        return "";
    }

    /**
     * 提取JWT编码
     *
     * @param headers 标题
     * @return {@link String }
     */
    private static String extractJwt(Map<String, String> headers) {
        if (headers.containsKey("Authorization")) {
            return decodeUrl(headers.get("Authorization").replace("Bearer ", "").trim());
        } else if (headers.containsKey("Cookie")) {
            String cookie = headers.get("Cookie");
            String token = extractTokenFromCookie(cookie);
            if (token != null) {
                return decodeUrl(token.replace("Bearer%20", "").trim());
            }
        } else if (headers.containsKey("X-Access-Token")) {
            String token = headers.get("X-Access-Token");
            return token.startsWith("Bearer ") ? decodeUrl(token.replace("Bearer ", "").trim()) : token;
        } else if (headers.containsKey("X-JWT-Token")) {
            String token = headers.get("X-JWT-Token");
            return token.startsWith("Bearer ") ? decodeUrl(token.replace("Bearer ", "").trim()) : token;
        }
        return null;
    }

    /**
     * 解码url
     *
     * @param value 价值
     * @return {@link String }
     */
    private static String decodeUrl(String value) {
        try {
            return URLDecoder.decode(value, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            return value; // 如果解码失败，返回原始值
        }
    }

    /**
     * 从cookie中提取令牌
     *
     * @param cookie 饼干
     * @return {@link String }
     */
    private static String extractTokenFromCookie(String cookie) {
        String[] cookies = cookie.split(";");
        for (String c : cookies) {
            if (c.trim().startsWith("token=")) {
                return c.split("=")[1].trim();
            }
        }
        return null;
    }
}
