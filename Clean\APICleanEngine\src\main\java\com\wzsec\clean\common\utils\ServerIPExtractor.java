package com.wzsec.clean.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于从HTTP响应头中提取服务端IP地址
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
public class ServerIPExtractor {

    /**
     * 常见的包含服务端IP的HTTP头字段
     * 按照可信度频率排序：
     * 1. Server-IP: 直接包含服务器IP地址
     * 2. X-Served-By: 通常包含服务器标识或IP
     * 3. X-Server-IP: 某些服务器使用的头，直接包含服务器IP
     * 4. X-Backend-Server: 某些负载均衡器添加的头，包含后端服务器信息
     */
    private static final String SERVER_IP = "server-ip";
    private static final String X_SERVED_BY = "x-served-by";
    private static final String X_SERVER_IP = "x-server-ip";
    private static final String X_BACKEND_SERVER = "x-backend-server";

    /**
     * 从HTTP响应头字符串中提取服务端IP地址
     * 按照预定义的优先级顺序依次检查各HTTP头字段：
     * 1. Server-IP - 直接包含服务器IP地址
     * 2. X-Served-By - 通常包含服务器标识或IP
     * 3. X-Server-IP - 某些服务器使用的头，直接包含服务器IP
     * 4. X-Backend-Server - 某些负载均衡器添加的头，包含后端服务器信息
     *
     * @param responseData 完整的HTTP响应头字符串
     * @return {@code String }
     */
    public static String extractServerIPFromHeader(String responseData) {
        if (StringUtils.isBlank(responseData)) {
            return null;
        }

        // 将响应数据转为小写以便不区分大小写匹配
        String lowerCaseResponseData = responseData.toLowerCase();

        // 按优先级尝试提取IP
        String ip = extractHeaderValue(lowerCaseResponseData, SERVER_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        ip = extractHeaderValue(lowerCaseResponseData, X_SERVED_BY);
        if (StringUtils.isNotBlank(ip)) {
            // X-Served-By可能包含服务器标识和IP，尝试提取IP部分
            ip = extractIPFromServerInfo(ip);
            return ip;
        }

        ip = extractHeaderValue(lowerCaseResponseData, X_SERVER_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        ip = extractHeaderValue(lowerCaseResponseData, X_BACKEND_SERVER);
        if (StringUtils.isNotBlank(ip)) {
            // X-Backend-Server可能包含服务器标识和IP，尝试提取IP部分
            ip = extractIPFromServerInfo(ip);
            return ip;
        }

        return null;
    }

    /**
     * 从响应头映射中提取服务端IP地址
     * 1. Server-IP - 直接包含服务器IP地址
     * 2. X-Served-By - 通常包含服务器标识或IP
     * 3. X-Server-IP - 某些服务器使用的头，直接包含服务器IP
     * 4. X-Backend-Server - 某些负载均衡器添加的头，包含后端服务器信息
     *
     * @param responseHeaderMap 响应头映射，键为头字段名，值为头字段值
     * @return {@code String }
     */
    public static String extractServerIPFromHeaderMap(Map<String, String> responseHeaderMap) {
        if (responseHeaderMap == null || responseHeaderMap.isEmpty()) {
            return null;
        }

        // 按优先级尝试提取IP
        String ip = null;

        // 尝试从Server-IP获取（直接包含服务器IP地址）
        ip = getHeaderValueIgnoreCase(responseHeaderMap, SERVER_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        // 尝试从X-Served-By获取（通常包含服务器标识或IP）
        ip = getHeaderValueIgnoreCase(responseHeaderMap, X_SERVED_BY);
        if (StringUtils.isNotBlank(ip)) {
            // X-Served-By可能包含服务器标识和IP，尝试提取IP部分
            ip = extractIPFromServerInfo(ip);
            return ip;
        }

        // 尝试从X-Server-IP获取
        ip = getHeaderValueIgnoreCase(responseHeaderMap, X_SERVER_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        // 尝试从X-Backend-Server获取
        ip = getHeaderValueIgnoreCase(responseHeaderMap, X_BACKEND_SERVER);
        if (StringUtils.isNotBlank(ip)) {
            // X-Backend-Server可能包含服务器标识和IP，尝试提取IP部分
            ip = extractIPFromServerInfo(ip);
            return ip;
        }

        return null;
    }

    /**
     * 从响应头字符串中提取指定头字段的值
     *
     * @param header     响应头字符串，应已转为小写
     * @param headerName 头字段名称，小写形式
     * @return {@code String }
     */
    private static String extractHeaderValue(String header, String headerName) {
        String pattern = headerName + ": (.+)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(header);
        if (m.find()) {
            return m.group(1).trim();
        }
        return null;
    }

    /**
     * 从响应头映射中获取指定头字段的值（不区分大小写）
     * 1. 直接使用原始小写形式查找
     * 2. 使用首字母大写形式查找（如"X-Served-By"）
     * 3. 遍历所有键进行不区分大小写比较
     *
     * @param headerMap  响应头映射
     * @param headerName 头字段名称，小写形式
     * @return {@code String }
     */
    private static String getHeaderValueIgnoreCase(Map<String, String> headerMap, String headerName) {
        // 直接尝试获取（小写形式）
        String value = headerMap.get(headerName);
        if (StringUtils.isNotBlank(value)) {
            return value.trim();
        }

        // 尝试获取（大写首字母形式，如"X-Served-By"）
        String capitalizedHeaderName = capitalizeWords(headerName);
        value = headerMap.get(capitalizedHeaderName);
        if (StringUtils.isNotBlank(value)) {
            return value.trim();
        }

        // 遍历所有键，不区分大小写比较（最全面但效率较低的方法）
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(headerName)) {
                return entry.getValue().trim();
            }
        }

        return null;
    }

    /**
     * 将字符串中的每个单词首字母大写
     *
     * @param str str
     * @return {@code String }
     */
    private static String capitalizeWords(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }

        String[] words = str.split("-");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            if (words[i].length() > 0) {
                if (i > 0) {
                    result.append("-");
                }
                result.append(Character.toUpperCase(words[i].charAt(0)));
                if (words[i].length() > 1) {
                    result.append(words[i].substring(1));
                }
            }
        }

        return result.toString();
    }

    /**
     * 从服务器信息中提取IP地址
     * 服务器信息格式可能为: servername(*******), *******, servername:******* 等
     * 尝试提取其中的IP地址部分
     *
     * @param serverInfo 服务器信息字符串
     * @return {@code String }
     */
    private static String extractIPFromServerInfo(String serverInfo) {
        if (StringUtils.isBlank(serverInfo)) {
            return serverInfo;
        }

        // 尝试匹配IPv4地址模式
        Pattern ipPattern = Pattern.compile("\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b");
        Matcher matcher = ipPattern.matcher(serverInfo);
        if (matcher.find()) {
            return matcher.group().trim();
        }

        return serverInfo.trim();
    }
}