package com.wzsec.clean.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.Collection;
import java.util.Map;

/**
 * 内存优化工具类
 * 提供内存监控、清理和优化功能
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MemoryOptimizer {
    
    private static final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
    
    // 内存使用率阈值
    private static final double MEMORY_THRESHOLD = 0.8; // 80%
    
    /**
     * 获取当前内存使用情况
     * @return 内存使用率 (0.0 - 1.0)
     */
    public static double getMemoryUsageRatio() {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        long used = heapMemoryUsage.getUsed();
        long max = heapMemoryUsage.getMax();
        return max > 0 ? (double) used / max : 0.0;
    }
    
    /**
     * 获取内存使用信息
     * @return 格式化的内存使用信息
     */
    public static String getMemoryInfo() {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        long used = heapMemoryUsage.getUsed();
        long committed = heapMemoryUsage.getCommitted();
        long max = heapMemoryUsage.getMax();
        
        return String.format("Memory Usage: Used=%dMB, Committed=%dMB, Max=%dMB, Usage=%.2f%%",
                used / 1024 / 1024,
                committed / 1024 / 1024,
                max / 1024 / 1024,
                getMemoryUsageRatio() * 100);
    }
    
    /**
     * 检查是否需要进行内存清理
     * @return true if memory usage is high
     */
    public static boolean shouldCleanMemory() {
        return getMemoryUsageRatio() > MEMORY_THRESHOLD;
    }
    
    /**
     * 安全清理集合
     * @param collection 要清理的集合
     */
    public static void safeCleanCollection(Collection<?> collection) {
        if (collection != null && !collection.isEmpty()) {
            try {
                collection.clear();
                log.debug("Cleaned collection with {} elements", collection.size());
            } catch (Exception e) {
                log.warn("Failed to clean collection: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 安全清理Map
     * @param map 要清理的Map
     */
    public static void safeCleanMap(Map<?, ?> map) {
        if (map != null && !map.isEmpty()) {
            try {
                int size = map.size();
                map.clear();
                log.debug("Cleaned map with {} entries", size);
            } catch (Exception e) {
                log.warn("Failed to clean map: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 强制垃圾回收（谨慎使用）
     */
    public static void forceGC() {
        if (shouldCleanMemory()) {
            log.info("Memory usage high ({}%), forcing garbage collection", 
                    String.format("%.2f", getMemoryUsageRatio() * 100));
            System.gc();
            System.runFinalization();
            
            // 等待一小段时间让GC完成
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            log.info("After GC: {}", getMemoryInfo());
        }
    }
    
    /**
     * 检查字符串大小是否超过限制
     * @param str 要检查的字符串
     * @param maxSizeMB 最大大小（MB）
     * @return true if string is too large
     */
    public static boolean isStringTooLarge(String str, int maxSizeMB) {
        if (str == null) return false;
        
        // 估算字符串占用的内存大小（每个字符大约2字节）
        long estimatedSize = str.length() * 2L;
        long maxSize = maxSizeMB * 1024L * 1024L;
        
        return estimatedSize > maxSize;
    }
    
    /**
     * 检查字节数组大小是否超过限制
     * @param bytes 要检查的字节数组
     * @param maxSizeMB 最大大小（MB）
     * @return true if byte array is too large
     */
    public static boolean isByteArrayTooLarge(byte[] bytes, int maxSizeMB) {
        if (bytes == null) return false;
        
        long maxSize = maxSizeMB * 1024L * 1024L;
        return bytes.length > maxSize;
    }
    
    /**
     * 安全的字符串截取，避免内存溢出
     * @param str 原字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String safeTruncateString(String str, int maxLength) {
        if (str == null) return null;
        if (str.length() <= maxLength) return str;
        
        log.warn("String too long ({}), truncating to {} characters", str.length(), maxLength);
        return str.substring(0, maxLength) + "...[truncated]";
    }
    
    /**
     * 监控内存使用情况并记录日志
     */
    public static void logMemoryUsage() {
        if (log.isDebugEnabled()) {
            log.debug(getMemoryInfo());
        }
        
        if (shouldCleanMemory()) {
            log.warn("High memory usage detected: {}", getMemoryInfo());
        }
    }
    
    /**
     * 清理ThreadLocal变量
     * @param threadLocal 要清理的ThreadLocal
     */
    public static void cleanThreadLocal(ThreadLocal<?> threadLocal) {
        if (threadLocal != null) {
            try {
                threadLocal.remove();
                log.debug("Cleaned ThreadLocal variable");
            } catch (Exception e) {
                log.warn("Failed to clean ThreadLocal: {}", e.getMessage());
            }
        }
    }
}
