package com.wzsec.clean.common.analysis.thread;

import com.alibaba.fastjson.JSONObject;
import com.wzsec.clean.common.rule.ProCommonRule;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.modules.model.HTTPResult;
import com.wzsec.clean.modules.model.HTTPResultDetail;
import com.wzsec.clean.modules.model.PcapTask;
import com.wzsec.clean.modules.service.PcapService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;

/**
 * Title: Packetbeat 读取ES保存流量
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/12/11
 */
@Component
@Transactional
public class PacketbeatParser {

    private static Logger logger = LoggerFactory.getLogger(PacketbeatParser.class);

    private static Map<String, Integer> countMap = new HashMap<>();
    private static Map<String, HTTPResultDetail> resultMap = new HashMap<>();
    // 敏感数据检测管理查询(key=敏感数据，value=敏感数据英文名)
    public static HashMap<String, String> httpSensitivedataMap = new HashMap<>();
    public static HashMap<String, String> ftpSensitivedataMap = new HashMap<>();
    //统计每行包含结果类型
    public static HashSet<String> resultTypeSet = new HashSet<>();
    // 明文统计次数结果Map
    public static Map<String, Map<String, Integer>> sensitiveDataCountResultMap = new HashMap<>();
    //结果类型
    public static Map<String, Integer> resTypeMap = new HashMap<>();
    //检测总次数
    public static HashMap<String, Integer> totalCountByMethodMap = new HashMap<>();

    private static PcapService pcapService;

    @Autowired
    public void setService(PcapService pcapService) {
        PacketbeatParser.pcapService = pcapService;
    }

    /**
     * 从ES检测pcap文件
     */
    public static void checkPcapFromES(PcapTask pcapTask) {
        try {
            // 手机号白名单初始化
            Const.whitePhoneList = pcapService.getFileListInfo(Const.PHONEDETECTION, Const.WHITELIST);
            // 敏感数据检测管理查询
            httpSensitivedataMap = pcapService.selectSensitiveData(Const.INTERFACE_FLOW_SIGN);
            // 结果类型
            resTypeMap = pcapService.selectResultType();

            // 开始时间
            String startTime = TimeUtils.getNowTime();


            String pcapDate = ConfigurationManager.getProperty("netflow.checkdate").trim();
            int IntervalDay = Integer.parseInt(ConfigurationManager.getProperty("netflow.checkintervalday").trim());


            // ES信息
            String index = ConfigurationManager.getProperty("netflow.es.index.prefix");
            String username = ConfigurationManager.getProperty("netflow.es.username");
            String password = ConfigurationManager.getProperty("netflow.es.password");
            // String ip = ConfigurationManager.getProperty("netflow.es.ip");
            // int port = Integer.parseInt(ConfigurationManager.getProperty("netflow.es.port"));
            String hosts = ConfigurationManager.getProperty("netflow.es.hostlist");
            long timeValueMinutes = Long.parseLong(ConfigurationManager.getProperty("netflow.es.scrolltimeValueMinutes"));
            int size = Integer.parseInt(ConfigurationManager.getProperty("netflow.es.scrollsize"));
            // String flowfileoutpath = ConfigurationManager.getProperty("pb.flow.es.flowfileoutpath");

            //ES检测时间
            String flowcheckdate = "";
            if (StringUtils.isEmpty(pcapDate)) {
                //获取前一天日期
                flowcheckdate = TimeUtils.getYesterdayByCalendar("yyyy-MM-dd", IntervalDay);
            } else {
                //将20201111格式转成2020-11-11格式
                flowcheckdate = TimeUtils.convertStrDateToStr(pcapDate, "yyyyMMdd", "yyyy-MM-dd");
            }


            String HttpIndexName = "";
            if (index.endsWith(Const.AUDIT_SPLIT_JOIN)) {
                HttpIndexName = index + flowcheckdate.replace(Const.AUDIT_SPLIT_JOIN, Const.AUDIT_POINT_JOIN);
            } else {
                HttpIndexName = index + Const.AUDIT_SPLIT_JOIN + flowcheckdate.replace(Const.AUDIT_SPLIT_JOIN, Const.AUDIT_POINT_JOIN);
            }

            if (Const.INTERFACE_FLOW_SIGN.equals(pcapTask.getType())) {
                // 处理ES中HTTP数据
                searchCheckByPageScroll(flowcheckdate, username, password, hosts, timeValueMinutes, size, HttpIndexName);
                // HTTP流量结果统计保存信息
                recordHTTPCheckResultInfo(pcapTask.getTaskname(), startTime);
            }


            //结束时间
            String endTime = TimeUtils.getNowTime();
            logger.info("开始时间：" + startTime);
            logger.info("结束时间：" + endTime);
            int time = TimeUtils.getTimeSecondsByBothDate(startTime, endTime);
            logger.info("任务号" + pcapTask.getTaskname() + ",Packetbeat流量检测共耗时：" + time + "秒");

            // 变量销毁
            destruction();
        } catch (Exception e) {
            logger.info("检测流量结果统计出现异常：" + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 适合大量数据，根据ScrollId
     *
     * @throws IOException
     */
    public static void searchCheckByPageScroll(String checkdate, String username, String password, String hosts, long timeValueMinutes, int size, String index) {
        try {
            RestHighLevelClient client = ES7Util.getClient(username, password, hosts);

            // 获取所有索引
            /*GetIndexRequest getIndexRequest = new GetIndexRequest("*packetbeat-7.9.1*");
            GetIndexResponse getIndexResponse = client.indices().get(getIndexRequest, RequestOptions.DEFAULT);
            String[] indices = getIndexResponse.getIndices();
            List<String> asList = Arrays.asList(indices);*/

            // 存活时间，当索引数据量特别大时，出现超时可能性大，此值适当调大
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(timeValueMinutes));// 缓存存在的时长
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("@timestamp", checkdate))
                    .must(QueryBuilders.termQuery("network.protocol", "http")));
            searchSourceBuilder.size(size);// 页大小
            // 设置请求
            SearchRequest searchRequest = new SearchRequest()
                    // ES7已经去掉type，查询时不加type
                    .indices(index).scroll(scroll).source(searchSourceBuilder);
            // 发起请求
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 读取响应
            String scrollId = searchResponse.getScrollId();
            logger.info("结果总条数:" + searchResponse.getHits().getTotalHits().value);
            SearchHit[] searchHits = searchResponse.getHits().getHits();// 第一页数据
            int dataPageCount = 0, dataLineCount = 0;
            // 遍历搜索命中的数据，直到没有数据
            while (searchHits != null && searchHits.length > 0) {
                dataPageCount++;
                // System.out.println("当前页大小：" + searchHits.length);
                if (searchHits != null && searchHits.length > 0) {
                    int i = 0;
                    for (SearchHit searchHit : searchHits) {
                        dataLineCount++;
                        i++;
                        String source = searchHit.getSourceAsString();
                        // System.out.println("遍历第" + dataPageCount + "页第" + i + "条===" + source);
                        // 处理http协议数据
                        httpDataClean(source, checkdate);
                    }
                }
                // 再次发送请求,并使用上次搜索结果的ScrollId
                // 设置请求
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                // 发起请求
                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                // 读取响应
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }
            logger.info("数据总页数：" + dataPageCount);
            logger.info("数据总数量：" + dataLineCount);

            // 清除缓存的scroll
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            boolean succeeded = clearScrollResponse.isSucceeded();// 是否清除成功
            System.out.println("清除scroll:" + succeeded);
            ES7Util.closeEsClient(client);
            logger.info("已断开连接");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * HTTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * <AUTHOR>
     * @date 2020-11-02
     */
    private static void recordHTTPCheckResultInfo(String taskNum, String startTime) throws IOException {

        Map<String, HTTPResultDetail> detailMap = new HashMap<>();
        Map<String, HTTPResult> outlineMap = new HashMap<>();

        //保存pcap文件信息
        if (resultMap != null && resultMap.size() != 0) {

            Set<String> keySet = resultMap.keySet();
            for (String key : keySet) {
                HTTPResultDetail httpResultDetail = resultMap.get(key);

                String checkRule = "p_checkSensitiveData";
                // 响应参数
                String[] datas = httpResultDetail.getRescontent().split(",|:|\"|\\{|}|\\[|]|\r\n");
                ArrayList<String> list = convertArrToList(datas);


                if (list != null) {

                    List<Integer> resultTypeList = checkSensitiveDataByUseRule(Const.HTTP_SIGN, key, list);

                    //概要统计接口调用总数
                    String totalcount = "";
                    String countKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN));
                    if (countMap.containsKey(countKey)) {
                        totalcount = String.valueOf(countMap.get(countKey));
                    }

                    if (resultTypeList != null) {
                        for (Integer resyltTypeNum : resultTypeList) {

                            HTTPResultDetail httpResultDetailNew = new HTTPResultDetail();
                            httpResultDetailNew.setSeqnumber(httpResultDetail.getSeqnumber());
                            httpResultDetailNew.setInterfaceuri(httpResultDetail.getInterfaceuri());
                            httpResultDetailNew.setChecktime(startTime);
                            httpResultDetailNew.setStarttime(httpResultDetail.getStarttime());
                            httpResultDetailNew.setEndtime(httpResultDetail.getEndtime());
                            httpResultDetailNew.setSourceip(httpResultDetail.getSourceip());
                            httpResultDetailNew.setSourceport(httpResultDetail.getSourceport());
                            httpResultDetailNew.setDesip(httpResultDetail.getDesip());
                            httpResultDetailNew.setDesport(httpResultDetail.getDesport());
                            httpResultDetailNew.setRescontent(httpResultDetail.getRescontent());
                            httpResultDetailNew.setReqcontent(httpResultDetail.getReqcontent());
                            httpResultDetailNew.setResstatus(httpResultDetail.getResstatus());
                            httpResultDetailNew.setResstatuscode(httpResultDetail.getResstatuscode());
                            httpResultDetailNew.setReqmethod(httpResultDetail.getReqmethod());
                            httpResultDetailNew.setUserid(httpResultDetail.getUserid());
                            httpResultDetailNew.setUsername(httpResultDetail.getUsername());
                            httpResultDetailNew.setCheckrule(checkRule);

                            String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                            //根据id查询结果类型
                            Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);

                            httpResultDetailNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                            if (!httpResultDetailNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                                httpResultDetailNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getResulttype())));
                            }
                            httpResultDetailNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");

                            Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getResulttype());
                            Collection<Integer> values = checkCountMap.values();
                            int checkCount = 0;
                            for (Integer value : values) {
                                checkCount += value;
                            }
                            Integer totalCount = totalCountByMethodMap.get(key);
                            httpResultDetailNew.setResulttypecount(String.valueOf(checkCount));
                            httpResultDetailNew.setChecktotalcount(String.valueOf(totalCount));
                            double rate = 100 * ((double) checkCount / totalCount);
                            BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                            double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            httpResultDetailNew.setRatio(String.valueOf(ratio));
                            detailMap.put(resKey, httpResultDetailNew);


                            String riskKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN)) + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getRisk();
                            if (outlineMap.containsKey(riskKey)) {
                                HTTPResult result = outlineMap.get(riskKey);
                                result.setRisk(httpResultDetailNew.getRisk());
                                result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
                                outlineMap.put(riskKey, result);
                            } else {
                                HTTPResult httpResult = new HTTPResult();
                                httpResult.setTaskname(taskNum);
                                httpResult.setChecktime(startTime);
                                httpResult.setInterfaceuri(httpResultDetail.getInterfaceuri());
                                httpResult.setSourceip(httpResultDetail.getSourceip());
                                httpResult.setSourceport(httpResultDetail.getSourceport());
                                httpResult.setDesip(httpResultDetail.getDesip());
                                httpResult.setDesport(httpResultDetail.getDesport());
                                httpResult.setProtocol(Const.HTTP_SIGN);
                                httpResult.setRisk(httpResultDetailNew.getRisk());
                                httpResult.setRiskcount("1");
                                httpResult.setTotalcount(totalcount);
                                outlineMap.put(riskKey, httpResult);
                            }
                        }
                    }

                }
            }
            logger.info("保存接口流量检测概要统计完成！");
            //保存详情结果
            if (detailMap != null) {
                Collection<HTTPResultDetail> httpResultDetails = detailMap.values();

                for (HTTPResultDetail httpResultDetail : httpResultDetails) {
                    pcapService.saveHttpResultDetail(httpResultDetail);
                }
                logger.info("保存接口流量检测详情统计完成！");
            }

            if (outlineMap != null) {
                Collection<HTTPResult> httpResults = outlineMap.values();
                for (HTTPResult httpResult : httpResults) {
                    pcapService.saveHttpResult(httpResult);
                }
            }

        } else {
            logger.info("接口流量检测统计结果为空！");
        }
    }

    /**
     * 处理HTTP流量JSON数据
     *
     * <AUTHOR>
     * @date 2020/12/11
     */
    private static void httpDataClean(String data, String date) {
        boolean flag = false;
        HTTPResultDetail httpResultDetail = new HTTPResultDetail();
        // String afterPath = cleanAfterPath + File.separator + Const.HTTP_SIGN + File.separator
        //      + Const.HTTP_SIGN+"_clean_"+date+".txt";

        // packetbeat存储时间格式
        String format = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
        /*JSONObject joInfoReq = new JSONObject(true);
        JSONObject joInfoRes = new JSONObject(true);
        JSONObject joType = new JSONObject(true);*/
        JSONObject httpDataJson = JSONObject.parseObject(data);

        if (httpDataJson != null) {
            String url = httpDataJson.getString("url");// URL
            String source = httpDataJson.getString("source"); //源
            String destination = httpDataJson.getString("destination"); //目的
            String event = httpDataJson.getString("event"); //事件
            String http = httpDataJson.getString("http"); //HTTP
            String network = httpDataJson.getString("network"); //网络
            String resstatus = httpDataJson.getString("status"); //状态
            String reqcontent = httpDataJson.getString("request");// 请求文本
            String rescontent = httpDataJson.getString("response");// 响应文本

            JSONObject urlJson = JSONObject.parseObject(url);
            JSONObject sourceJson = JSONObject.parseObject(source);
            JSONObject destinationJson = JSONObject.parseObject(destination);
            JSONObject eventJson = JSONObject.parseObject(event);
            JSONObject httpJson = JSONObject.parseObject(http);
            JSONObject networkJson = JSONObject.parseObject(network);

            String interfaceuri = urlJson != null ? urlJson.getString("path") : ""; // URI
            String sourceip = sourceJson != null ? sourceJson.getString("ip") : ""; //源IP
            String sourceport = sourceJson != null ? sourceJson.getString("port") : ""; //源端口
            String desip = destinationJson != null ? destinationJson.getString("ip") : ""; //目的IP
            String desport = destinationJson != null ? destinationJson.getString("port") : ""; //目的端口
            String startTime = eventJson != null ? eventJson.getString("start") : ""; //开始时间
            String endTime = eventJson != null ? eventJson.getString("end") : ""; //响应时间
            String protocol = networkJson != null ? networkJson.getString("protocol") : ""; //协议

            String request = httpJson != null ? httpJson.getString("request") : "";
            String response = httpJson != null ? httpJson.getString("response") : "";
            JSONObject requestJson = JSONObject.parseObject(request);
            JSONObject responseJson = JSONObject.parseObject(response);
            String reqmethod = requestJson != null ? requestJson.getString("method").toUpperCase() : ""; //请求方法
            String resstatuscode = responseJson != null ? responseJson.getString("status_code") : ""; //响应状态码
            String body = responseJson != null ? responseJson.getString("body") : "";
            JSONObject bodyJson = JSONObject.parseObject(body);
            String resContentDetail = bodyJson != null ? bodyJson.getString("content") : ""; //响应详细数据
            String headers = responseJson != null ? responseJson.getString("headers") : "";
            JSONObject headersJson = JSONObject.parseObject(headers);
            String contentType = headersJson != null ? headersJson.getString("content-type") : "";

            String[] dataArr = contentType.split(";");
            for (String s : dataArr) {
                // 判断响应头
                if (ParamUtil.checkContentTypeIsTxt(s)) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                /* 输出到本地清洗后文件
                // 请求
                joInfoReq.put("interfaceuri",interfaceuri);
                joInfoReq.put("sourceip",sourceip);
                joInfoReq.put("sourceport",sourceport);
                joInfoReq.put("desip",desip);
                joInfoReq.put("desport",desport);
                joInfoReq.put("reqmethod",reqmethod);
                long reqTime = 0;
                if (StringUtils.isNotEmpty(startTime)){
                    reqTime = TimeUtils.convertUTC2local(startTime, format);
                    joInfoReq.put("starttime",reqTime);
                }
                joInfoReq.put("reqcontent",reqcontent);
                //响应
                joInfoRes.put("interfaceuri",interfaceuri);
                joInfoRes.put("sourceip",desip);
                joInfoRes.put("sourceport",desport);
                joInfoRes.put("desip",sourceip);
                joInfoRes.put("desport",sourceport);
                long resTime = 0;
                if (StringUtils.isNotEmpty(startTime)){
                    resTime = TimeUtils.convertUTC2local(endTime, format);
                    joInfoRes.put("endtime",resTime);
                }
                joInfoRes.put("resstatuscode",resstatuscode);
                joInfoRes.put("resstatus",resstatus);
                joInfoRes.put("rescontent",rescontent);

                joType.put("req",joInfoReq);
                joType.put("res",joInfoRes);
                //输出到清洗之后文件
                append2File(joType.toJSONString(),afterPath);*/

                // httpResultDetail.setSeqnumber();
                httpResultDetail.setInterfaceuri(interfaceuri);
                httpResultDetail.setSourceip(sourceip);
                httpResultDetail.setSourceport(sourceport);
                httpResultDetail.setDesip(desip);
                httpResultDetail.setDesport(desport);
                httpResultDetail.setReqmethod(reqmethod);
                httpResultDetail.setStarttime(String.valueOf(TimeUtils.convertUTC2local(startTime, format)));
                httpResultDetail.setReqcontent(reqcontent);
                httpResultDetail.setEndtime(String.valueOf(TimeUtils.convertUTC2local(endTime, format)));
                httpResultDetail.setResstatuscode(resstatuscode);
                httpResultDetail.setRescontent(rescontent);
                httpResultDetail.setResstatus(resstatus);


                // reqKey = 客户端IP-客户端端口-服务端IP-服务端端口-接口URI
                String reqKey = httpResultDetail.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                        httpResultDetail.getDesip() + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getDesport() + Const.AUDIT_SPLIT_JOIN +
                        httpResultDetail.getInterfaceuri();

                String resultReqKey = reqKey + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getStarttime();


                String reqtime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResultDetail.getStarttime()));
                httpResultDetail.setStarttime(reqtime);
                String restime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResultDetail.getEndtime()));
                httpResultDetail.setEndtime(restime);

                // 统计接口调用次数
                interfaceInfoCount(reqKey);

                // 保存http结果
                resultMap.put(resultReqKey, httpResultDetail);
            }
        }
    }


    /*
     *@Decription 变量销毁
     *<AUTHOR>
     *@date 2020/7/10
     */
    public static void destruction() {
        countMap = new HashMap<>();
        resultMap = new HashMap<>();
        httpSensitivedataMap = new HashMap<>();
        ftpSensitivedataMap = new HashMap<>();
        resultTypeSet = new HashSet<>();
        sensitiveDataCountResultMap = new HashMap<>();
        resTypeMap = new HashMap<>();
        totalCountByMethodMap = new HashMap<>();
    }


    /**
     * 统计接口检测总数
     *
     * @param key
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private static void interfaceInfoCount(String key) {
        if (countMap.containsKey(key)) {
            countMap.put(key, countMap.get(key) + 1);
        } else {
            countMap.put(key, 1);
        }
    }


    /**
     * @param map:需要根据value排序的map
     * @Description：按照行为时间进行排序，方式采集时时间顺序错误导致审计出错
     * <AUTHOR>
     * @date 2019年12月9日 下午3:13:02
     */
    private static String mapSort(Map<String, Integer> map) {
        if (map == null || map.size() == 0) {
            return null;
        }
        ArrayList<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            // 定义一个比较器
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                Integer num1 = o1.getValue();
                Integer num2 = o2.getValue();
                return num2.compareTo(num1);
            }
        });
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<String, Integer> l : list) {
            jsonObject.put(l.getKey(), l.getValue());
        }
        return jsonObject.toString();
    }


    /**
     * 清洗数组中空字符串
     *
     * @param datas
     * <AUTHOR>
     * @date 2020-11-02
     */
    private static ArrayList<String> convertArrToList(String[] datas) {
        ArrayList<String> list = new ArrayList<>();
        for (String data : datas) {
            if (!data.equals("")) {
                list.add(data);
            }
        }
        return list;
    }


    /**
     * @Description:检测参数数据通过通用检测规则
     * <AUTHOR> by wangqi
     * @date 2020-09-03
     */
    private static List<Integer> checkSensitiveDataByUseRule(String sign, String strKey, List<String> list) {

        resultTypeSet = new HashSet<>();
        List<Integer> resultTypeList = null;
        if (Const.HTTP_SIGN.equals(sign)) {
            // 校验参数是否包含敏感数据
            resultTypeList = ProCommonRule.checkIsClearType(httpSensitivedataMap, sensitiveDataCountResultMap,
                    strKey, list.toArray(new String[list.size()]), resultTypeSet, resTypeMap);
        } else if (Const.FTP_SIGN.equals(sign)) {
            // 校验参数是否包含敏感数据
            resultTypeList = ProCommonRule.checkIsClearType(ftpSensitivedataMap, sensitiveDataCountResultMap,
                    strKey, list.toArray(new String[list.size()]), resultTypeSet, resTypeMap);
        }


        // 统计检测次数
        if (list.size() > 0) {
            if (null == totalCountByMethodMap) {
                totalCountByMethodMap = new HashMap<>();
                totalCountByMethodMap.put(strKey, list.size());
            } else {
                if (totalCountByMethodMap.containsKey(strKey)) {
                    totalCountByMethodMap.put(strKey, totalCountByMethodMap.get(strKey).intValue() + list.size());
                } else {
                    totalCountByMethodMap.put(strKey, list.size());
                }
            }
        }

        return resultTypeList;
    }


    /**
     * @Decription 追加流量内容到文件
     * <AUTHOR>
     * @date 2020-11-05
     */
    public static void append2File(String strLine, String path) {
        // 判断文件夹是否存在
        String[] paths = path.split(Matcher.quoteReplacement(File.separator));
        StringBuilder fullPath = new StringBuilder();
        for (int i = 0; i < paths.length; i++) {
            fullPath.append(paths[i]).append(File.separator);
            File file = new File(fullPath.toString());
            if (paths.length - 1 != i) {
                if (!file.exists()) {
                    file.mkdir();
                }
            }
        }
        FileOutputStream fos;
        try {
            fos = new FileOutputStream(path, true);
            fos.write((strLine + "\n").getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
            logger.info("清洗数据保存:" + path + "出现异常!");
        }
    }
}
