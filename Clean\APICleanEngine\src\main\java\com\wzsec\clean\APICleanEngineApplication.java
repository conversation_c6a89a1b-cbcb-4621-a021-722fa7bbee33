package com.wzsec.clean;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.BlockingDeque;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@EnableKafka
@EnableAsync
@EnableScheduling
@SpringBootApplication
@EnableEncryptableProperties
public class APICleanEngineApplication {

    public static void main(String[] args) {
        SpringApplication.run(APICleanEngineApplication.class, args);
    }


    @Bean("CleanThreadPool")
    public ThreadPoolExecutor threadPoolExecutor() {
        // 减少线程池大小和队列大小以降低内存压力
        BlockingDeque<Runnable> blockingDeque = new LinkedBlockingDeque<>(50);
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(2, 4, 60, TimeUnit.SECONDS, blockingDeque);
        // 设置拒绝策略，当队列满时直接在调用线程中执行
        poolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return poolExecutor;
    }

}
