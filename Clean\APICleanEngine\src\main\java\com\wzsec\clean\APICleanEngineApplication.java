package com.wzsec.clean;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.BlockingDeque;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@EnableKafka
@EnableAsync
@EnableScheduling
@SpringBootApplication
@EnableEncryptableProperties
public class APICleanEngineApplication {

    public static void main(String[] args) {
        SpringApplication.run(APICleanEngineApplication.class, args);
    }


    @Bean("CleanThreadPool")
    public ThreadPoolExecutor threadPoolExecutor() {
        BlockingDeque<Runnable> blockingDeque = new LinkedBlockingDeque<>(100);
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, blockingDeque);
        return poolExecutor;
    }

}
