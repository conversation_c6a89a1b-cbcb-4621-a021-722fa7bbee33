package com.wzsec.clean.common.utils;

import org.apache.commons.io.FileUtils;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.*;

/**
 * Word and PPT 读取
 *
 * <AUTHOR>
 * @date 2019-06-17
 */
public class OfficeUtil {

    /**
     * 是否是2007及以上版本
     *
     * @param 文件地址
     * @return true 是 false 不是
     * @throws IOException
     * @description 通过检测文件头判断是2007及以上返回true 不是返回false
     * <AUTHOR>
     * @date 2019-06-17
     */
    public static boolean isOffice2007(String filePath) throws IOException {
        boolean b = false;
        InputStream fis = null;
        fis = new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(filePath)));
        if (!fis.markSupported()) {
            fis = new PushbackInputStream(fis, 8);
        }
        // if (POIFSFileSystem.hasPOIFSHeader(fis)) {//2003及以下版本
        // return false;
        // }

        try {
            new XWPFDocument(fis);
            b = true;
        } catch (IOException ignored) {
        }
//        if (POIXMLDocument.hasOOXMLHeader(fis)) {// 2007及以上版本
//            b = true;
//        }
        return b;
    }

//    /**
//     * 读取PPT文本内容
//     *
//     * @param 文件地址
//     * @return
//     * @description 处理2003与2007两种版本PPT
//     * <AUTHOR>
//     * @date 2019-06-17
//     */
//    public static String getPPTStr(String filePath) {
//        StringBuilder result = new StringBuilder();
//        InputStream fis = null;
//        try {
//            fis = new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(filePath)));
//            if (isOffice2007(filePath)) {
//                // System.out.println("检测后，ppt版本为2007及以上");
//                XMLSlideShow slide = null;
//                slide = new XMLSlideShow(fis);
//                XSLFPowerPointExtractor extractor = new XSLFPowerPointExtractor(slide);
//                result.append(extractor.getText());
//            } else {
//                // System.out.println("检测后，ppt版本为2003及以下");
//                PowerPointExtractor ex = null;
//                ex = new PowerPointExtractor(fis);
//                result.append(ex.getText());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result.toString();
//    }

    /**
     * 读取word文本内容
     *
     * @return
     * @description 处理2003与2007两种版本word
     * <AUTHOR>
     * @date 2019-06-17
     */
    public static String getWordStr(String filePath) {
        String result = null;
        InputStream in = null;
        StringBuilder str = new StringBuilder();
        try {
            in = new FileInputStream(filePath);

            if (isOffice2007(filePath)) {// 2007及以上版本
                // System.out.println("检测后，word版本为2007及以上");
                XWPFDocument document = new XWPFDocument(in);
                XWPFWordExtractor extractor = new XWPFWordExtractor(document);
                result = extractor.getText();
                //List<XWPFParagraph> paragraphList = document.getParagraphs();
                //XWPFParagraph paragraph = paragraphList.get(7);
                //XmlObject object = paragraph.getCTP().getRArray(7);
                //XmlCursor cursor = object.newCursor();
                //System.out.println(cursor.getTextValue());
            } else { // 2003及以下版本
                // System.out.println("检测后，word版本为2003及以下");
                HWPFDocument document = new HWPFDocument(in);
                WordExtractor extractor = new WordExtractor(document);
//                String res = getWordStrNest(filePath);
                str.append(extractor.getText());
                result = str.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

//    /**
//     * 支持读取word2003版嵌套03或07版word、03或07版excel、03或07版ppt内容
//     *
//     * @param filePath 文件地址
//     * <AUTHOR>
//     * @date 2020-02-18
//     */
//    public static String getWordStrNest(String filePath) {
//        String result = null;
//        try {
//            InputStream fis = new FileInputStream(filePath);
//
//            POIFSFileSystem fileSystem = new POIFSFileSystem(fis);
//            // 首先，获取工作簿的提取器
//            POIOLE2TextExtractor oleTextExtractor = (POIOLE2TextExtractor) ExtractorFactory.createExtractor(fileSystem);
//            // 然后是任何嵌入式Excel，Word，PowerPoint的提取器列表
//            POITextExtractor[] embeddedExtractors = ExtractorFactory.getEmbededDocsTextExtractors(oleTextExtractor);
//            System.out.println(embeddedExtractors.length);
//            for (POITextExtractor textExtractor : embeddedExtractors) {
//                //判断是03版本或者07版本
//                if (textExtractor instanceof ExcelExtractor || textExtractor instanceof WordExtractor || textExtractor instanceof PowerPointExtractor) {
//                    // 如果嵌入式对象是03版Excel电子表格。
//                    if (textExtractor instanceof ExcelExtractor) {
//                        ExcelExtractor excelExtractor = (ExcelExtractor) textExtractor;
//                        result = excelExtractor.getText();
//                        System.out.println("excel文档内容" + excelExtractor.getText());
//                    }
//                    // 03版Word文档
//                    else if (textExtractor instanceof WordExtractor) {
//                        WordExtractor wordExtractor = (WordExtractor) textExtractor;
//                        String[] paragraphText = wordExtractor.getParagraphText();
//                        for (String paragraph : paragraphText) {
//                            result = paragraph;
//                            System.out.println(paragraph);
//                        }
//                    }
//                    // 03版PowerPoint演示文稿。
//                    else if (textExtractor instanceof PowerPointExtractor) {
//                        PowerPointExtractor powerPointExtractor = (PowerPointExtractor) textExtractor;
//                        result = powerPointExtractor.getText();
//                    }
//                } else {
//                    //07版本嵌套word
//                    if (textExtractor instanceof XWPFWordExtractor) {
//                        XWPFWordExtractor wordExtractor = (XWPFWordExtractor) textExtractor;
//                        String paragraphText = wordExtractor.getText();
//                        result = paragraphText;
//                    }//07版本嵌套ppt
//                    else if (textExtractor instanceof XSLFPowerPointExtractor) {
//                        XSLFPowerPointExtractor powerPointExtractor = (XSLFPowerPointExtractor) textExtractor;
//                        result = powerPointExtractor.getText();
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }
}
