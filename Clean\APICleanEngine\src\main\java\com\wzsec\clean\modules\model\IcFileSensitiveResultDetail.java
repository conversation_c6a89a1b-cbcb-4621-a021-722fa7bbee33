package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-11-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IcFileSensitiveResultDetail implements Serializable {


    private Integer id;

    /**
     * 接口编码
     */
    private String apicode;

    /**
     * 接口名称
     */
    private String apiname;

    /**
     * 接口url
     */
    private String url;

    /**
     * 客户端IP
     */
    private String clientip;

    /**
     * 服务端IP
     */
    private String serverip;

    /**
     * 服务端端口
     */
    private String serverport;

    /**
     * 文件名称
     */
    private String filename;

    /**
     * 文件大小
     */
    private String filesize;

    /**
     * 行为类型
     */
    private String behaviortype;

    /**
     * 检测总次数
     */
    private Long totalcount;

    /**
     * 敏感数据类型
     */
    private String resulttype;

    /**
     * 敏感数据数量
     */
    private String sensitivenum;

    /**
     * 比例
     */
    private String ratio;

    /**
     * 敏感数据样例
     */
    private String example;

    /**
     * 检测时间
     */
    private String checktime;

    /**
     * 任务标识
     */
    private String taskname;

    /**
     * 风险程度
     */
    private String risk;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;

    /**
     * 备用字段5
     */
    private String sparefield5;

}