#!/bin/bash
#2023-03-02
#备注：-eq //equals等于; -ne //no equals不等于
#接口审计系统运行状况检查脚本
#如果脚本执行报错,使用vim首先查看文件格式并修改: set ff=unix

#1.检查mysql服务是否正常,如果未启动则进行启动
mysqlprocount=`ps -ef|grep mysql|grep -v grep|wc -l`
if [ $mysqlprocount -ne 2 ]
then
  echo "MySQL IS Not Running."
else
  echo "MySQL Is Running."
fi

#2.检查redis服务是否正常,如果未启动则进行启动
redisprocount=`ps -ef|grep redis|grep -v grep|wc -l`
if [ $redisprocount -ne 1 ]
then
 echo "Redis IS Not Running."
else
  echo "Redis Is Running."
fi

#3.检查Nginx服务是否正常,如果未启动则进行启动  -ne 不大于
nginxprocount=`ps -ef|grep nginx|grep -v grep|wc -l`
if [ $nginxprocount -ne 2 ]
then
  echo "Nginx IS Not Running."
else
  echo "Nginx IS  Running."
fi

#4.检查ds_bdas_system服务是否正常
# 需改动地方: ds_bdas_system-2.4.1.jar , 以实际jar名调整
bdasSystemName=ds_bdas_system-2.4.1.jar
bdasSystemProNewCount=`ps -ef |grep "$bdasSystemName" |grep -v "grep" |wc -l`
if [ 1 -eq  $bdasSystemProNewCount ]
then
  echo "BDAS System Is Running."
else
  echo "BDAS System Is Not Running."
fi

#5.检查ds_bdas_engine服务是否正常
# 需改动地方: ds_bdas_engine-2.4.1.jar , 以实际jar名调整
bdasEngineName=ds_bdas_engine-2.4.1.jar
bdasEngineProNewCount=`ps -ef |grep "$bdasEngineName" |grep -v "grep" |wc -l`
if [ 1 -eq  $bdasEngineProNewCount ]
then
  echo "BDAS Engine Is Running."
else
  echo "BDAS Engine Is Not Running."
fi
