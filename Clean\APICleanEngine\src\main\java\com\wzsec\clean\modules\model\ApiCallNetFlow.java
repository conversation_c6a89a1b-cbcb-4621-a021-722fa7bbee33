package com.wzsec.clean.modules.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * ES 参数实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiCallNetFlow {
    private String id;//id
    private String ak;//ak
    private String seqnum;//seqnum
    private String apicode;//接口编码
    private String system;//系统
    private String module;//模块
    private String apiuri;//接口路径
    private String apiip;//接口IP
    private String apiport;//应用程序
    private String clientip;//客户端IP
    private String clientport;//客户端端口
    private String clientIpRegion; // 客户端IP归属地
    private String clientmac;//客户端mac
    private String reqmethod;//请求方法
    private Params reqcontent;//请求内容
    private String repstatus;//请求状态
    private String resstatus;//响应状态
    private DataRescontent rescontent;//响应内容
    private String account;//账号
    private String datatype;//数据类型
    private String calltime;//请求时间
    private String cleantime;//清洗时间

    private Long ressize; // TODO 新增字段2024.05.16 获取响应数量(字符数)
    private String reqtimetag; // TODO 新增字段2024.05.16清洗时间(请求时间标识)
    private String akapicode;  // TODO ak-apiCode组合

    private String reqBody;  // TODO 请求体
    private String reqByte;  // TODO 请求字节
    private String resBody;  // TODO 响应体
    private String resByte;  // TODO 请求字节

    private String logid;//日志ID

    private EventDTO event; //事件

    @JSONField(name = "@timestamp")
    private Instant timestamp;

    @NoArgsConstructor
    @Data
    public static class EventDTO {
        private String category; //类别

        private String type; //类型

        private String kind; //event

        private Integer outcome; //结果

        private String created; //创建时间
    }


    // 中移在线新增
    private String apiname; //接口名称
    private String abilitymoduleid; //能力模块ID
    private String abilitymodulename; //能力模块名称
    private String csfcode; //CSF编码
    private String deptid; //组织机构ID
    private String deptname; //组织机构名称

}
