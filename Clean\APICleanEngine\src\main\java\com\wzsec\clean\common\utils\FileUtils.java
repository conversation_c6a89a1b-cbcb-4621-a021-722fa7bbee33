package com.wzsec.clean.common.utils;

import cn.hutool.core.lang.Console;
import org.apache.tools.tar.TarEntry;
import org.apache.tools.tar.TarInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.zip.GZIPInputStream;


/**
 * <AUTHOR>
 * @ClassName: FileUtils
 * @Description: 文件操作工具类
 * @date 2018年11月16日
 */
public class FileUtils {

    private final static Logger logger = LoggerFactory.getLogger(FileUtils.class);

    /**
     * @param sourceFile
     * @return String
     * @Description 判断编码格式方法
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static String getFileCharset(File sourceFile) {
        String charset = "GBK";
        byte[] first3Bytes = new byte[3];
        try {
            boolean checked = false;
            BufferedInputStream bis = new BufferedInputStream(new FileInputStream(sourceFile));
            bis.mark(0);
            int read = bis.read(first3Bytes, 0, 3);
            if (read == -1) {
                return charset; // 文件编码为 ANSI
            } else if (first3Bytes[0] == (byte) 0xFF && first3Bytes[1] == (byte) 0xFE) {
                charset = "UTF-16LE"; // 文件编码为 Unicode
                checked = true;
            } else if (first3Bytes[0] == (byte) 0xFE && first3Bytes[1] == (byte) 0xFF) {
                charset = "UTF-16BE"; // 文件编码为 Unicode big endian
                checked = true;
            } else if (first3Bytes[0] == (byte) 0xEF && first3Bytes[1] == (byte) 0xBB
                    && first3Bytes[2] == (byte) 0xBF) {
                charset = "UTF-8"; // 文件编码为 UTF-8
                checked = true;
            }
            bis.reset();
            if (!checked) {
                int loc = 0;
                while ((read = bis.read()) != -1) {
                    loc++;
                    if (read >= 0xF0)
                        break;
                    if (0x80 <= read && read <= 0xBF) // 单独出现BF以下的，也算是GBK
                        break;
                    if (0xC0 <= read && read <= 0xDF) {
                        read = bis.read();
                        if (0x80 <= read && read <= 0xBF) // 双字节 (0xC0 - 0xDF)
                            // (0x80
                            // - 0xBF),也可能在GB编码内
                            continue;
                        else
                            break;
                    } else if (0xE0 <= read && read <= 0xEF) {// 也有可能出错，但是几率较小
                        read = bis.read();
                        if (0x80 <= read && read <= 0xBF) {
                            read = bis.read();
                            if (0x80 <= read && read <= 0xBF) {
                                charset = "UTF-8";
                                break;
                            } else
                                break;
                        } else
                            break;
                    }
                }
            }
            bis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return charset;
    }

    /**
     * 读取资源路径下文件，优先读取外部文件
     *
     * @param filePath  文件路径
     * @param sparePath 备用路径
     * @throws IOException
     */
    public static InputStream getResourceAsStream(String filePath, String sparePath) throws IOException {
        // 将配置文件放在jar包外部，优先使用外部配置文件
        File object = new File(filePath);
        InputStream inputStream = null;
        String status = "外部";
        if (!object.exists()) {
            status = "内部";
            if (sparePath != null && !"".equals(sparePath)) {
                filePath = sparePath;
            }
            inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath);
        } else {
            inputStream = new FileInputStream(object);
        }
        logger.info("读取资源文件路径为：" + status + " 的 " + filePath);
        return inputStream;
    }

    /**
     * 删除目录（文件夹）目录下的文件
     *
     * @param sPath 被删除目录的文件路径
     * @return 目录删除成功返回true，否则返回false
     */
    public static boolean deleteDirectory(String sPath) {

        // 如果sPath不以文件分隔符结尾，自动添加文件分隔符
        if (!sPath.endsWith(File.separator)) {
            sPath = sPath + File.separator;
        }
        File dirFile = new File(sPath);
        // 如果dir对应的文件不存在，或者不是一个目录，则退出
        if (!dirFile.exists() || !dirFile.isDirectory()) {
            return false;
        }
        boolean flag = true;
        // 删除文件夹下的所有文件(包括子目录)
        File[] files = dirFile.listFiles();
        for (int i = 0; i < files.length; i++) {
            // 删除子文件
            if (files[i].isFile()) {
                flag = deleteFile(files[i].getAbsolutePath());
                if (!flag) {
                    break;
                }
            } // 删除子目录
            else {
                flag = deleteDirectory(files[i].getAbsolutePath());
                boolean success = (new File(files[i].getAbsolutePath())).delete();
                if (!flag) {
                    flag = false;
                    break;
                }
                if (!success) {
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 删除单个文件
     *
     * @param sPath 被删除文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String sPath) {
        boolean flag = false;
        try {
            File file = new File(sPath);
            // 路径为文件且不为空则进行删除
            if (file.isFile() && file.exists()) {
                file.delete();
                flag = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 根据路径删除指定的目录或文件，无论存在与否
     *
     * @param sPath 要删除的目录或文件
     * @return 删除成功返回 true，否则返回 false。
     */
    public static boolean DeleteFolder(String sPath) {
        boolean flag = false;
        File file = new File(sPath);
        // 判断目录或文件是否存在
        if (!file.exists()) { // 不存在返回 false
            return flag;
        } else {
            // 判断是否为文件
            if (file.isFile()) { // 为文件时调用删除文件方法
                return deleteFile(sPath);
            } else { // 为目录时调用删除目录方法
                return deleteDirectory(sPath);
            }
        }
    }

    /**
     * 按行读取txt文件内容,解析文件内容到list
     *
     * @param file
     */
    public static ArrayList<String> readtxt(File file) {
        ArrayList<String> arrayList = new ArrayList<String>();
        BufferedReader reader = null;
        String temp = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            while ((temp = reader.readLine()) != null) {
                for (String s : temp.split("。|，|,| |；|;|：|\t|-|=|:")) {
                    String a = s.trim();
                    if (!"".equals(a)) {
                        arrayList.add(a);
                    }
                }
            }
        } catch (Exception e) {
            logger.info(file.getName() + "：暂不支持处理");
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return arrayList;
    }

    /**
     * 按行读取txt文件内容,解析文件内容到str
     *
     * @param file
     */
    public static String getTxtStr(File file) {
        StringBuilder str = new StringBuilder();
        BufferedReader reader = null;
        String temp = null;
        try {
            int count = 0;
            reader = new BufferedReader(new FileReader(file));
            while ((temp = reader.readLine()) != null) {
                count++;
                if (count == 1000000) {
                    System.out.println("读取100W行数据");
                    count = 0;
                }
                str.append(temp + ",");
            }
        } catch (Exception e) {
            logger.info(file.getName() + "：暂不支持处理");
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return str.toString();
    }

    /**
     * 计算百分比
     */
    public static String getPercent(int x, int total) {
        if (x == 0 && total == 0) {
            return "0%";
        }
        // 创建一个数值格式化对象
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后0位
        numberFormat.setMaximumFractionDigits(2);
        String result = numberFormat.format((float) x / (float) total * 100);
        return result + "%";
    }

    /**
     * 字节数B转化为KB、MB、GB
     *
     * @param size
     * @return
     * <AUTHOR>
     */
    public static String getPrintSize(long size) {
        // 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (size < 1024) {
            return size + "B";
        } else {
            size = size / 1024;
        }
        // 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        // 因为还没有到达要使用另一个单位的时候
        // 接下去以此类推
        if (size < 1024) {
            return size + "KB";
        } else {
            size = size / 1024;
        }
        if (size < 1024) {
            // 因为如果以MB为单位的话，要保留最后1位小数，
            // 因此，把此数乘以100之后再取余
            size = size * 100;
            return size / 100 + "."
                    + size % 100 + "MB";
        } else {
            // 否则如果要以GB为单位的，先除于1024再作同样的处理
            size = size * 100 / 1024;
            return size / 100 + "."
                    + size % 100 + "GB";
        }
    }


    /**
     * 将Byte数组转换成文件
     *
     * @param bytes
     * @param filePath
     * @param fileName
     */
    public static void getFileByBytes(byte[] bytes, String filePath, String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            File dir = new File(filePath);
            if (!dir.exists()) {// 判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath + File.separator + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 将文件转换成Byte数组
     *
     * @param pathStr
     * @return
     */
    public static byte[] getBytesByFile(String pathStr) {
        File file = new File(pathStr);
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            byte[] data = bos.toByteArray();
            bos.close();
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解压tar.gz 文件
     *
     * @param file      要解压的tar.gz文件对象
     * @param outputDir 要解压到某个指定的目录下
     * @throws IOException
     */
    public static boolean unTarGz(File file, String outputDir) {
        boolean flag = true;
        TarInputStream tarIn = null;
        try {
            tarIn = new TarInputStream(new GZIPInputStream(new BufferedInputStream(new FileInputStream(file))),
                    1024 * 2);

            createDirectory(outputDir, null);// 创建输出目录

            TarEntry entry = null;
            while ((entry = tarIn.getNextEntry()) != null) {
                // 处理SaaS文件解压目录问题
                String fileName = entry.getName();
                if (entry.isDirectory()) {// 是目录
                    createDirectory(outputDir, fileName);// 创建空目录
                } else {// 是文件
                    File tmpFile = new File(outputDir + File.separator + fileName);
                    createDirectory(tmpFile.getParent() + File.separator, null);// 创建输出目录
                    OutputStream out = null;
                    try {
                        out = new FileOutputStream(tmpFile);
                        int length = 0;

                        byte[] b = new byte[2048];

                        while ((length = tarIn.read(b)) != -1) {
                            out.write(b, 0, length);
                        }

                    } catch (IOException ex) {
                        throw ex;
                    } finally {
                        if (out != null) {
                            out.close();
                        }
                    }
                }
            }
        } catch (IOException ex) {
            // TODO 部分 tar.gz文件 解压出现异常
            Console.log("解压文件出现异常: {} ,解压文件为:{} ,输出路径为:{}", ex.getMessage(), file, outputDir);
            // throw new IOException("解压归档文件出现异常", ex);
        } finally {
            try {
                if (tarIn != null) {
                    tarIn.close();
                }
            } catch (IOException ex) {
                Console.log("关闭tarFile出现异常: {}", ex.getMessage());
                // throw new IOException("关闭tarFile出现异常", ex);
            }
        }
        return true;
    }


    public static void createDirectory(String outputDir, String name) throws IOException {
        if (name != null) {
            outputDir = outputDir + File.separator + name;
        }
        File dir = new File(outputDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    /**
     * 获取指定路径下的所有文件路径
     *
     * @param fileList 文件列表
     * @param path     路径
     */
    public static void findFiles(List<File> fileList, String path) {
        File[] allFiles = new File(path).listFiles();
        for (int i = 0; i < allFiles.length; i++) {
            File file = allFiles[i];
            if (file.isFile()) {
                fileList.add(file);
            } else {
                findFiles(fileList, file.getAbsolutePath());
            }
        }
    }


    /**
     * 获取一个文件夹下的所有文件全路径和文件名
     *
     * @param path
     * @param fileNameMap
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    public static void getAllFileName(String path, Map<String, String> fileNameMap) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    fileNameMap.put(f.getAbsolutePath(), f.getName());
                }
            }
        }
    }

    /**
     * [中移在线]向指定文件增量写入字符串，每个字符串后添加回车符
     * 如果文件不存在，则先创建文件
     * 如果路径不存在，则创建路径
     *
     * @param filePath 文件路径
     * @param content  字符串内容
     * @return 是否成功写入
     */
    public static boolean writeToFile(String filePath, String content) {
        File file = new File(filePath);

        // 获取文件的父目录
        File parentDir = file.getParentFile();

        // 判断目录是否存在，如果不存在，则创建目录
        if (parentDir != null && !parentDir.exists()) {
            if (parentDir.mkdirs()) {
                System.out.println("目录创建成功: " + parentDir.getAbsolutePath());
            } else {
                System.out.println("目录创建失败: " + parentDir.getAbsolutePath());
                return false;  // 目录创建失败，返回 false
            }
        }

        // 判断文件是否存在，如果不存在，则创建文件
        if (!file.exists()) {
            try {
                if (file.createNewFile()) {
                    System.out.println("文件创建成功: " + filePath);
                } else {
                    System.out.println("文件创建失败: " + filePath);
                    return false;  // 文件创建失败，返回 false
                }
            } catch (IOException e) {
                e.printStackTrace();
                return false;  // 文件创建异常
            }
        }

        // 写入文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath, true))) {
            writer.write(content);
            writer.newLine();  // 添加回车符
            return true;  // 写入成功
        } catch (IOException e) {
            e.printStackTrace();
            return false;  // 写入失败
        }
    }
}
