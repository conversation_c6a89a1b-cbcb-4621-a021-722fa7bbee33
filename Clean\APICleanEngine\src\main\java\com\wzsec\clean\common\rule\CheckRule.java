package com.wzsec.clean.common.rule;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CheckRule {

    /***
     * @Description 正则表达式检测
     * @param rsContent
     * @return
     * @throws Exception
     */
    public static boolean checkingRule(String parameter, String regexps) {
        boolean paraValid = false;
        Pattern pattern = Pattern.compile(regexps);
        Matcher matcher = pattern.matcher(parameter.trim());
        //字符串是否与正则表达式相匹配
        if (matcher.matches()) {
            paraValid = true;
        }
        return paraValid;
    }

}
