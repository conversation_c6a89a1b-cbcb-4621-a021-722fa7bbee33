package com.wzsec.clean.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;


/**
 * <AUTHOR>
 * @ClassName: TimeUtils
 * @Description: TODO
 * @date 2018年11月23日
 */
public class TimeUtils {

    /**
     * 将时间转换为字符串(yyyy-MM-dd HH:mm)
     *
     * @param Date
     * @return String
     */
    public static String DateToStr(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String str = format.format(date);
        return str;
    }

    /**
     * 格式化日期
     *
     * @param Date
     * @return String
     */
    public static String DateFormatToStr(String formatstr, Date date) {
        SimpleDateFormat format = new SimpleDateFormat(formatstr);
        String str = format.format(date);
        return str;
    }

    /**
     * 将时间转换为字符串(yyyyMMddHHmm)
     *
     * @param Date
     * @return String
     */
    public static String DateToStr1(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String str = format.format(date);
        return str;
    }

    /**
     * 获取当前时间
     */
    public static String getReqTime() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date());
    }

    /**
     * 根据时间戳获取格式化时间
     *
     * @param Timestamp
     */
    public static String TimestampToDateStr(long Timestamp) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date(Timestamp));
    }

    public static void main(String[] args) {
        Date date = TimeUtils.stampForDate(1648088889);
        String date1 = TimeUtils.dateForString(date);
        System.out.println(date1);
    }

    /**
     * （int）时间戳转Date
     *
     * @param timestamp
     * @return
     */
    public static Date stampForDate(Integer timestamp) {
        return new Date((long) timestamp * 1000);
    }

    /**
     * date转String
     *
     * @param date
     * @return
     */
    public static String dateForString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//时间的格式
        return sdf.format(date);
    }


    /**
     * 通过日历类的Calendar.add方法第二个参数-1达到前一天的月份的效果
     *
     * @return
     */
    public static String getYesterdayByCalendar(String dateformat, int Intervalday) {
        Calendar calendar = Calendar.getInstance();
//		System.out.println(Calendar.DATE);
        calendar.add(Calendar.DATE, -Intervalday);
        Date time = calendar.getTime();
        String yesterday = new SimpleDateFormat(dateformat).format(time);
        return yesterday;
    }

    /**
     * 将时间和要追加的日期转换为字符串，例如 data是 2020-01-30，number 为 1 后返回的是 2020-02-01
     * 天数可以为负数，例如 data 是 2020-01-01，number 为 -1 后返回的是 2019-12-31
     *
     * @param date
     * @param number 要加的天数
     * @return String
     */
    public static String getNextDay(Date date, int number) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -number);
        String str = format.format(calendar.getTime());
        return str;
    }

    /**
     * 获取根据日期和天数推算的月份
     * - 如果日期为某个月的第一天，返回上个月的月份。
     * - 根据number天数的偏移来推算。
     *
     * @param date   日期
     * @param number 天数偏移量
     * @return {@link String} 返回月份，格式为yyyyMM
     */
    public static String getNextMonth(Date date, int number) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy_MM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 计算偏移后的日期
        calendar.add(Calendar.DAY_OF_MONTH, number);

        // 如果偏移后的日期是某个月的第一天，则返回上个月
        if (calendar.get(Calendar.DAY_OF_MONTH) == 1) {
            calendar.add(Calendar.MONTH, -1); // 调整为上个月
        }

        return format.format(calendar.getTime());
    }

    /**
     * 用当天的日期的long型字符数串减去昨天日期long型字符数串 其中： 86400000L，它的意思是说1天的时间=24小时 x 60分钟 x
     * 60秒 x 1000毫秒 单位是L。
     *
     * @return
     */
    public static String getYesterdayByDate() {
        // 实例化当天的日期
        Date today = new Date();
        // 用当天的日期减去昨天的日期
        Date yesterdayDate = new Date(today.getTime() - 86400000L);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(yesterdayDate);
        return yesterday;
    }

    /**
     * 获取当前时间
     */
    public static String getNowTime() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date());
    }

    /**
     * @Description:将当前时间转换为固定格式
     * <AUTHOR> by xiongpf
     * @date 2018-01-04
     */
    public static String getNowTimeStrByFormat(String dateFormat) {
        Date now = new Date();
        SimpleDateFormat dateFormatVal = new SimpleDateFormat(dateFormat);
        String nowtime = dateFormatVal.format(now);
        return nowtime;
    }

    /**
     * @Description:判断字符串是否是时间yyyyMMddHHmmss
     * <AUTHOR> by houzs
     * @date 2019-07-30
     */
    public static boolean checkIsTimeByData(String data) {
        if (null != data && data.length() == 14 && data.matches("^\\d{14}$")) {
            int year = Integer.parseInt(data.substring(0, 4));
            if (year > 1800 && year < 3000) {
                int month = Integer.parseInt(data.substring(4, 6));
                int day = Integer.parseInt(data.substring(6, 8));
                if (month > 0 && month < 13 && day > 0 && day < 61) {
                    int hour = Integer.parseInt(data.substring(8, 10));
                    int min = Integer.parseInt(data.substring(10, 12));
                    int second = Integer.parseInt(data.substring(12, 14));
                    return hour < 61 && min < 61 && second < 61;
                }
            }
        }
        return false;
    }

    /**
     * @Description:获取两个时间之差的秒数
     * <AUTHOR> by xiongpf
     * @date 2018-01-10
     */
    public static int getTimeSecondsByBothDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startDate = null;
        Date endDate = null;
        long totalMilliSeconds = 0;
        try {
            startDate = sdf.parse(startTime);
            endDate = sdf.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (startDate != null && endDate != null) {
            // 得到两个日期对象的总毫秒数
            long startDateMilliSeconds = startDate.getTime();
            long endDateMilliSeconds = endDate.getTime();
            totalMilliSeconds = endDateMilliSeconds - startDateMilliSeconds;
        }
        return (int) (totalMilliSeconds / 1000);
    }


    /**
     * 根据时间戳获取格式化时间
     *
     * @param Timestamp
     */
    public static String TimestampToDateStrNew(long Timestamp) {
        int length = String.valueOf(Timestamp).length();
        if (length == 10) {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return df.format(new Date(Timestamp * 1000));
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return df.format(new Date(Timestamp));
        }
    }


    /**
     * 将UTC时间转北京时间 返回毫秒值
     *
     * @param time
     * @param format
     * @return
     */
    public static long convertUTC2local(String time, String format) {

        Date date = null;
        Calendar calendar = null;
        long timeInMillis = 0;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            date = simpleDateFormat.parse(time);
            calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
            timeInMillis = calendar.getTimeInMillis();
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return timeInMillis;
    }

    /**
     * 将时间字符串转成格式化时间字符串
     *
     * @param strDate    时间字符串
     * @param sourFormat 原时间字符串格式
     * @param desFormat  目的时间字符串格式
     * @return
     */
    public static String convertStrDateToStr(String strDate, String sourFormat, String desFormat) {
        Date date = null;
        String formatStr = "";
        try {
            SimpleDateFormat sourDateFormat = new SimpleDateFormat(sourFormat);
            date = sourDateFormat.parse(strDate);
            SimpleDateFormat desDateFormat = new SimpleDateFormat(desFormat);
            formatStr = desDateFormat.format(date);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return formatStr;
    }

    /**
     * 时间戳转字符串
     *
     * @return {@link String}
     */
    public static String timestampToStr(long timestamp) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
        String str = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return str;
    }
}
