package com.wzsec.clean.filter.clean.HainanBigData;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.filter.parser.GeneralClean;
import com.wzsec.clean.filter.parser.PcapTrafficAnalysis;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.wzsec.clean.modules.model.PcapFlowCombination;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.StringReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求头处理
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@Slf4j
public class HaiNanRequestHeaderProcess {

    private static final long REQUEST_RESPONSE_PACKET_RESTRICTION = 1024 * 1024 * 50; //请求响应包写入ES大小限制

    /**
     * 海南场景清洗
     *
     * @param pcapFlowCombination pcap流量组合图
     */
    public static ApiCallNetFlow haiNanSceneCleaning(PcapFlowCombination pcapFlowCombination) throws ParseException {

        String requestUrl = pcapFlowCombination.getRequest_url();  //请求URL
        String clientip = pcapFlowCombination.getClient_ip(); //客户端IP
        String apiip = pcapFlowCombination.getServer_ip();  //服务端IP
        String serverPort = pcapFlowCombination.getServer_port();  //客户端端口
        String clientPort = pcapFlowCombination.getClient_port();  //服务端端口
        String requestTime = pcapFlowCombination.getRequest_time(); //请求时间
        String requestData = pcapFlowCombination.getRequest_data() == null ? "" : pcapFlowCombination.getRequest_data(); //请求内容
        String responseData = pcapFlowCombination.getResponse_data() == null ? "" : pcapFlowCombination.getResponse_data();  //响应内容

        // 请求无值导致截取报错,该处置为空
        String resstatuscode = "";
        try {
            // 判断响应体大小，如果大于某个阈值，则直接返回(50MB 为最大限制)
            if (!(responseData.length() > REQUEST_RESPONSE_PACKET_RESTRICTION)) {
                int startIndex = responseData.indexOf(" ") + 1;
                int endIndex = responseData.indexOf(" ", startIndex);
                if (endIndex != -1) {
                    resstatuscode = responseData.substring(startIndex, endIndex);
                }
            }
        } catch (Exception e) {
            resstatuscode = "";
        }

        String reqmethod = "";
        try (BufferedReader reader = new BufferedReader(new StringReader(requestData))) {
            // 只读取第一行
            String firstLine = reader.readLine();
            if (firstLine != null) {
                // 使用空格分割第一行并获取请求方法
                String[] parts = firstLine.split(" ");
                if (parts.length > 0) {
                    reqmethod = parts[0];  // 请求方法通常在第一部分
                }
            }
        } catch (Exception e) {
            reqmethod = "";
        }

        String reqcontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            try {
                reqcontent = PcapTrafficAnalysis.decodeRequestBody(requestData);
            } catch (Exception e) {
                reqcontent = requestData;
            }
        }

        String rescontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            rescontent = PcapTrafficAnalysis.extractResponseBody(responseData);
        }

        Map<String, String> requestHeaderMap = parseHttpRequestHeader(requestData);

        String apicode = "";
        if (requestHeaderMap.containsKey("hnjhpt_sid") || requestHeaderMap.containsKey("_api_name")) {
            if (requestHeaderMap.containsKey("hnjhpt_sid")) {
                apicode = requestHeaderMap.get("hnjhpt_sid");
            } else {
                apicode = requestHeaderMap.get("_api_name");
            }
        }

        String ak = "";
        if (requestHeaderMap.containsKey("_api_access_key")) {
            ak = requestHeaderMap.get("_api_access_key");
        }

        // TODO 源IP取值
        if (requestHeaderMap.containsKey("X-Real-IP") || requestHeaderMap.containsKey("x-real-ip")) {
            if (requestHeaderMap.containsKey("X-Real-IP")) {
                clientip = requestHeaderMap.get("X-Real-IP");
            } else {
                clientip = requestHeaderMap.get("x-real-ip");
            }
        }

        // TODO 目标IP取值 (2024.11.7 提出按流量清洗取值)
        // if (requestHeaderMap.containsKey("x-forwarded-for") || requestHeaderMap.containsKey("X-Forwarded-For")) {
        //
        //     String XFF = "";
        //     if (requestHeaderMap.containsKey("x-forwarded-for")) {
        //         XFF = requestHeaderMap.get("x-forwarded-for").trim();
        //     } else {
        //         XFF = requestHeaderMap.get("X-Forwarded-For").trim();
        //     }
        //
        //     try {
        //         apiip = XFF.substring(0, XFF.indexOf(","));
        //     } catch (Exception e) {
        //         apiip = XFF;
        //     }
        //
        //     // TODO XXF定义不规范,部分第一个IP为客户端IP,部分第一个为代理服务器IP
        //     if (clientip.equals(apiip)) {
        //         try {
        //             // 查找最后一个逗号的位置
        //             int lastCommaIndex = XFF.lastIndexOf(",");
        //             if (lastCommaIndex != -1) {
        //                 // 截取逗号后的部分，获取最后一个 IP 地址
        //                 apiip = XFF.substring(lastCommaIndex + 1).trim();
        //             } else {
        //                 // 如果没有逗号，则直接使用整个字符串
        //                 apiip = XFF.trim();
        //             }
        //         } catch (Exception e) {
        //             apiip = XFF.trim(); // 在异常发生时使用原始 XFF
        //         }
        //     }
        // }

        String seqNum = ""; // x-request-id
        if (requestHeaderMap.containsKey("x-request-id")) {
            seqNum = requestHeaderMap.get("x-request-id");
        }

        //  TODO 获取响应数据
        String resstatus = "";
        String repstatus = "";
        if (resstatuscode.startsWith(Const.API_STATUSCODE_5PREFIX)) {
            resstatus = Const.SERVER_ERROR_STATUS;
            repstatus = Const.CLIENT_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_4PREFIX)) {
            resstatus = Const.CLIENT_ERROR_STATUS;
            repstatus = Const.SERVER_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_1PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_2PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_3PREFIX)) {
            resstatus = Const.SERVER_CORRECT_STATUS; //响应状态   服务端
            repstatus = Const.CLIENT_CORRECT_STATUS; //请求状态   客户端
        }

        String system = ConfigurationManager.getProperty("netflow.systemFullName").trim();

        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
        apiCallNetFlow.setAk(ak);  //AK
        apiCallNetFlow.setApicode(apicode);
        apiCallNetFlow.setApiuri(requestUrl); //请求URL
        apiCallNetFlow.setApiip(apiip); //服务器IP
        apiCallNetFlow.setApiport(clientPort); //服务端端口
        apiCallNetFlow.setClientip(clientip); //客户端IP
        apiCallNetFlow.setClientport(serverPort); //客户端端口
        apiCallNetFlow.setRepstatus(repstatus); //请求状态
        apiCallNetFlow.setResstatus(resstatus); //响应状态
        apiCallNetFlow.setReqmethod(reqmethod);  //请求方法(GET、POST)
        apiCallNetFlow.setSystem(system); //系统名称

        Params params = new Params();
        params.setParams(reqcontent);
        apiCallNetFlow.setReqcontent(params); //请求体数据

        DataRescontent dataRescontent = new DataRescontent();
        dataRescontent.setData(rescontent);
        apiCallNetFlow.setRescontent(dataRescontent); //响应体数据

        apiCallNetFlow.setDatatype("netflow");
        apiCallNetFlow.setCalltime(requestTime);
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime());

        // TODO 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
        ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
        eventDTO.setCategory("netflow");
        apiCallNetFlow.setEvent(eventDTO);

        // 新增字段 接口响应字符数
        apiCallNetFlow.setRessize((long) rescontent.length());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = formatter.parse(requestTime);
        int hour = date.getHours();
        apiCallNetFlow.setReqtimetag(String.valueOf(hour));

        apiCallNetFlow.setAkapicode(ak + "," + apicode + "," + system);

        return apiCallNetFlow;

    }


    /**
     * 解析http请求标头
     *
     * @param rawHeader 原始收割台
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> parseHttpRequestHeader(String rawHeader) {
        Map<String, String> paramMap = new HashMap<>();

        // 添加空值检查和长度限制
        if (rawHeader == null || rawHeader.isEmpty()) {
            return paramMap;
        }

        // 限制处理的数据大小，避免处理过大的请求头
        if (rawHeader.length() > 1024 * 1024) { // 1MB限制
            log.warn("请求头过大，跳过解析: {} bytes", rawHeader.length());
            return paramMap;
        }

        try {
            // 使用BufferedReader逐行读取，避免一次性分割大字符串
            try (BufferedReader reader = new BufferedReader(new StringReader(rawHeader))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 如果行中包含冒号，将其分割为键值对
                    if (line.contains(":")) {
                        int colonIndex = line.indexOf(':');
                        if (colonIndex > 0 && colonIndex < line.length() - 1) {
                            String key = line.substring(0, colonIndex).trim();
                            String value = line.substring(colonIndex + 1).trim();
                            paramMap.put(key, value);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析HTTP请求头时发生异常", e);
        }

        return paramMap;
    }

}
