package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IcInterfaceInfo {

    /**
     * id
     */
    private Long id;

    /**
     * 接口编码
     */
    private String apicode;

    /**
     * 接口名称
     */
    private String apiname;

    /**
     * 接口方法
     */
    private String apimethod;

    /**
     * 接口描述
     */
    private String apides;

    /**
     * 接口地址
     */
    private String url;

    /**
     * 接口入参
     */
    private String inputparams;

    /**
     * 接口入参含义
     */
    private String inparam_mean;

    /**
     * 请求示例
     */
    private String req_example;

    /**
     * 接口出参
     */
    private String outputparams;

    /**
     * 接口出参含义
     */
    private String outparam_mean;

    /**
     * 返回示例
     */
    private String return_example;

    /**
     * 失败返回示例
     */
    private String failreturn;

    /**
     * 返回数据格式(字典：json、xml、html)
     */
    private String data_format;

    /**
     * 接口重要程度
     */
    private String importance;

    /**
     * 接口类型（字典：HTTP、MQ、SOCKET、WEB SERVICE）
     */
    private String invoke_type;

    /**
     * 接口状态 （字典：0 异常 1 运行 2 停用）
     */
    private String interface_status;

    /**
     * 是否删除
     */
    private String deleted;

    /**
     * 行政区域（字典：省级、市级、县级）
     */
    private String area;

    /**
     * 接口对应的应用
     */
    private String app;

    /**
     * 统一编码
     */
    private String resourceId;

    /**
     * 数源单位（组织名称）
     */
    private String data_source;

    /**
     * 数源单位（组织id）
     */
    private String data_source_mozi_org_id;

    /**
     * 接口注册到共享平台上的具体部门(组织名称)
     */
    private String register_unit;

    /**
     * 接口注册到共享平台上的具体部门(组织id)
     */
    private String register_unit_mozi_org_id;

    /**
     * 联系人
     */
    private String link_man;

    /**
     * 联系方式(手机号)
     */
    private String link_tel;

    /**
     * 创建人
     */
    private String createuser;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * 更新人
     */
    private String updateuser;

    /**
     * 更新时间
     */
    private Timestamp updatetime;

    /**
     * 备注
     */
    private String note;

    /**
     * 单次最大输出量
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4(URI)
     */
    private String sparefield4;

    /**
     * 系统标识
     */
    private String logsign;

    /**
     * API IP
     */
    private String apiip;

    /**
     * API PORT
     */
    private String apiport;

    /*同步状态*/
    private String syncstate;

}
