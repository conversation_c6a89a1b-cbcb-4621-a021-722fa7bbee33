package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警处置实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IcAlarmDisposal {

    private Integer id;

    /**
     * 接口编码
     */
    private String apicode;

    /**
     * 接口名称
     */
    private String apiname;

    /**
     * 检测模型
     */
    private String detectionmodel;

    /**
     * 事件详情
     */
    private String circumstantiality;

    /**
     * 风险程度
     */
    private String risk;

    /**
     * 检测时间
     */
    private String checktime;

    /**
     * 处理状态
     */
    private String treatmentstate;

    /**
     * 备注
     */
    private String note;

    /**
     * 备用字段1
     */
    private String reservefield1;

    /**
     * 备用字段2
     */
    private String reservefield2;

    /**
     * 备用字段3
     */
    private String reservefield3;

    /**
     * 备用字段4
     */
    private String reservefield4;

    /**
     * 区县
     */
    private String area;

    /**
     * 部门
     */
    private String department;

    /**
     * 事件处置人员
     */
    private String incidenthandler;

    /**
     * 事件处置时间
     */
    private String eventhandlingtime;

    /**
     * 源IP
     */
    private String sourceip;

    /**
     * 源端口
     */
    private String sourceport;

    /**
     * 目标IP
     */
    private String destinationip;

    /**
     * 目标端口
     */
    private String destinationport;

    /**
     * 事件相关用户名
     */
    private String account;

    /**
     * 规则
     */
    private String eventrule;

    /**
     * 推送次数
     */
    private String pushnumber;

}
