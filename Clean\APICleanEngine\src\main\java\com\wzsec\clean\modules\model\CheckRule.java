package com.wzsec.clean.modules.model;

/**
 * <AUTHOR>
 * @ClassName: CheckRule
 * @Description: TODO
 * @date 2020年4月28日
 */
public class CheckRule {

    private int id;    //id
    private String erule;  //英文名
    private String crule;   //中文名
    private String expressionformat;  //表示形式
    private String rulestatus;//是否启用（启用1，禁用0）
    private String backupfield1;  //规则类型
    private String backupfield2;  //表示形式
    private String backupfield3;  //表示形式
    private String backupfield4;  //表示形式
    private String notes;   //备注

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getErule() {
        return erule;
    }

    public void setErule(String erule) {
        this.erule = erule;
    }

    public String getCrule() {
        return crule;
    }

    public void setCrule(String crule) {
        this.crule = crule;
    }

    public String getExpressionformat() {
        return expressionformat;
    }

    public void setExpressionformat(String expressionformat) {
        this.expressionformat = expressionformat;
    }


    public String getRulestatus() {
        return rulestatus;
    }

    public void setRulestatus(String rulestatus) {
        this.rulestatus = rulestatus;
    }

    public String getBackupfield1() {
        return backupfield1;
    }

    public void setBackupfield1(String backupfield1) {
        this.backupfield1 = backupfield1;
    }

    public String getBackupfield2() {
        return backupfield2;
    }

    public void setBackupfield2(String backupfield2) {
        this.backupfield2 = backupfield2;
    }

    public String getBackupfield3() {
        return backupfield3;
    }

    public void setBackupfield3(String backupfield3) {
        this.backupfield3 = backupfield3;
    }

    public String getBackupfield4() {
        return backupfield4;
    }

    public void setBackupfield4(String backupfield4) {
        this.backupfield4 = backupfield4;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Override
    public String toString() {
        return "CheckRule [id=" + id + ", erule=" + erule + ", crule=" + crule + ", expressionformat="
                + expressionformat + ", rulestatus=" + rulestatus + ", backupfield1=" + backupfield1 + ", backupfield2="
                + backupfield2 + ", backupfield3=" + backupfield3 + ", backupfield4=" + backupfield4 + ", notes="
                + notes + "]";
    }

}
