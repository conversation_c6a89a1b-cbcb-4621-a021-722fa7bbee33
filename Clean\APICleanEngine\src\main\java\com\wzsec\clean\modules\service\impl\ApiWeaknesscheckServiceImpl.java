package com.wzsec.clean.modules.service.impl;

import com.wzsec.clean.modules.dao.ApiWeaknesscheckDao;
import com.wzsec.clean.modules.model.ApiWeaknesscheck;
import com.wzsec.clean.modules.service.ApiWeaknesscheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 告警处置
 */
@Service
public class ApiWeaknesscheckServiceImpl implements ApiWeaknesscheckService {

    @Autowired
    private ApiWeaknesscheckDao apiWeaknesscheckDao;


    @Override
    public void saveWeaknesscheck(ApiWeaknesscheck apiWeaknesscheck) {
        apiWeaknesscheckDao.saveWeaknesscheck(apiWeaknesscheck);
    }
}
