package com.wzsec.clean.modules.service.impl;

import com.wzsec.clean.modules.dao.IcAlarmDisposalDao;
import com.wzsec.clean.modules.model.IcAlarmDisposal;
import com.wzsec.clean.modules.service.IcAlarmDisposalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 告警处置
 */
@Service
public class IcAlarmDisposalServiceImpl implements IcAlarmDisposalService {

    @Autowired
    private IcAlarmDisposalDao icAlarmDisposalDao;


    @Override
    public void saveResult(IcAlarmDisposal icAlarmDisposal) {
        icAlarmDisposalDao.saveResult(icAlarmDisposal);
    }
}
