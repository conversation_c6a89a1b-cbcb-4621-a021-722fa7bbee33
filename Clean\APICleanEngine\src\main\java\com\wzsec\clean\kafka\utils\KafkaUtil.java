package com.wzsec.clean.kafka.utils;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.DateUtils;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Description: kafka工具类
 * <AUTHOR>
 * @date 2020年12月3日
 */
public class KafkaUtil {

	private final static Logger log = LoggerFactory.getLogger(KafkaUtil.class);

	public static final AtomicBoolean isRunning = new AtomicBoolean(true);

	@Autowired
	private RestHighLevelClient client;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	private static KafkaUtil kafkaUtil;

	@PostConstruct
	public void init() {
		kafkaUtil = this;
		kafkaUtil.client = this.client;
		kafkaUtil.jdbcTemplate = this.jdbcTemplate;
	}

	/**
	 * 初始化kafka配置
	 *
	 * @return
	 */
	public static Properties initConfig() {
		Properties props = new Properties();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
				ConfigurationManager.getProperty("kafka.consumer.bootstrap-servers").trim());
		props.put(ConsumerConfig.GROUP_ID_CONFIG, ConfigurationManager.getProperty("kafka.consumer.group-id").trim());
		props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,
				ConfigurationManager.getProperty("kafka.consumer.enable.auto.commit").trim()); // 自动commit
		props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,
				ConfigurationManager.getProperty("kafka.consumer.auto.commit.interval.ms").trim()); // 定时commit的周期
		props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
				ConfigurationManager.getProperty("kafka.consumer.session.timeout.ms").trim()); // consumer活性超时时间
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
				ConfigurationManager.getProperty("kafka.consumer.key.deserializer").trim());
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
				ConfigurationManager.getProperty("kafka.consumer.value.deserializer").trim());
		return props;
	}

	/**
	 * 消费kafka消息
	 *
	 * @return
	 */
	public static void consumeKafkaMessage(String topic) {
		Properties props = initConfig();
		KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
		consumer.subscribe(Arrays.asList(topic));
		try {
			while (isRunning.get()) {
				ConsumerRecords<String, String> records = consumer.poll(5);
				if (records != null && records.count() > 0) {
					for (ConsumerRecord<String, String> record : records) {
						String message = record.value();
						log.info(" 开始从kafka中获取数据, 写入ES.");
						ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
						Map<String, String> fieldDataMap = JSON.parseObject(message, HashMap.class);
						String apiCode = fieldDataMap.get("apiCode") == null ? "" : fieldDataMap.get("apiCode");//接口编码
						String apiName = fieldDataMap.get("apiName") == null ? "" : fieldDataMap.get("apiName");//接口名称
						String componentHost = fieldDataMap.get("componentHost") == null ? "" : fieldDataMap.get("componentHost");//服务IP
						String componentPort = fieldDataMap.get("componentPort") == null ? "" : fieldDataMap.get("componentPort");//服务端口
						String consumerAppName = fieldDataMap.get("consumerAppName") == null ? "" : fieldDataMap.get("consumerAppName");//app系统名称
						String fromIp = fieldDataMap.get("fromIp") == null ? "" : fieldDataMap.get("fromIp");//请求ip
						String logId = fieldDataMap.get("logId") == null ? "" : fieldDataMap.get("logId");//日志ID
						String transId = fieldDataMap.get("transId") == null ? "" : fieldDataMap.get("transId");//账号信息
						String statusCode = fieldDataMap.get("statusCode") == null ? "" : fieldDataMap.get("statusCode");//状态
						String httpMethod = fieldDataMap.get("httpMethod") == null ? "" : fieldDataMap.get("httpMethod");//请求方法
						JSONObject json = JSONObject.parseObject(message);//四种消息列表(REC REQ RESP RET)
						JSONArray messageList = json.getJSONArray("messageList");
						if (messageList.size() > 0) {
							for (Object infoObj : messageList) {
								JSONObject infoObjList = JSONObject.parseObject(String.valueOf(infoObj));
								String body = infoObjList.getString("body");
								Integer time = infoObjList.getInteger("time");
								String type = infoObjList.getString("type");
								String url = infoObjList.getString("url");
								if (Const.API_type_REC.equals(type)){
									Params params = new Params();
									params.setParams(body);
									apiCallNetFlow.setReqcontent(params);//请求内容
									apiCallNetFlow.setApiuri(url);//url
									apiCallNetFlow.setCalltime(TimeUtils.dateForString(TimeUtils.stampForDate(time)));//请求时间
								}
								if (Const.API_type_RET.equals(type)){
									DataRescontent dataRescontent = new DataRescontent();
									dataRescontent.setData(body);
									apiCallNetFlow.setRescontent(dataRescontent);//响应内容
								}
							}
						}
						if (Const.API_STATUSCODE_0000.equals(statusCode)){
							apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS);//客户端
							apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
						} else {
							apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);//客户端
							apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
						}
						apiCallNetFlow.setApicode(apiCode);//接口编码
						apiCallNetFlow.setLogid(logId);//日志ID
						apiCallNetFlow.setReqmethod(httpMethod);//请求方法
						apiCallNetFlow.setApiip(componentHost);//服务ip
						apiCallNetFlow.setApiport(componentPort);//服务端口
						apiCallNetFlow.setClientip(fromIp);//请求ip
						apiCallNetFlow.setCleantime(TimeUtils.getReqTime());//清洗时间
						String data = JSON.toJSONString(apiCallNetFlow);

						//推送ES索引日期设置为前一天
						IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1)).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
						//写入es中
						kafkaUtil.client.index(request, RequestOptions.DEFAULT);
					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			consumer.close();
		}
	}

	public static void main(String[] args) {
		// String row =
		// "{\"accelSet\":{\"lat\":315,\"long_\":1202,\"vert\":120,\"yaw\":343},\"brakes\":{\"abs\":\"engaged\",\"auxBrakes\":\"off\",\"brakeBoost\":\"off\",\"brakePadel\":\"on\",\"scs\":\"engaged\",\"traction\":\"engaged\",\"wheelBrakes\":{\"bitSize\":5,\"bytes\":\"50\",\"leftFront\":true,\"leftRear\":false,\"rightFront\":true,\"rightRear\":false,\"unavailable\":false,\"unusedBits\":3}},\"heading\":2148,\"id\":\"4000743400980006\",\"msgCnt\":0,\"pos\":{\"elevation\":44426,\"lat\":314219765,\"long_\":1206352991},\"safetyExt\":{\"pathHistory\":{\"crumbData\":[{\"llvOffset\":{\"offsetLL\":{\"choiceID\":6,\"position_LatLon\":{\"lat\":1,\"lon\":2}}},\"speed\":8191,\"timeOffset\":108}]}},\"secMark\":6301,\"size\":{\"height\":2,\"length\":6,\"width\":3},\"speed\":6,\"transmission\":\"neutral\",\"vehicleClass\":{\"classification\":0}}";
		// Properties props = initConfig();
		// KafkaProducer<String, String> producer = new KafkaProducer<String,
		// String>(props);
		// for (int i = 0; i < 5000000; i++) {
		// producer.send(new ProducerRecord<String, String>("test-data",
		// "key111", row));
		// }

		 consumeKafkaMessage("test-data");
	}
}
