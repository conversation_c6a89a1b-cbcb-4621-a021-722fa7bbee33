package com.wzsec.clean.filter.parser;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.modules.dao.IcFileSensitiveResultDao;
import com.wzsec.clean.modules.dao.IcFileSensitiveResultDetailDao;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.IcAlarmDisposalService;
import com.wzsec.clean.common.rule.SensitiveDataDiscovery;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 接口流量文件识别
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
public class ApiFileRecognition {

    private static IcFileSensitiveResultDao icFileSensitiveResultMapper = SpringUtils.getApplicationContext().getBean(IcFileSensitiveResultDao.class);

    private static IcFileSensitiveResultDetailDao icFileSensitiveResultDetailMapper = SpringUtils.getApplicationContext().getBean(IcFileSensitiveResultDetailDao.class);

    private static IcAlarmDisposalService icAlarmDisposalService = SpringUtils.getApplicationContext().getBean(IcAlarmDisposalService.class);

    /**
     * 接口文本文件提取
     *
     * @param packet             流量包数据
     * @param flowCombinationMap 流量组合 <ack,抽取对象>
     */
    public static void interfaceTextFileExtraction(Packet packet,
                                                   Map<String, FlowCombination> flowCombinationMap) {

        // TODO 请求: 判断是不是以  GET|POST|PUT|DELETE  开头,记录 ack
        // TODO 响应中的seq对应请求中的seq 对应请求中的ack,如果响应报文不全需要组包,根据ask相同进行组包

        String content = packet.getContent();
        String ack_number = packet.getAck_number();
        String seq_number = packet.getSeq_number();

        if (content.contains(Const.HTTP_SIGN) && !content.contains("OPTIONS")) {
            // 判断请求头
            if (content.contains("Accept") && (content.contains("GET") || content.contains("POST") || content.contains("PUT"))) {
                FlowCombination flowCombination = new FlowCombination();
                flowCombination.setRequest_data(content); //请求体
                flowCombination.setRequest_bytes(packet.getContent_byte()); //请求体字节

                //获取客户端,服务端信息
                flowCombination.setClient_ip(packet.getSourceip()); //客户端IP
                flowCombination.setServer_ip(packet.getDesip()); //服务端IP
                flowCombination.setServer_port(packet.getDesport()); //服务端端口
                flowCombinationMap.put(ack_number, flowCombination);
            }
            // 判断响应头
            if (content.contains("Content-Type")) {
                // 请求对应响应更新到Map集合
                if (flowCombinationMap.containsKey(seq_number)) {
                    FlowCombination flowCombination = flowCombinationMap.get(seq_number);
                    flowCombination.setResponse_data(content); //响应体
                    flowCombination.setResponse_bytes(packet.getContent_byte()); //响应体字节
                    flowCombinationMap.remove(seq_number);
                    flowCombinationMap.put(ack_number, flowCombination);
                }
            }
        } else {
            //对不完整响应进行组装 下载
            FlowCombination flowCombination = flowCombinationMap.get(ack_number);
            if (flowCombinationMap.containsKey(ack_number)) {
                String response_data = flowCombination.getResponse_data();

                if (StringUtils.isNotBlank(response_data)) {
                    if (response_data.contains("Content-Type")) {
                        //如果包含key值,需要对value进行更新,同时将content_byte进行组装
                        byte[] bytes = combineBytes(flowCombination.getResponse_bytes(), packet.getContent_byte());
                        flowCombination.setResponse_bytes(bytes);
                        flowCombination.setResponse_data(flowCombination.getResponse_data() + content);
                        flowCombinationMap.put(ack_number, flowCombination);
                    }
                } else {
                    //对不完整响应进行组装 上传 ask对应
                    //如果包含key值,需要对value进行更新,同时将content_byte进行组装
                    byte[] bytes = combineBytes(flowCombination.getRequest_bytes(), packet.getContent_byte());
                    flowCombination.setRequest_bytes(bytes);
                    flowCombination.setRequest_data(flowCombination.getRequest_data() + content);
                    flowCombinationMap.put(ack_number, flowCombination);
                }

            }
        }

    }


    /**
     * 获取文本参数并将敏感数据检测结果入库
     *
     * @param flowCombinationMap 流量组合图
     */
    public static void getTextParameters(Map<String, PcapFlowCombination> pcapFlowCombinationMap, Map<String, FlowCombination> flowCombinationMap, Map<String, IcInterfaceInfo> icInterfaceInfoMap) throws UnsupportedEncodingException, NoSuchAlgorithmException {

        // 上述已对包进行组装,需要识别其中的关键信息
        for (String ack : pcapFlowCombinationMap.keySet()) {

            PcapFlowCombination pcapFlowCombination = pcapFlowCombinationMap.get(ack);
            FlowCombination flowCombination = new FlowCombination();
            String request_data = pcapFlowCombination.getRequest_data();
            String response_data = pcapFlowCombination.getResponse_data();
            byte[] request_bytes = pcapFlowCombination.getRequest_bytes();
            byte[] response_bytes = pcapFlowCombination.getResponse_bytes();

            flowCombination.setRequest_data(request_data);
            flowCombination.setResponse_data(response_data);
            flowCombination.setClient_ip(pcapFlowCombination.getClient_ip());
            flowCombination.setServer_ip(pcapFlowCombination.getServer_ip());
            flowCombination.setServer_port(pcapFlowCombination.getServer_port());

            // TODO 提取请求URL
            if (request_data.contains("GET") || request_data.contains("POST")) {
                String request_url = extractUrlFromRequest(request_data);
                flowCombination.setRequest_url(request_url);

                String api_code = MD5Util.encrypt(request_url);
                flowCombination.setApi_code(api_code);
                // 获取接口名称
                String api_name = "";
                if (icInterfaceInfoMap.containsKey(api_code)) {
                    api_name = icInterfaceInfoMap.get(api_code).getApiname();
                }
                flowCombination.setApi_name(api_name);

            }

            if (request_data.contains("Content-Type") && request_data.contains("boundary=----")) {

                flowCombination.setBehavior_type("上传");
                String requestBody = extractTextFromMultipart(request_bytes);
                //提取上传文件名
                String uploadFileName = obtainUploadFileName(request_data);
                flowCombination.setFile_name(uploadFileName);
                // 上传文本文件大小
                String fileSize = FileUtils.getPrintSize(requestBody.getBytes().length);
                flowCombination.setFile_size(fileSize);
                flowCombination.setRequest_body_data(requestBody);

                //TODO ----- 敏感数据检测 -----
                sensitiveDataDetectionAndStorage(flowCombination, requestBody);

                flowCombinationMap.put(ack, flowCombination);
                // Console.log("【上传文件文本内容】-->  \n{}", requestBody);
            }

            if (response_data.contains("Content-Disposition: attachment;")) {
                String responseBodyData = extractTextFromBody(response_bytes);
                if (StringUtils.isNotBlank(responseBodyData)) {
                    flowCombination.setBehavior_type("下载");
                    flowCombination.setResponse_body_data(responseBodyData);
                    //提取下载文件名
                    String downloadFileName = obtainDownloadFileName(response_data).trim();
                    flowCombination.setFile_name(downloadFileName);
                    // 下载文本文件大小
                    String fileSize = FileUtils.getPrintSize(responseBodyData.getBytes().length);
                    flowCombination.setFile_size(fileSize);

                    //TODO ----- 敏感数据检测 -----
                    sensitiveDataDetectionAndStorage(flowCombination, responseBodyData);

                    flowCombinationMap.put(ack, flowCombination);
                    Console.log("【下载文件文本内容】-->  \n{}", responseBodyData);
                }
            }
        }
    }

    /**
     * 敏感数据检测入库
     *
     * @param flowCombination 流量组合
     * @param data            主体
     */
    private static void sensitiveDataDetectionAndStorage(FlowCombination flowCombination, String data) throws UnsupportedEncodingException, NoSuchAlgorithmException {

        // TODO 唯一标识注入
        String timeUniqueIdentification = DateUtils.getDate("yyyyMMddHHmmssSSS");


        String[] fieldDisassembly = data.split("。|，|\\\\|\\t|\\\t|\t|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");
        // 使用Stream过滤掉空字符串
        List<String> dataToBeIdentifiedList = Arrays.stream(fieldDisassembly)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
        Map<String, List<String>> sensitiveDataMap = SensitiveDataDiscovery.sensitiveDataClassification(dataToBeIdentifiedList);

        String risk = SensitiveDataDiscovery.alarmLevelDefinition(sensitiveDataMap);  //告警级别

        int totalCount = dataToBeIdentifiedList.size();  //检测参数总数

        IcFileSensitiveResult icFileSensitiveResult = new IcFileSensitiveResult();
        icFileSensitiveResult.setApicode(flowCombination.getApi_code());
        icFileSensitiveResult.setApiname(flowCombination.getApi_name());
        icFileSensitiveResult.setUrl(flowCombination.getRequest_url());
        icFileSensitiveResult.setClientip(flowCombination.getClient_ip());
        icFileSensitiveResult.setServerip(flowCombination.getServer_ip());
        icFileSensitiveResult.setServerport(flowCombination.getServer_port());
        icFileSensitiveResult.setFilename(flowCombination.getFile_name());
        icFileSensitiveResult.setFilesize(flowCombination.getFile_size());
        icFileSensitiveResult.setBehaviortype(flowCombination.getBehavior_type());
        icFileSensitiveResult.setRisk(risk);
        icFileSensitiveResult.setChecktime(DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));
        icFileSensitiveResult.setTaskname(timeUniqueIdentification);
//        icFileSensitiveResult.setSparefield5(data);   //TODO 临时加入,将文件内容入库,便于查看检测结果
        icFileSensitiveResultMapper.saveResult(icFileSensitiveResult);


        // TODO 告警处置
        alarmHandlingAndPush(flowCombination, sensitiveDataMap, risk);


        for (String alarmType : sensitiveDataMap.keySet()) {
            if (sensitiveDataMap.get(alarmType).size() > 0) {

                Map<String, Integer> map = frequencyOfListElements(sensitiveDataMap.get(alarmType));
                String sensitiveDataPick = sensitiveDataPicking(map);

                String detailsRisk = Const.RISK_NOT;
                if (alarmType.equals("明文身份证号") || alarmType.equals("明文护照号") || alarmType.equals("明文军官证号码") ||
                        alarmType.equals("明文手机号") || alarmType.equals("明文车牌号") || alarmType.equals("明文港澳通行证") ||
                        alarmType.equals("明文银行卡号")) {
                    detailsRisk = Const.RISK_HIGH;
                } else if (alarmType.equals("明文地址") || alarmType.equals("明文姓名") || alarmType.equals("明文IP")) {
                    detailsRisk = Const.RISK_MIDDLE;
                } else if (alarmType.equals("正常")) {
                    detailsRisk = Const.RISK_NOT;
                }

                IcFileSensitiveResultDetail icFileSensitiveResultDetail = new IcFileSensitiveResultDetail();
                icFileSensitiveResultDetail.setApicode(flowCombination.getApi_code());
                icFileSensitiveResultDetail.setApiname(flowCombination.getApi_name());
                icFileSensitiveResultDetail.setUrl(flowCombination.getRequest_url());
                icFileSensitiveResultDetail.setClientip(flowCombination.getClient_ip());
                icFileSensitiveResultDetail.setServerip(flowCombination.getServer_ip());
                icFileSensitiveResultDetail.setServerport(flowCombination.getServer_port());
                icFileSensitiveResultDetail.setFilename(flowCombination.getFile_name());
                icFileSensitiveResultDetail.setFilesize(flowCombination.getFile_size());
                icFileSensitiveResultDetail.setBehaviortype(flowCombination.getBehavior_type());
                icFileSensitiveResultDetail.setTotalcount(Long.valueOf(totalCount));
                icFileSensitiveResultDetail.setResulttype(alarmType);
                icFileSensitiveResultDetail.setSensitivenum(String.valueOf(sensitiveDataMap.get(alarmType).size()));
                String obtainProportion = obtainProportion(sensitiveDataMap.get(alarmType).size(), totalCount);
                icFileSensitiveResultDetail.setRatio(obtainProportion);
                if (!detailsRisk.equals(Const.RISK_NOT)) {
                    icFileSensitiveResultDetail.setExample(sensitiveDataPick);
                }
                icFileSensitiveResultDetail.setChecktime(DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));
                icFileSensitiveResultDetail.setRisk(detailsRisk);
                icFileSensitiveResultDetail.setTaskname(timeUniqueIdentification);
                icFileSensitiveResultDetailMapper.saveResult(icFileSensitiveResultDetail);
            }
        }
    }


    public static void alarmHandlingAndPush(FlowCombination flowCombination, Map<String, List<String>> sensitiveDataMap, String risk) {
        // TODO 事件详情定义
        if (sensitiveDataMap.size() > 0 && !risk.equals(Const.RISK_NOT)) {

            String sensitiveContent = concatenateKeys(sensitiveDataMap, "正常");

            String str = "该接口:{}(接口名称:{}) {} 文件: {} 输出敏感内容: {} ";  // 接口文件敏感内容检测
            String eventDetails = StrUtil.format(str, flowCombination.getApi_code(), flowCombination.getApi_name() == null ? "" : flowCombination.getApi_name(),
                    flowCombination.getBehavior_type(), flowCombination.getFile_name(), sensitiveContent);

            IcAlarmDisposal icAlarmdisposal = new IcAlarmDisposal();
            icAlarmdisposal.setApicode(flowCombination.getApi_code()); //接口编码
            icAlarmdisposal.setApiname(flowCombination.getApi_name());  //接口名称
            icAlarmdisposal.setDetectionmodel(Const.DICT_API_FILE_SENSITIVE); //检测模型
            icAlarmdisposal.setCircumstantiality(eventDetails);
            icAlarmdisposal.setRisk(risk);  //风险程度
            icAlarmdisposal.setReservefield1(risk); //调整级别
            icAlarmdisposal.setChecktime(DateUtil.now());
            icAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);  //处置状态

            icAlarmdisposal.setSourceip(flowCombination.getClient_ip());//源ip
            icAlarmdisposal.setDestinationip(flowCombination.getServer_ip());//目标IP
            icAlarmdisposal.setDestinationport(flowCombination.getServer_port());// 目标端口
            icAlarmdisposal.setEventrule(Const.DICT_API_FILE_SENSITIVE_CHARS);// 接口文件敏感内容检测

            icAlarmdisposal.setReservefield2(flowCombination.getRequest_data() + "\r\n" + flowCombination.getResponse_data());//接口日志

            icAlarmDisposalService.saveResult(icAlarmdisposal); //保存接口鉴权到告警处置表

            //告警推送syslog
            new MonitorRiskAlarmData().sendIcExample(icAlarmdisposal);
        }

    }


    public static String concatenateKeys(Map<String, List<String>> dataMap, String excludedKey) {
        List<String> keysToConcatenate = new ArrayList<>();

        // 遍历Map的所有键
        for (String key : dataMap.keySet()) {
            // 排除特定的键
            if (!key.equals(excludedKey)) {
                if (dataMap.get(key).size() > 0) {
                    keysToConcatenate.add(key);
                }
            }
        }

        // 使用逗号将键拼接起来
        return String.join(",", keysToConcatenate);
    }


    /**
     * 统计list集合中每个参数出现的次数
     *
     * @param items 项目
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    public static Map<String, Integer> frequencyOfListElements(List<String> items) {
        if (items == null || items.size() == 0) {
            return null;
        }
        Map<String, Integer> map = new HashMap<String, Integer>();
        for (String temp : items) {
            Integer count = map.get(temp);
            map.put(temp, (count == null) ? 1 : count + 1);
        }
        return map;
    }

    /**
     * 敏感数据字段信息拾取(取值10条)
     *
     * @param sensitiveNumMap 敏感数据及数量
     * @return {@link String}
     */
    public static String sensitiveDataPicking(Map<String, Integer> sensitiveNumMap) {
        Map sensitiveMap = new HashMap<>();
        String sensitiveDataPick = "";
        //取十条写入数据库,低于10条全取
        if (sensitiveNumMap.size() < 10) {
            for (String s : sensitiveNumMap.keySet()) {
                sensitiveMap.put(s, sensitiveNumMap.get(s));
            }
            sensitiveDataPick = JSONUtil.toJsonStr(sensitiveMap);
        } else {
            int i = 0;
            for (String s : sensitiveNumMap.keySet()) {
                i++;
                sensitiveMap.put(s, sensitiveNumMap.get(s));
                if (i == 10) {
                    break;
                }
            }
            sensitiveDataPick = JSONUtil.toJsonStr(sensitiveMap);
        }
        return sensitiveDataPick;
    }


    /**
     * 获取参数占比
     *
     * @return {@link String}
     */
    public static String obtainProportion(int checkCount, int totalCount) {
        double rate = 100 * ((double) checkCount / totalCount);
        BigDecimal bDec = new BigDecimal(rate);
        double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        return String.valueOf(ratio);
    }


    /**
     * 获取上传文件名
     *
     * @param contentDisposition 内容处置
     * @return {@link String}
     */
    public static String obtainUploadFileName(String contentDisposition) {
        String pattern = "filename=\"([^\"]+)\"";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(contentDisposition);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }


    /**
     * 获取下载文件名
     *
     * @param content 内容处置
     * @return {@link String}
     */
    public static String obtainDownloadFileName(String content) {
        String fileName = null;

        if (content != null && content.contains("Content-Disposition")) {
            // Find the Content-Disposition header
            int startIndex = content.indexOf("Content-Disposition") + 20;
            int endIndex = content.indexOf("\n", startIndex);

            if (startIndex != -1 && endIndex != -1) {
                String contentDisposition = content.substring(startIndex, endIndex);

                if (contentDisposition.contains("filename=")) {
                    int filenameStartIndex = contentDisposition.indexOf("filename=") + 9; // 9 is the length of "filename="
                    int filenameEndIndex = contentDisposition.indexOf(";", filenameStartIndex);

                    if (filenameEndIndex == -1) {
                        fileName = contentDisposition.substring(filenameStartIndex);
                    } else {
                        fileName = contentDisposition.substring(filenameStartIndex, filenameEndIndex);
                    }

                    try {
                        fileName = URLDecoder.decode(fileName, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        return fileName;
    }

    /**
     * 从请求中提取url
     *
     * @param requestString 请求字符串
     * @return {@link String}
     */
    public static String extractUrlFromRequest(String requestString) {
        // Split the request string into lines
        String[] lines = requestString.split("\\r?\\n");

        // Find the line containing the request path
        for (String line : lines) {
            if (line.startsWith("GET") || line.startsWith("POST")) {
                // Extract the URL from the OPTIONS request
                String[] parts = line.split(" ");
                if (parts.length >= 2) {
                    return parts[1];
                }
            }
        }

        return null;
    }


    /**
     * 将两个字节数组组装成一个新的字节数组
     *
     * @param bytes1 字节1
     * @param bytes2 字节2
     * @return {@link byte[]}
     */
    private static byte[] combineBytes(byte[] bytes1, byte[] bytes2) {
        byte[] combinedBytes = new byte[bytes1.length + bytes2.length];
        System.arraycopy(bytes1, 0, combinedBytes, 0, bytes1.length);
        System.arraycopy(bytes2, 0, combinedBytes, bytes1.length, bytes2.length);
        return combinedBytes;
    }


    /**
     * 从上传流量提取文本
     *
     * @param byteArray 字节数组
     * @return {@link String}
     */
    public static String extractTextFromMultipart(byte[] byteArray) {
        try {
            // 将字节数组转换成字符串
            String content = new String(byteArray, "UTF-8");
            // 找到正文的起始位置
            int startIndex = content.indexOf("\r\n\r\n") + 4;

            // 截取响应主体的文本部分
            String requestBody = content.substring(startIndex);
            if (StringUtils.isNotBlank(requestBody)) {
                // 找到首行的结束位置（回车换行符的位置）
                int firstLineEnd = requestBody.indexOf("\r\n\r\n") + 4;
                // 找到正文的结束位置
                int lastLineStart = requestBody.lastIndexOf("\r\n------") - 2;
                // 截取首行、尾行以及它们之间的文本部分
                requestBody = requestBody.substring(firstLineEnd, lastLineStart);
            } else {
                requestBody = null;
            }
            return requestBody;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从下载流量中提取文本
     *
     * @param responseBytes 响应字节
     * @return {@link String}
     */
    public static String extractTextFromBody(byte[] responseBytes) {
        try {
            // 将字节数组转换成字符串
            String response = new String(responseBytes, "UTF-8");
            // 找到响应主体的起始位置
            int startIndex = response.indexOf("\r\n\r\n") + 4;
            // 截取响应主体的文本部分
            String responseBody = response.substring(startIndex);
            if (StringUtils.isNotBlank(responseBody)) {
                // 找到首行的结束位置（回车换行符的位置）
                int firstLineEnd = responseBody.indexOf("\r\n") + 2;

                // 找到尾行的起始位置
                int lastLineStart = responseBody.lastIndexOf("0") - 4;
                // 截取首行、尾行以及它们之间的文本部分
                responseBody = responseBody.substring(firstLineEnd, lastLineStart)
                        .replaceAll("\r\n2000", "")
                        .replaceAll("\r\na50", "");
            } else {
                responseBody = null;
            }

            return responseBody;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }


}
