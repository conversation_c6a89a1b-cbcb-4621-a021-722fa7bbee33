package com.wzsec.clean.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

/**
 * @ClassName: FileTools
 * @Description: 接口格式工具类
 */
@Slf4j
public class FileTools {

    // 输入限制
    private static final int MAX_INPUT_LENGTH = 10000;

    /**
     * 判断是否为HTML
     *
     * @param content HTML字符串
     * @return
     */
    public static boolean isHtml(String content) {
        // 检查输入内容是否为 null 或者长度超过 10000
        if (content == null || content.length() > MAX_INPUT_LENGTH) {
            return false; // 如果是，返回 false
        }
        try {
            // 使用 Jsoup 解析内容
            Document doc = Jsoup.parse(content);
            // 检查 body 中是否有子元素
            return !doc.body().children().isEmpty(); // 如果有子元素，认为是 HTML
        } catch (StackOverflowError e) {
            log.error("堆栈溢出: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("HTML判断失败: {}", e.getMessage());
            return false;
        }
    }


    /**
     * HTML转json
     *
     * @param content HTML字符串
     * @return
     */
    public static String isHtmlJsonTransfer(String content) {
        if (content == null) {
            return "{}"; // 返回空的JSON对象
        }
        try {
            Document doc = Jsoup.parse(content);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", doc.title());
            jsonObject.put("content", doc.body().text());
            return jsonObject.toString();
        } catch (Exception e) {
            log.error("HTML转JSON失败: {}", e.getMessage());
            return "{}"; // 返回空的JSON对象
        }
    }

    /**
     * 判断是否为XML
     *
     * @param content xml字符串
     * @return
     */
    public static boolean isXml(String content) {
        if (content == null || content.length() > MAX_INPUT_LENGTH) {
            return false;
        }
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            builder.parse(new InputSource(new StringReader(content)));
            return true;
        } catch (StackOverflowError e) {
            log.error("堆栈溢出: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("XML判断失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否是xml结构
     *
     * @param content xml字符串
     * @return
     */
    public static boolean isXML(String content) {
        if (content == null || content.length() > MAX_INPUT_LENGTH) {
            return false;
        }
        try {
            DocumentHelper.parseText(content);
            return true;
        } catch (StackOverflowError e) {
            log.error("堆栈溢出: {}", e.getMessage());
            return false;
        } catch (DocumentException e) {
            return false;
        }
    }

    /**
     * xml转json
     *
     * @param content xml字符串
     * @return
     */
    public static String isXmlJsonTransfer(String content) {
        if (content == null) {
            return "{}"; // 返回空的JSON对象
        }
        try {
            return XML.toJSONObject(content).toString();
        } catch (Exception e) {
            log.error("XML转JSON失败: {}", e.getMessage());
            return "{}"; // 返回空的JSON对象
        }
    }

    /**
     * 判断是否是Jsonp
     *
     * @param jsonp
     * @return
     */
    public static boolean isJsonp(String jsonp) {
        if (jsonp == null || jsonp.length() > MAX_INPUT_LENGTH) {
            return false;
        }
        try {
            String json = jsonp.substring(jsonp.indexOf("(") + 1, jsonp.lastIndexOf(")"));
            JSON.parse(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * jsonp转json
     *
     * @param jsonp xml字符串
     * @return
     */
    public static String isJsonpTransfer(String jsonp) {
        if (jsonp == null) {
            return null;
        }
        try {
            String json = jsonp.substring(jsonp.indexOf("(") + 1, jsonp.lastIndexOf(")"));
            return JSON.parse(json).toString();
        } catch (Exception e) {
            return jsonp; // 如果转换失败，返回原始字符串
        }
    }

    /**
     * 是否为有效的JSON
     *
     * @param json json
     * @return boolean
     */
    public static boolean isJsonValid(String json) {
        if (json == null || json.length() > MAX_INPUT_LENGTH) {
            return false;
        }
        try {
            new ObjectMapper().readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
