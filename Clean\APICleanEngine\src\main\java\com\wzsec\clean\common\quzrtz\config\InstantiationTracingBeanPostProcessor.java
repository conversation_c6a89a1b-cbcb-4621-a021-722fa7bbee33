package com.wzsec.clean.common.quzrtz.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
public class InstantiationTracingBeanPostProcessor implements ApplicationListener<ContextRefreshedEvent> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private QuartzManager quartzManager;

    // @Value("${scheduled.status}")
    // private String status;

    /**
     * 服务启动执行该方法，初始化待执行任务
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // if (Const.FLOW_CLEANING_ON.equals(status)) {
        //     if (event.getApplicationContext().getParent() == null) {
        //         quartzManager.init();
        //     }
        // } else {
        //     logger.info("流量检测任务状态未开启");
        // }
    }

}
