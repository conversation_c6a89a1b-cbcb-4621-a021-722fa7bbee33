package com.wzsec.clean.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 *
 * <AUTHOR>
 * @version 2016-4-15
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static String[] parsePatterns = {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss",
            "yyyy.MM.dd HH:mm", "yyyy.MM"};

    // 私有构造方法，防止实例化
    private DateUtils() {
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy.MM.dd）
     */
    public static String getDateToday() {
        return getDate("yyyy.MM.dd");
    }


    /**
     * 得到当前日期前后时间字符串 格式（yyyy.MM.dd）
     */
    public static String getBeforeOneDay(Date currentdate, int offSet) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentdate);
        calendar.add(Calendar.DAY_OF_MONTH, -offSet);
        currentdate = calendar.getTime();
        String dayTime = format.format(currentdate);
        return dayTime;
    }


    /**
     * @return int
     * @description: 查询时间间隔(传入时间格式yyyy.MM.dd)
     * @author: JOY
     * @date: 2022-03-02
     */
    public static long getTimeInterval(String startTime, String endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");// 自定义时间格式

        Calendar calendar_a = Calendar.getInstance();// 获取日历对象
        Calendar calendar_b = Calendar.getInstance();

        Date date_a = null;
        Date date_b = null;

        try {
            date_a = simpleDateFormat.parse(startTime);//字符串转Date
            date_b = simpleDateFormat.parse(endTime);
            calendar_a.setTime(date_a);// 设置日历
            calendar_b.setTime(date_b);
        } catch (ParseException e) {
            e.printStackTrace();//格式化异常
        }

        long time_a = calendar_a.getTimeInMillis();
        long time_b = calendar_b.getTimeInMillis();

        long between_days = (time_b - time_a) / (1000 * 3600 * 24);//计算相差天数

        return between_days;

    }


    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到日期时间字符串，转换格式（yyyyMM）获取月份
     */
    public static String formatDateTimeMonth(Date date) {
        return formatDate(date, "yyyyMM");
    }

    /**
     * 得到日期时间字符串，转换格式（yyyyMMdd）
     */
    public static String formatDateTimeDay(Date date) {
        return formatDate(date, "yyyyMMdd");
    }

    /**
     * 得到当前时间字符串 格式（HH:mm:ss）
     */
    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    /**
     * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
     *
     * @return
     */
    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前年份字符串 格式（yyyy）
     */
    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    /**
     * 得到当前月份字符串 格式（MM）
     */
    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    /**
     * 得到当天字符串 格式（dd）
     */
    public static String getDay() {
        return formatDate(new Date(), "dd");
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    /**
     * 日期型字符串转化为日期 格式 { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
     * "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy.MM.dd",
     * "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * @Description: 转换为指定格式时间（注意数据库中时间格式）
     * <AUTHOR>
     * @date 2018年4月16日
     */
    public static Date parseFormatDate(String strDate) {
        if (strDate == null) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            return sdf.parse(strDate);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * @Description: 转换为指定格式时间（yyyy-MM-dd）
     * <AUTHOR>
     * @date 2018年4月16日
     */
    public static Date parseFormatDateToDay(String strDate) {
        if (strDate == null) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(strDate);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取过去的天数
     *
     * @param date
     * @return
     */
    public static long pastDays(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取过去的小时
     *
     * @param date
     * @return
     */
    public static long pastHour(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (60 * 60 * 1000);
    }

    /**
     * 获取过去的分钟
     *
     * @param date
     * @return
     */
    public static long pastMinutes(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (60 * 1000);
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     *
     * @param timeMillis
     * @return
     */
    public static String formatDateTime(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
    }

    /**
     * 获取两个日期之间的天数
     *
     * @param before
     * @param after
     * @return
     */
    public static double getDistanceOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
    }

    // /**
    // * @param args
    // * @throws ParseException
    // */
    // public static void main(String[] args) throws ParseException {
    // // System.out.println(formatDate(parseDate("2010/3/6")));
    // // System.out.println(getDate("yyyy年MM月dd日 E"));
    // // long time = new Date().getTime()-parseDate("2012-11-19").getTime();
    // // System.out.println(time/(24*60*60*1000));
    //
    // System.out.println(getDate("yyyy-MM-dd HH:mm"));
    // System.out.println(parseFormatDate("2018-04-17 17:24:17"));
    // }

    /**
     * @Description:将当前时间转换为字符串yyyyMMddHHmmss
     * <AUTHOR> by xiongpf
     * @date 2018-01-04
     */
    public static String getNowTimeString(String dateFormat) {
        Date now = new Date();
        SimpleDateFormat dateFormatVal = new SimpleDateFormat(dateFormat);
        String nowtime = dateFormatVal.format(now);
        return nowtime;
    }


    /**
     * 获取今日往前N天日期的
     *
     * @param dateformat  dateformat
     * @param Intervalday intervalday
     * @return {@code String}
     */
    public static String getYesterdayByCalendar(String dateformat, int Intervalday) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -Intervalday);
        Date time = calendar.getTime();
        String yesterday = new SimpleDateFormat(dateformat).format(time);
        return yesterday;
    }

    /**
     * 获取两个时间中的每一天
     *
     * @param bigtimeStr 开始时间 yyyy-MM-dd
     * @param endTimeStr 结束时间 yyyy-MM-dd
     * @return
     * @throws ParseException
     * <AUTHOR>
     */
    public static List<String> getDays(String bigtimeStr, String endTimeStr, String returnFormat)
            throws ParseException {
        Date bigtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(bigtimeStr + " 00:00:00");
        Date endtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endTimeStr + " 00:00:00");
        // 定义一个接受时间的集合
        List<Date> lDate = new ArrayList<>();
        lDate.add(bigtime);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(bigtime);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(endtime);
        // 测试此日期是否在指定日期之后
        while (endtime.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(calBegin.getTime());
        }
        List<String> datas = new LinkedList<>();
        for (Date date : lDate) {
            datas.add(new SimpleDateFormat(returnFormat).format(date));
        }
        return datas;
    }

    /**
     * 获取前30天的时间值
     *
     * <AUTHOR>
     * @date 2020-03-13
     */
    // 注意day 必须是long类型 否者会超精度影响结果
    public static Date frontDate(Date date, long day) {
        long time = date.getTime(); // 得到指定日期的毫秒数
        day = day * 24 * 60 * 60 * 1000; // 要加上的天数转换成毫秒数
        time -= day; // 相减得到新的毫秒数
        return new Date(time); // 将毫秒数转换成日期
    }

    /**
     * 将时间转换为字符串(yyyyMMddHHmm)
     *
     * @return String
     */
    public static String DateToStr1(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String str = format.format(date);
        return str;
    }

    // 获取这一年的日历
    private static Calendar getCalendarFormYear(int year) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.YEAR, year);
        return cal;
    }

    // 获取某一年的某一周的周一日期
    public static String getStartDayOfWeekNo(int year, int weekNo) {
        Calendar cal = getCalendarFormYear(year);
        cal.set(Calendar.WEEK_OF_YEAR, weekNo);
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-M-d");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return format2.format(format1.parse(cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1) + "-"
                    + cal.get(Calendar.DAY_OF_MONTH)));
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1) + "-" + cal.get(Calendar.DAY_OF_MONTH);
    }

    // 获取某一年的某一周的周日日期
    public static String getEndDayOfWeekNo(int year, int weekNo) {
        Calendar cal = getCalendarFormYear(year);
        cal.set(Calendar.WEEK_OF_YEAR, weekNo);
        cal.add(Calendar.DAY_OF_WEEK, 6);
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-M-d");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return format2.format(format1.parse(cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1) + "-"
                    + cal.get(Calendar.DAY_OF_MONTH)));
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1) + "-" + cal.get(Calendar.DAY_OF_MONTH);
    }

    // 获取某季度的第一天和最后一天
    public static String[] getStartDayAndEndDayOfQuarterNo(int year, int quarterNo) {
        String[] s = new String[2];
        String str = "";
        // 设置本年的季
        Calendar quarterCalendar = null;
        switch (quarterNo) {
            case 1: // 本年到现在经过了一个季度，在加上前4个季度
                quarterCalendar = getCalendarFormYear(year);
                quarterCalendar.set(Calendar.MONTH, 3);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                str = DateUtils.formatDate(quarterCalendar.getTime(), "yyyy-MM-dd");
                s[0] = str.substring(0, str.length() - 5) + "01-01";
                s[1] = str;
                break;
            case 2: // 本年到现在经过了二个季度，在加上前三个季度
                quarterCalendar = getCalendarFormYear(year);
                quarterCalendar.set(Calendar.MONTH, 6);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                str = DateUtils.formatDate(quarterCalendar.getTime(), "yyyy-MM-dd");
                s[0] = str.substring(0, str.length() - 5) + "04-01";
                s[1] = str;
                break;
            case 3:// 本年到现在经过了三个季度，在加上前二个季度
                quarterCalendar = getCalendarFormYear(year);
                quarterCalendar.set(Calendar.MONTH, 9);
                quarterCalendar.set(Calendar.DATE, 1);
                quarterCalendar.add(Calendar.DATE, -1);
                str = DateUtils.formatDate(quarterCalendar.getTime(), "yyyy-MM-dd");
                s[0] = str.substring(0, str.length() - 5) + "07-01";
                s[1] = str;
                break;
            case 4:// 本年到现在经过了四个季度，在加上前一个季度
                quarterCalendar = getCalendarFormYear(year);
                str = DateUtils.formatDate(quarterCalendar.getTime(), "yyyy-MM-dd");
                s[0] = str.substring(0, str.length() - 5) + "10-01";
                s[1] = str.substring(0, str.length() - 5) + "12-31";
                break;
        }
        return s;
    }

    /**
     * 得到当前日期前后时间字符串
     *
     * @param currentdate date
     * @param offSet      偏移量
     * @param pattern     日期字符格式(如:yyyy_MM_dd)
     * @return {@link String}
     */
    public static String findBeforeOneDay(Date currentdate, int offSet, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentdate);
        calendar.add(Calendar.DAY_OF_MONTH, -offSet);
        currentdate = calendar.getTime();
        String dayTime = format.format(currentdate);
        return dayTime;
    }

    /**
     * 在日期date上增加amount天 。
     *
     * @param date   处理的日期，非null
     * @param amount 要加的天数(正数)/要减去的天数(负数)
     */
    public static String dealDays(String date, int amount) {
        Date dateTime = DateUtils.parseDate(date);
        Calendar now = Calendar.getInstance();
        now.setTime(dateTime);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + amount);
        return DateFormatUtils.format(now.getTime(), "yyyy-MM-dd");
    }


    /**
     * 在日期date上增加amount天 。
     *
     * @param date   处理的日期，非null
     * @param amount 要加的天数(正数)/要减去的天数(负数)
     */
    public static String dealDays(String date, int amount, String pattern) {
        Date dateTime = DateUtils.parseDate(date);
        Calendar now = Calendar.getInstance();
        now.setTime(dateTime);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + amount);
        return DateFormatUtils.format(now.getTime(), pattern);
    }

    /**
     * 获取时间范围内的每一天
     *
     * @param beginTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static List<String> findEveryDay(String beginTime, String endTime) {
        //1.创建一个放所有日期的集合
        List<String> dates = new ArrayList();
        //2.创建时间解析对象规定解析格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //3.将传入的时间解析成Date类型,相当于格式化
        Date dBegin = null;
        Date dEnd = null;
        try {
            dBegin = sdf.parse(beginTime);
            dEnd = sdf.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //4.将格式化后的第一天添加进集合
        dates.add(sdf.format(dBegin));
        //5.使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();
        //6.传入起始时间将此日历设置为起始日历
        calBegin.setTime(dBegin);
        //8.判断结束日期是否在起始日历的日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 9.根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            //10.得到的每一天就添加进集合
            dates.add(sdf.format(calBegin.getTime()));
            //11.如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }

    /**
     * 日期格式转换
     *
     * @param inputDate 输入日期
     * @return {@link String}
     */

    public static String convertDateFormat(String inputDate) {
        try {
            SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyyMMdd");
            Date date = inputDateFormat.parse(inputDate);

            SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy.MM.dd");
            return outputDateFormat.format(date);
        } catch (ParseException e) {
            return getDateToday();
        }
    }


    /**
     * 获取时间范围内的每一天
     *
     * @param beginTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static List<String> findEveryDay(String beginTime, String endTime, String Pattern) {
        //1.创建一个放所有日期的集合
        List<String> dates = new ArrayList();
        //2.创建时间解析对象规定解析格式
        SimpleDateFormat sdf = new SimpleDateFormat(Pattern);
        //3.将传入的时间解析成Date类型,相当于格式化
        Date dBegin = null;
        Date dEnd = null;
        try {
            dBegin = sdf.parse(beginTime);
            dEnd = sdf.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //4.将格式化后的第一天添加进集合
        dates.add(sdf.format(dBegin));
        //5.使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();
        //6.传入起始时间将此日历设置为起始日历
        calBegin.setTime(dBegin);
        //8.判断结束日期是否在起始日历的日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 9.根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            //10.得到的每一天就添加进集合
            dates.add(sdf.format(calBegin.getTime()));
            //11.如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }

    /**
     * 获取精确到秒的时间戳
     *
     * @return
     */
    public static int getSecondTimestamp(Date date) {
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime());
        int length = timestamp.length();
        if (length > 3) {
            return Integer.valueOf(timestamp.substring(0, length - 3));
        } else {
            return 0;
        }
    }

    /**
     * @Description: 转换为指定格式时间（注意数据库中时间格式）
     * <AUTHOR>
     * @date 2018年4月16日
     */
    public static Date formatDate(String strDate) {
        if (strDate == null) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(strDate);
        } catch (ParseException e) {
            return null;
        }
    }


    /**
     * 计算时间区间
     *
     * @param section
     * @return
     */
    public static String computationInterval(String section) {
        String intervalValue = "";
        int hour = Integer.parseInt(section.substring(11, 13));
        if (hour == 23) {
            intervalValue = hour + "-" + 0;
        } else {
            intervalValue = hour + "-" + (hour + 1);
        }
        return intervalValue;
    }

    /**
     * 将Timestamp对象按照指定的模式转换为字符串
     *
     * @param timestamp 时间戳
     * @param pattern   图案
     * @return {@link String}
     */
    public static String timestampToString(Timestamp timestamp, String pattern) {
        if (timestamp == null) {
            return null;
        }

        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date(timestamp.getTime()));
    }


    /**
     * 转换为时间戳
     *
     * @param timeStr 时间str
     * @return long
     */
    public static long convertToTimestamp(String timeStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse(timeStr);
        // 设置目标时区
        TimeZone targetTimeZoneObject = TimeZone.getTimeZone("Asia/Shanghai");
        sdf.setTimeZone(targetTimeZoneObject);
        return date.getTime() / 1000; // 转为秒
    }

    /**
     * 判断是有效日期格式
     *
     * @param input 输入
     * @return boolean
     */
    public static boolean isValidDateFormat(String input, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        dateFormat.setLenient(false); // 关闭宽松模式，禁止解析不合法日期
        try {
            dateFormat.parse(input);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * ES索引日期转换日期格式
     *
     * @param input 输入
     * @return {@link String}
     */
    public static String EsDateFormat(String input) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy.MM.dd");

        try {
            // 将输入字符串解析为日期
            Date date = inputFormat.parse(input);
            // 将日期格式化为指定格式的字符串
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return DateUtils.getBeforeOneDay(new Date(), 0); // 处理解析异常，返回当天时间
        }
    }

    /**
     * 获取当前时间
     *
     * @param pattern 格式化类型
     * @return {@link String }
     */
    public static String getCurrentTime(String pattern) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return now.format(formatter);
    }

    /**
     * 将时间字符串转换为 LocalDateTime
     *
     * @param dateStr 日期str
     * @param format  格式
     * @return {@link Date }
     * @throws Exception 例外
     */
    public static LocalDateTime stringToDate(String dateStr, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(dateStr, formatter);
    }

    /**
     * 获取当前时间
     */
    public static String getNowTime() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date());
    }

    /**
     * @Description:获取两个时间之差的秒数
     * <AUTHOR> by xiongpf
     * @date 2018-01-10
     */
    public static int getTimeSecondsByBothDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startDate = null;
        Date endDate = null;
        long startDateMilliSeconds = 0;
        long endDateMilliSeconds = 0;
        try {
            startDate = sdf.parse(startTime);
            endDate = sdf.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        // 得到两个日期对象的总毫秒数
        if (null != startDate) {
            startDateMilliSeconds = startDate.getTime();
        }
        if (null != endDate) {
            endDateMilliSeconds = endDate.getTime();
        }

        long totalMilliSeconds = endDateMilliSeconds - startDateMilliSeconds;
        return (int) (totalMilliSeconds / 1000);
    }

}
