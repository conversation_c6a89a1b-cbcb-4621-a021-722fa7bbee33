<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.15</version>
    </parent>

    <groupId>cn.ctyun.nfd</groupId>
    <artifactId>APICleanEngine</artifactId>
    <version>2.2.1</version>
    <name>APICleanEngine</name>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <maven.test.skip>true</maven.test.skip>
        <spring-kafka.version>2.7.14</spring-kafka.version>
        <postgresql.version>42.5.5</postgresql.version>
        <snakeyaml.version>2.2</snakeyaml.version>
        <tomcat.version>9.0.99</tomcat.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>webmvc</groupId>
            <artifactId>2025_webmvc</artifactId>
            <version>5.3.41</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/webmvc-5.3.41-2025.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.huawei.gauss200.jdbc</groupId>
            <artifactId>gsjdbc200</artifactId>
            <version>2.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/gsjdbc200.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>2.6.4</version>
        </dependency>

        <dependency>
            <groupId>com.oceanbase.jdbc</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/oceanbase-client-2.4.1.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.pcap4j</groupId>
            <artifactId>pcap4j-core</artifactId>
            <version>1.8.2</version>
        </dependency>
        <dependency>
            <groupId>org.pcap4j</groupId>
            <artifactId>pcap4j-packetfactory-static</artifactId>
            <version>1.8.2</version>
        </dependency>

        <!--syslog-->
        <dependency>
            <groupId>org.graylog2</groupId>
            <artifactId>syslog4j</artifactId>
            <version>0.9.60</version>
        </dependency>

        <!--DM (根据需求放开,避免打包过大) -->
        <!--        <dependency>-->
        <!--            &lt;!&ndash;注意：只有这个版本的hibernate兼容达梦数据库 &ndash;&gt;-->
        <!--            <groupId>org.hibernate</groupId>-->
        <!--            <artifactId>hibernate-core</artifactId>-->
        <!--            <version>5.3.18.Final</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dameng</groupId>-->
        <!--            <artifactId>DmJdbcDriver18</artifactId>-->
        <!--            <version>8.1.1.193</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>DM</groupId>-->
        <!--            <artifactId>DmDialect-for-hibernate</artifactId>-->
        <!--            <version>1.0</version>-->
        <!--            <scope>system</scope>-->
        <!--            <systemPath>${project.basedir}/src/main/resources/lib/DmDialect-for-hibernate5.3.jar</systemPath>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.6.0</version>
        </dependency>

        <!--postgresql (根据需求放开,避免打包过大)-->
        <!--        <dependency>-->
        <!--            <groupId>org.postgresql</groupId>-->
        <!--            <artifactId>postgresql</artifactId>-->
        <!--            <version>${postgresql.version}</version>-->
        <!--        </dependency>-->

        <!--实现配置文件加密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot</artifactId>
            <version>1.18</version>
        </dependency>
        <dependency>
            <groupId>org.jasypt</groupId>
            <artifactId>jasypt</artifactId>
            <version>1.9.2</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.hyperic</groupId>
            <artifactId>sigar</artifactId>
            <version>0.9.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/sigar-lib/sigar.jar</systemPath>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.19</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>

        <!-- elasticsearch7账号密码-start -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.9.1</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.9.1</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.9.1</version>
            <!--排除httpclient、httpcore低版本依赖-->
            <exclusions>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--httpclient-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>
        <!--httpcore-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.9</version>
        </dependency>
        <!-- elasticsearch7-end -->

        <!-- elasticsearch2-start -->
        <!--<dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>transport</artifactId>
            <version>5.4.3</version>
        </dependency>-->
        <!-- elasticsearch2-end -->


        <!-- google utils -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>14.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>

        <!-- poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.2.5</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/poi-scratchpad-5.2.5.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.wzsec</groupId>
            <artifactId>watermark</artifactId>
            <version>1.0.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/watermark-1.0.2.jar</systemPath>
        </dependency>

        <!-- 定时任务 -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz-jobs</artifactId>
            <version>2.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.5</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.20</version>
        </dependency>


        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.8.3</version>
        </dependency>

        <!--hutool-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.12</version>
        </dependency>
        <!--lang3-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.3.2</version>
            <scope>compile</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework.kafka/spring-kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>${spring-kafka.version}</version>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.6.0</version>
                <executions>
                    <!-- 以下配置说明执行mvn的package命令时候，会执行proguard-->
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <proguardVersion>7.1.0</proguardVersion>
                    <!-- 输入Jar的名称，输入混淆前的jar。 -->
                    <injar>${project.build.finalName}.jar</injar>
                    <!-- 输出jar名称，输出混淆后的jar -->
                    <outjar>${project.build.finalName}.jar</outjar>
                    <!-- 是否混淆 默认是true -->
                    <obfuscate>true</obfuscate>
                    <!-- 混淆配置文件proguard.cfg -->
                    <proguardInclude>${project.basedir}/proguard.cfg</proguardInclude>
                    <!-- 项目编译所需要的jar -->
                    <libs>
                        <lib>${java.home}/lib/rt.jar</lib>
                        <lib>${java.home}/lib/jce.jar</lib>
                        <lib>${java.home}/lib/jsse.jar</lib>
                    </libs>
                    <!-- 对输入jar进行过滤比如，如下配置就是对META-INFO文件不处理。 -->
                    <inLibsFilter>!META-INF/**,!META-INF/versions/9/**.class</inLibsFilter>
                    <!-- 输出路径配置，但是要注意这个路径必须要包括injar标签填写的jar -->
                    <outputDirectory>${project.basedir}/target</outputDirectory>
                    <!--配置混淆的一些细节选项，可在proguard.cfg中配置-->
                    <options>
                    </options>
                    <!-- 把jar包放到临时目录以便缩短命令行 -->
                    <putLibraryJarsInTempDir>true</putLibraryJarsInTempDir>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.guardsquare</groupId>
                        <artifactId>proguard-base</artifactId>
                        <version>7.1.0</version>
                        <scope>runtime</scope>
                    </dependency>
                    <dependency>
                        <groupId>com.guardsquare</groupId>
                        <artifactId>proguard-core</artifactId>
                        <version>7.1.0</version>
                        <scope>runtime</scope>
                    </dependency>
                </dependencies>
            </plugin>
            <!--  proguard 代码混淆配置  结束-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.0.RELEASE</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
