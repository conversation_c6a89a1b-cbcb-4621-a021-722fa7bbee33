package com.wzsec.clean.filter.clean.chinaMobileOnline;

import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;

import java.util.HashMap;
import java.util.Map;

/**
 * [中移在线]请求解析器
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
public class RequestParser {

    /**
     * 日志类型识别请求响应
     *
     * @param apiCallNetFlow 目标端写入ES对象
     * @param LOG_TYPECD     日志类型
     * @param MSG_CNTT       交易报文
     */
    public static void logTypeRecognitionRequestResponse(ApiCallNetFlow apiCallNetFlow,
                                                         String LOG_TYPECD,
                                                         String MSG_CNTT) {
        if (LOG_TYPECD.equals(Const.LOG_TYPE_REQUEST)) { //日志类型为请求
            // TODO 请求需解析请求头和请求体
            Params params = new Params();
            //请求解析
            Map<String, Object> result = RequestParser.parseRequest(MSG_CNTT);
            Map<String, String> reqHeader = (Map<String, String>) result.get("reqHeader"); //请求头
            String reqMsg = (String) result.get("reqMsg"); //请求体
            params.setParams(reqMsg);
            params.setHead(reqHeader);
            apiCallNetFlow.setReqcontent(params);
        } else if (LOG_TYPECD.equals(Const.LOG_TYPE_RESPONSE)) { //日志类型为响应
            DataRescontent dataRescontent = new DataRescontent();
            dataRescontent.setData(MSG_CNTT);
            apiCallNetFlow.setRescontent(dataRescontent);
        }
    }


    /**
     * 解析请求
     *
     * @param input 输入
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    public static Map<String, Object> parseRequest(String input) {
        Map<String, Object> result = new HashMap<>();

        // 截取请求头部分
        int headerStart = input.indexOf("reqHeader={");
        int headerEnd = input.indexOf("},reqMsg=");
        if (headerStart != -1 && headerEnd != -1) {
            String reqHeaderStr = input.substring(headerStart + 11, headerEnd).trim();
            Map<String, String> reqHeader = parseHeaderToMap(reqHeaderStr);
            result.put("reqHeader", reqHeader);
        }

        // 截取请求体部分（从 reqMsg= 后面开始截取）
        int bodyStart = input.indexOf("reqMsg=");
        if (bodyStart != -1) {
            String reqMsg = input.substring(bodyStart + 7).trim();  // 从 "reqMsg=" 后面开始截取，去掉 "reqMsg="
            result.put("reqMsg", reqMsg);
        }

        return result;
    }

    /**
     * 将请求头字符串解析为键值对
     *
     * @param headerStr 请求头字符串
     * @return {@link Map }<{@link String }, {@link String }>
     */
    public static Map<String, String> parseHeaderToMap(String headerStr) {
        Map<String, String> headerMap = new HashMap<>();

        // 去掉首尾的双引号
        headerStr = headerStr.replaceAll("^\"|\"$", "");

        // 按", "分割键值对
        String[] pairs = headerStr.split("\",\"");
        for (String pair : pairs) {
            String[] keyValue = pair.split("\":\"");
            if (keyValue.length == 2) {
                String key = keyValue[0].replaceAll("^\"|\"$", "").trim();  // 去掉键的引号
                String value = keyValue[1].replaceAll("^\"|\"$", "").trim();  // 去掉值的引号
                headerMap.put(key, value);
            }
        }

        return headerMap;
    }

    public static void main(String[] args) {
        String input = "reqHeader={\"accessSecretkey\":\"UxIghkqDh3x/+i8rUKQQlNDDpNGYorNs+LFZggJro5x9zMhYvCHGAeCFCka2HH48\",\"sendTime\":\"2024-11-02 04:50:17\"},reqMsg={\"params\":{\"CNTMNG_ID\":\"2024091413331214513213349\"}}";

        // 调用解析方法
        Map<String, Object> result = parseRequest(input);

        // 输出请求头
        System.out.println("请求头:");
        Map<String, String> reqHeader = (Map<String, String>) result.get("reqHeader");
        reqHeader.forEach((key, value) -> System.out.println(key + ": " + value));

        // 输出请求体
        System.out.println("\n请求体:");
        String reqMsg = (String) result.get("reqMsg");
        System.out.println(reqMsg);
    }
}

