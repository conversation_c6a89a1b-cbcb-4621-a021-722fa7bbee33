package com.wzsec.clean.modules.model;

/**
 * Title: FTPResultDetail
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/1
 */
public class FTPResultDetail {
    private String id;
    private String sourceip;
    private String sourceport;
    private String desip;
    private String desport;
    private String sign;
    private String username;
    private String password;
    private String filename;
    private String filepath;
    private String filesize;
    private String resulttype;
    private String sensitivedata;
    private String risk;
    private String resulttypecount;
    private String totalcount;
    private String ratio;
    private String operationtime;
    private String checktime;
    private String sparefield1;
    private String sparefield2;
    private String sparefield3;
    private String sparefield4;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourceip() {
        return sourceip;
    }

    public void setSourceip(String sourceip) {
        this.sourceip = sourceip;
    }

    public String getSourceport() {
        return sourceport;
    }

    public void setSourceport(String sourceport) {
        this.sourceport = sourceport;
    }

    public String getDesip() {
        return desip;
    }

    public void setDesip(String desip) {
        this.desip = desip;
    }

    public String getDesport() {
        return desport;
    }

    public void setDesport(String desport) {
        this.desport = desport;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getFilepath() {
        return filepath;
    }

    public void setFilepath(String filepath) {
        this.filepath = filepath;
    }

    public String getFilesize() {
        return filesize;
    }

    public void setFilesize(String filesize) {
        this.filesize = filesize;
    }

    public String getResulttype() {
        return resulttype;
    }

    public void setResulttype(String resulttype) {
        this.resulttype = resulttype;
    }

    public String getSensitivedata() {
        return sensitivedata;
    }

    public void setSensitivedata(String sensitivedata) {
        this.sensitivedata = sensitivedata;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public String getResulttypecount() {
        return resulttypecount;
    }

    public void setResulttypecount(String resulttypecount) {
        this.resulttypecount = resulttypecount;
    }

    public String getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(String totalcount) {
        this.totalcount = totalcount;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getOperationtime() {
        return operationtime;
    }

    public void setOperationtime(String operationtime) {
        this.operationtime = operationtime;
    }

    public String getChecktime() {
        return checktime;
    }

    public void setChecktime(String checktime) {
        this.checktime = checktime;
    }

    public String getSparefield1() {
        return sparefield1;
    }

    public void setSparefield1(String sparefield1) {
        this.sparefield1 = sparefield1;
    }

    public String getSparefield2() {
        return sparefield2;
    }

    public void setSparefield2(String sparefield2) {
        this.sparefield2 = sparefield2;
    }

    public String getSparefield3() {
        return sparefield3;
    }

    public void setSparefield3(String sparefield3) {
        this.sparefield3 = sparefield3;
    }

    public String getSparefield4() {
        return sparefield4;
    }

    public void setSparefield4(String sparefield4) {
        this.sparefield4 = sparefield4;
    }
}
