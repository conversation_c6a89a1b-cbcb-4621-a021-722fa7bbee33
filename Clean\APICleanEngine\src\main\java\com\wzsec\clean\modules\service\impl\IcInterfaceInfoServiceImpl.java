package com.wzsec.clean.modules.service.impl;

import com.wzsec.clean.modules.model.IcInterfaceInfo;
import com.wzsec.clean.modules.service.IcInterfaceInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 金华接口信息
 */
@Service
public class IcInterfaceInfoServiceImpl implements IcInterfaceInfoService {

    @Autowired
    private com.wzsec.clean.modules.dao.IcInterfaceInfoDao IcInterfaceInfoDao;


    /**
     * 获取接口信息(接口编码,接口名称,区县部门信息)
     *
     * @return {@link List}<{@link IcInterfaceInfo}>
     */
    @Override
    public List<IcInterfaceInfo> selectInterfaceInfo() {

        List<Map<String, Object>> InterfaceInfoMap = IcInterfaceInfoDao.selectInterfaceInfo();
        List<IcInterfaceInfo> IcInterfaceInfoList = new ArrayList<>();
        for (Map<String, Object> interfaceMap : InterfaceInfoMap) {
            IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
            icInterfaceInfo.setId(Long.parseLong(String.valueOf(interfaceMap.get("id"))));
            icInterfaceInfo.setApicode((String) interfaceMap.get("apicode"));  //接口编码
            icInterfaceInfo.setApiname((String) interfaceMap.get("apiname"));  //接口名称
            icInterfaceInfo.setInterface_status((String) interfaceMap.get("interface_status"));  //接口状态
            icInterfaceInfo.setArea((String) interfaceMap.get("area"));  //区县
            icInterfaceInfo.setData_source((String) interfaceMap.get("data_source"));  //部门
            IcInterfaceInfoList.add(icInterfaceInfo);
        }

        return IcInterfaceInfoList;
    }

    /**
     * 通过接口编码查询是否入库
     */
    @Override
    public List<String> queryApicodeList() {
        return IcInterfaceInfoDao.queryApicodeList();
    }


    @Override
    public String cleanAPIServiceFlag(String flag) {
        return IcInterfaceInfoDao.cleanAPIServiceFlag(flag);
    }

    @Override
    public List<String> cleanAPIServiceList(String flag) {
        return IcInterfaceInfoDao.cleanAPIServiceList(flag);
    }

}
