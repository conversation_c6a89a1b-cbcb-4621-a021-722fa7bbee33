package com.wzsec.clean.common.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;


/**
 * Title: ESUtil
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/17
 */
public class ES7Util {

    private static Logger log = LoggerFactory.getLogger(ES7Util.class);

    //ES 7.9.1版本

    /**
     * 获取ES客户端连接
     */
    public static RestHighLevelClient getClient(String username, String password, String ip, int port) {
        try {
            synchronized (ES7Util.class) {
                long t = System.currentTimeMillis();

                /** 用户认证对象 */
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                /** 设置账号密码 */
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
                /** 创建rest client对象 */
                RestClientBuilder builder = RestClient.builder(new HttpHost(ip, port))
                        .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                            @Override
                            public HttpAsyncClientBuilder customizeHttpClient(
                                    HttpAsyncClientBuilder httpClientBuilder) {
                                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                            }
                        });
                RestHighLevelClient client = new RestHighLevelClient(builder);
                long t1 = System.currentTimeMillis();
                System.out.println("获得连接，耗时:" + (t1 - t) + "s");
                return client;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取ES客户端连接
     *
     * @param username 用户名
     * @param password 密码
     * @param hosts    Elasticsearch 集群的IP和端口，格式为 "ip:port,ip:port" 的字符串
     * @return RestHighLevelClient
     */
    public static RestHighLevelClient getClient(String username, String password, String hosts) {
        try {
            // 创建认证对象
            BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    org.apache.http.auth.AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password)
            );

            // 创建连接池管理器
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

            // 配置请求信息，设置更长的连接和读取超时
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(0) // 设置读取数据的超时，0表示无限制
                    .setConnectTimeout(60000) // 设置连接超时，确保能建立连接
                    .build();

            // 创建HttpClient
            HttpClientBuilder httpClientBuilder = HttpClients.custom()
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setDefaultRequestConfig(requestConfig)
                    .setConnectionManager(connectionManager);

            // 解析输入的hosts，支持多个节点，分隔符为逗号
            String[] hostArray = hosts.split(",");
            HttpHost[] httpHosts = new HttpHost[hostArray.length];

            for (int i = 0; i < hostArray.length; i++) {
                String[] ipPort = hostArray[i].split(":");
                if (ipPort.length == 2) {
                    httpHosts[i] = new HttpHost(ipPort[0], Integer.parseInt(ipPort[1]));
                } else {
                    throw new IllegalArgumentException("主机格式无效");
                }
            }

            // 创建 RestClientBuilder 并传入所有的 HttpHost
            RestClientBuilder builder = RestClient.builder(httpHosts);

            // 设置认证和其他配置
            builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                @Override
                public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                }
            });

            // 创建 RestHighLevelClient
            return new RestHighLevelClient(builder);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 关闭ES连接
     */
    public static void closeClient(RestHighLevelClient client) {
        try {
            if (client != null) {
                client.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭ES连接
     */
    public static void closeEsClient(RestHighLevelClient client) {
        try {
            if (client != null) {
                client.close();
                client = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 判断索引是否存在
     *
     * @param indexName
     * @return
     */
    public static boolean existsIndex(RestHighLevelClient client, String indexName) {
        try {
            GetIndexRequest request = new GetIndexRequest(indexName);
            boolean exists = client.indices().exists(request, RequestOptions.DEFAULT);
            return exists;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 创建ES索引
     *
     * @param client
     * @param indexName
     * @return
     */
    public static boolean createIndex(RestHighLevelClient client, String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            return acknowledged;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 添加数据
     *
     * @param client
     * @param indexName
     * @param jsondata
     */
    public static void addData(RestHighLevelClient client, String indexName, JSONObject jsondata) {
        IndexRequest indexRequest = new IndexRequest(indexName);
        try {
            indexRequest.source(jsondata, XContentType.JSON);
            // 同步方式
            client.index(indexRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
