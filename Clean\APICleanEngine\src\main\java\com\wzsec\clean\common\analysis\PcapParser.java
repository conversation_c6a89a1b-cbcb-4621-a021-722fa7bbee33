package com.wzsec.clean.common.analysis;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.ParamUtil;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.FTPData;
import com.wzsec.clean.modules.model.PcapData;
import com.wzsec.clean.modules.service.PcapService;
import com.wzsec.clean.common.rule.ProCommonRule;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;

@Component
@Transactional
public class PcapParser {

//	private static Map<String, String> applicationConfs = PropertiesUtil.getProperty("application.properties");

    private static Logger logger = LoggerFactory.getLogger(PcapParser.class);

    private static Map<String, Integer> countMap = new HashMap<>();
    private static Map<String, PcapData> resultMap = new HashMap<>();
    private static Map<String, PcapData> ackMap = new HashMap<>();
    // 敏感数据检测管理查询(key=敏感数据，value=敏感数据英文名)
    public static HashMap<String, String> sensitivedataMap = new HashMap<>();
    //统计每行包含结果类型
    public static HashSet<String> resultTypeSet = new HashSet<>();
    // 明文统计次数结果Map
    public static Map<String, Map<String, Integer>> sensitiveDataCountResultMap = new HashMap<>();
    //结果类型
    public static Map<String, Integer> resTypeMap = new HashMap<>();
    //检测总次数
    public static HashMap<String, Integer> totalCountByMethodMap = new HashMap<>();
    //FTP用户名密码
    public static FTPData ftpData = new FTPData();
    //FTP打开关闭连接
    public static boolean connect = false;
    //FTP 文件list
    public static List<String> ftpList = new ArrayList<>();
    //FTP 结果Map保存文件信息
    public static Map<String, List<String>> ftpListMap = new HashMap<>();
    //FTP 结果Map对象信息
    public static Map<String, FTPData> ftpEntityMap = new HashMap<>();

    private boolean flag;

    @Autowired
    private PcapService pcapService;


    //	@Scheduled(cron = "${pcap.cron}")
    public void getRes() {
        try {
            // 手机号白名单初始化
            Const.whitePhoneList = pcapService.getFileListInfo(Const.PHONEDETECTION, Const.WHITELIST);
            // 敏感数据检测管理查询
            sensitivedataMap = pcapService.selectSensitiveData(Const.INTERFACE_FLOW_SIGN);
            //结果类型
            resTypeMap = pcapService.selectResultType();

            String pcapDir = ConfigurationManager.getProperty("PcapCleanBeforePath").trim();
            String pcapDate = ConfigurationManager.getProperty("PcapDate").trim();
            String IntervalDay = ConfigurationManager.getProperty("Interval.day").trim();

            //间隔天数
            int INTERVAL_DAY = Integer.parseInt(IntervalDay);
            //pcap检测日期
            if ("".equals(pcapDate) || pcapDate == null) {
                //默认检测前一天
                pcapDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", INTERVAL_DAY);
            }
            //检测前路径
            String parserBeforeDir = "";
            if (pcapDir.endsWith(File.separator)) {
                parserBeforeDir = pcapDir + pcapDate;
            } else {
                parserBeforeDir = pcapDir + File.separator + pcapDate;
            }
            Map<String, String> fileNameMap = new HashMap<>();
            getAllFileName(parserBeforeDir, fileNameMap);

            //检测任务号  61_代表pcap
            String taskNum = "61" + TimeUtils.DateToStr1(new Date());
            //开始时间
            String startTime = TimeUtils.getNowTime();

            //文件解析
            if (fileNameMap != null) {
                Set<String> keys = fileNameMap.keySet();
                if (keys.size() != 0) {
                    logger.info("开始解析pcap文件");
                    logger.info("文件数量：" + fileNameMap.size());

                    for (String key : keys) {
                        parser(key);
                        flag = true;
                    }
                    logger.info("pcap文件解析完毕");
                } else {
                    logger.info("文件数量为0");
                }
            }
            //结束时间
            String endTime = TimeUtils.getNowTime();

            //HTTP流量结果统计保存信息
            recordHTTPCheckResultInfo(taskNum, startTime, endTime);
            //FTP流量结果统计保存信息
            recordFTPCheckResultInfo(taskNum, startTime, endTime);

            //变量销毁
            destruction();
        } catch (Exception e) {
            flag = false;
            logger.info("检测流量结果统计出现异常：" + e.getMessage());
            e.printStackTrace();
        }


    }

    /**
     * FTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * @param endTime
     */
    private void recordFTPCheckResultInfo(String taskNum, String startTime, String endTime) {
        if (ftpListMap.size() > 0) {
            Map<String, FTPData> detailMap = new HashMap<>();
//			String key = ftpData.getSource_address()+Const.AUDIT_SPLIT_JOIN+
//					ftpData.getDes_address()+Const.AUDIT_SPLIT_JOIN+
//					ftpData.getFilename();
            for (String key : ftpListMap.keySet()) {
                if (ftpListMap.containsKey(key)) {
                    FTPData ftpEntity = ftpEntityMap.get(key);
                    List<String> list = ftpListMap.get(key);
                    List<Integer> resultTypeList = checkSensitiveDataByUseRule(key, list);

                    for (Integer resyltTypeNum : resultTypeList) {
                        FTPData ftpDataNew = new FTPData();
                        String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                        //根据id查询结果类型
                        Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);
                        ftpDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                        if (!ftpDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                            ftpDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype())));
                        }
                        ftpDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
                        ftpDataNew.setChecktime(startTime);
                        ftpDataNew.setTasknum(taskNum);
                        ftpDataNew.setProtocol("FTP");
                        ftpDataNew.setDes_address(ftpEntity.getDes_address());
                        ftpDataNew.setSource_address(ftpEntity.getSource_address());
                        ftpDataNew.setTime(ftpEntity.getTime());

                        Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype());
                        Collection<Integer> values = checkCountMap.values();
                        int checkCount = 0;
                        for (Integer value : values) {
                            checkCount += value;
                        }
                        Integer totalCount = totalCountByMethodMap.get(key);
                        ftpDataNew.setContentcheckcount(String.valueOf(checkCount));
                        ftpDataNew.setContenttotalcount(String.valueOf(totalCount));
                        double rate = 100 * ((double) checkCount / totalCount);
                        BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                        double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        ftpDataNew.setRatio(String.valueOf(ratio));
                        ftpDataNew.setSign(ftpEntity.getSign());
                        ftpDataNew.setUsername(ftpEntity.getUsername());
                        ftpDataNew.setPassword(ftpEntity.getPassword());
                        ftpDataNew.setFilename(ftpEntity.getFilename());
                        ftpDataNew.setFilepath(ftpEntity.getFilepath());
                        ftpDataNew.setFilesize(ftpEntity.getFilesize());
                        detailMap.put(resKey, ftpDataNew);
                    }
                }
            }
            Collection<FTPData> pcapDatas = detailMap.values();

            for (FTPData ftpData : pcapDatas) {
                String id = UUID.randomUUID().toString().replace("-", "");
                ftpData.setId(id);
                pcapService.saveFtpResult(ftpData);
            }
            logger.info("保存FTP流量检测统计完成！");
            //初始化ftpList
//				ftpList=new ArrayList<>();

        } else {
            logger.info("FTP流量检测统计结果为空！");
        }

    }


    /**
     * HTTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * @param endTime
     */
    private void recordHTTPCheckResultInfo(String taskNum, String startTime, String endTime) {

        Map<String, PcapData> detailMap = new HashMap<>();

        //保存pcap文件信息
        if (resultMap != null && resultMap.size() != 0) {

            Set<String> keySet = resultMap.keySet();
            for (String key : keySet) {
                PcapData pcapData = resultMap.get(key);
                //概要统计
                if (countMap.containsKey(key)) {
                    pcapData.setTotalcount(String.valueOf(countMap.get(key)));
                }
                pcapData.setTasknum(taskNum);
                pcapData.setChecktime(startTime);
                String id = UUID.randomUUID().toString().replace("-", "");
                pcapData.setId(id);
                pcapService.saveResult(pcapData);

                String[] datas = pcapData.getContent().split(",|:|\"|\\{|}|\\[|]");
                ArrayList<String> list = convertArrToList(datas);

                //详情统计 统计敏感数据
                List<Integer> resultTypeList = checkSensitiveDataByUseRule(key, list);

                for (Integer resyltTypeNum : resultTypeList) {
                    PcapData pcapDataNew = new PcapData();
                    String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                    //根据id查询结果类型
                    Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);
                    pcapDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                    if (!pcapDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                        pcapDataNew.setExample(pcapData.getContent());
                        pcapDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + pcapDataNew.getResulttype())));
                    }
                    pcapDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
                    pcapDataNew.setChecktime(pcapData.getChecktime());
                    pcapDataNew.setTasknum(pcapData.getTasknum());
                    pcapDataNew.setInterface_uri(pcapData.getInterface_uri());
                    pcapDataNew.setProtocol(pcapData.getProtocol());
                    pcapDataNew.setDes_address(pcapData.getDes_address());
                    pcapDataNew.setSource_address(pcapData.getSource_address());
                    pcapDataNew.setTime(pcapData.getTime());

                    Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + pcapDataNew.getResulttype());
                    Collection<Integer> values = checkCountMap.values();
                    int checkCount = 0;
                    for (Integer value : values) {
                        checkCount += value;
                    }
                    Integer totalCount = totalCountByMethodMap.get(key);
                    pcapDataNew.setContentcheckcount(String.valueOf(checkCount));
                    pcapDataNew.setContenttotalcount(String.valueOf(totalCount));
                    double rate = 100 * ((double) checkCount / totalCount);
                    BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                    double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    pcapDataNew.setRatio(String.valueOf(ratio));
//					pcapDataNew.setSign(pcapData.getSign());
                    detailMap.put(resKey, pcapDataNew);
                }
            }
            logger.info("保存接口流量概要统计信息完成！");
            //保存详情结果
            Collection<PcapData> pcapDatas = detailMap.values();

            for (PcapData pcapData : pcapDatas) {
                String id = UUID.randomUUID().toString().replace("-", "");
                pcapData.setId(id);
                pcapService.saveDetailResult(pcapData);
            }
            logger.info("保存接口流量检测详情统计完成！");

            //保存接口流量审核任务
            HashMap<String, String> map = new HashMap<>();
            map.put("id", UUID.randomUUID().toString().replace("-", ""));
            map.put("tasknum", taskNum);
            if (flag) {
                map.put("status", "1");//成功
            } else {
                map.put("status", "2");//失败
            }
            map.put("typecount", String.valueOf(keySet.size()));
            Collection<Integer> values = countMap.values();
            int totalcount = 0;
            for (Integer value : values) {
                totalcount += value;
            }
            map.put("totalcount", String.valueOf(totalcount));
            map.put("starttime", startTime);
            map.put("endtime", endTime);
            pcapService.saveTask(map);

            logger.info("保存接口流量检测任务完成！");
        } else {
            logger.info("接口流量检测统计结果为空！");
        }
    }

    /**
     * 清洗数组中空字符串
     *
     * @param datas
     */
    private ArrayList<String> convertArrToList(String[] datas) {
        ArrayList<String> list = new ArrayList<>();
        for (String data : datas) {
            if (!data.equals("")) {
                list.add(data);
            }
        }
        return list;
    }

    /**
     * @description 解析pcap文件
     * <AUTHOR>
     */
    public void parser(String fielDir) throws IOException {

        FileInputStream fis = new FileInputStream(fielDir);
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        int m = fis.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);

        while (m > 0) {
            PcapData data = new PcapData();

            m = fis.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            //将时间戳转换成时间
            data.setTime(TimeUtils.TimestampToDateStrNew(data.getTime_s()));
            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));


            byte[] content = new byte[data.getpLength()];
            m = fis.read(content);
            data.setContent_byte(content);


            byte[] ver_ihla = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 14;
                ver_ihla[0] = content[b];
            }

            byte[] pro = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 23;
                pro[i] = content[b];
            }

            StringBuilder sbr = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 26;
                sbr.append(content[b] & 0xff);
                sbr.append(".");
            }
            sbr.deleteCharAt(sbr.length() - 1);
            data.setSource_address(sbr.toString());

            StringBuilder sba = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 30;
                sba.append(content[b] & 0xff);
                sba.append(".");
            }
            sba.deleteCharAt(sba.length() - 1);
            data.setDes_address(sba.toString());
            if ((short) ver_ihla[0] == 69) {
                if ((short) pro[0] == 6) {
                    //TCP协议
                    StringBuilder sbf = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 38;
                        sbf.append(content[b] & 0xff);
                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    data.setSeq_number(sbf.toString());


                    StringBuilder sbg = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 42;
                        sbg.append(content[b] & 0xff);
                    }
                    sbg.deleteCharAt(sbg.length() - 1);
                    data.setAck_number(sbg.toString());

                    //数据包转文本内容
//					String strContent = content2Str(data.getContent_byte());
                    String strContent = content2Str(data.getContent_byte(), "UTF-8");

                    //分析响应内容并保存Map HTTP
                    saveDataToMapForHTTP(data, strContent);

                    //分析响应内容并保存List FTP
                    saveDataToListForFTP(data, strContent);
                }
            }

        }
        fis.close();
    }

    /**
     * 分析解析数据包内容保存List FTP
     *
     * @param strContent 数据
     */
    private void saveDataToListForFTP(PcapData data, String strContent) {
        if (connect) {
            if (strContent.contains("\r\n")) {
                if (strContent.substring(0, strContent.indexOf("\r\n")).contains(Const.FTP_226_TRANSFER_COMPLETE)) {
                    //传输完成
                    connect = false;
                    String key = ftpData.getSource_address() + Const.AUDIT_SPLIT_JOIN +
                            ftpData.getDes_address() + Const.AUDIT_SPLIT_JOIN +
                            ftpData.getFilename();
                    FTPData ftpEntity = new FTPData();
                    ftpEntity.setSource_address(ftpData.getSource_address());
                    ftpEntity.setDes_address(ftpData.getDes_address());
                    ftpEntity.setTime(ftpData.getTime());
                    ftpEntity.setUsername(ftpData.getUsername());
                    ftpEntity.setPassword(ftpData.getPassword());
                    ftpEntity.setFilename(ftpData.getFilename());
                    ftpEntity.setFilepath(ftpData.getFilepath());
                    ftpEntity.setFilesize(ftpData.getFilesize());
                    ftpEntity.setSign(ftpData.getSign());
                    ftpEntityMap.put(key, ftpEntity);
                    ftpListMap.put(key, ftpList);
                    //文件传输完成清空list
                    ftpList = new ArrayList<>();

                }
            }
            if (connect) {
                String[] dataStr = strContent.split(",|:|\"|\\{|}|\\[|]|\t|\r|\n|\r\n");
                ArrayList<String> list = convertArrToList(dataStr);
                ftpList.addAll(list);
            }
        }

        //FTP 协议
        if (connect == false && strContent.contains("\r\n")) {
            String orderStr = strContent.substring(0, strContent.indexOf("\r\n"));
            if (orderStr.contains(Const.FTP_USER)) {
                //用户名
                String username = orderStr.substring(orderStr.indexOf(Const.FTP_USER) + 5);
                ftpData.setUsername(username);
            } else if (orderStr.contains(Const.FTP_PASS)) {
                //密码
                String password = orderStr.substring(orderStr.indexOf(Const.FTP_PASS) + 5);
                ftpData.setPassword(password);
            } else if (orderStr.contains(Const.FTP_257_PATH)) {
                //路径名
                String filepath = orderStr.substring(orderStr.indexOf(Const.FTP_257_PATH) + 4);
                filepath = filepath.substring(filepath.indexOf("\"") + 1, filepath.lastIndexOf("\""));
                ftpData.setFilepath(filepath);
            } else if (orderStr.contains(Const.FTP_RETR)) {
                //下载
                ftpData.setSign("文件下载");
                //文件名
                String filename = orderStr.substring(orderStr.indexOf(Const.FTP_RETR) + 5);
                ftpData.setFilename(filename);
            } else if (orderStr.contains(Const.FTP_STOR)) {
                //上传
                ftpData.setSign("文件上传");
                //文件名
                String filename = orderStr.substring(orderStr.indexOf(Const.FTP_STOR) + 5);
                ftpData.setFilename(filename);
            } else if (orderStr.contains(Const.FTP_150_OBMDC)) {
                ftpData.setAck_number(data.getAck_number());
                ftpData.setSeq_number(data.getSeq_number());
                ftpData.setTime(data.getTime());
                ftpData.setSource_address(data.getSource_address());
                ftpData.setDes_address(data.getDes_address());
                //打开链接
                String filsize = orderStr.substring(orderStr.indexOf(ftpData.getFilename()) + ftpData.getFilename().length());
                filsize = filsize.substring(filsize.indexOf("(") + 1, filsize.indexOf("bytes)"));
                ftpData.setFilesize(filsize);
                connect = true;
            }
        }


    }


    /**
     * 分析解析数据包内容保存Map HTTP
     *
     * @param strContent 数据
     */
    private void saveDataToMapForHTTP(PcapData data, String strContent) {
        if (strContent.contains("HTTP")) {
            //HTTP协议
            data.setProtocol("HTTP");
            if (strContent.contains("GET")) {
                strContent = strContent.substring(strContent.indexOf("GET"));
            } else if (strContent.contains("POST")) {
                strContent = strContent.substring(strContent.indexOf("POST"));
            } else if (strContent.contains("PUT")) {
                strContent = strContent.substring(strContent.indexOf("PUT"));
            } else if (strContent.contains("DELETE")) {
                strContent = strContent.substring(strContent.indexOf("DELETE"));
            } else {
                strContent = strContent.substring(strContent.indexOf("HTTP"));
            }
            String[] dataArr = strContent.split("\n");
            boolean type = false;
            for (String s : dataArr) {
                if (s.contains("Accept") || s.contains("Content-Type")) {
                    if (ParamUtil.checkContentTypeIsTxt(s)) {
//									System.out.println("seq"+data.getSeq_number());
//									System.out.println("ack"+data.getAck_number());
                        type = true;
                    }
                }
                if (s.contains("Content-Type: multipart/form-data")) {
                    //文件上传
                    data.setSign("文件上传");
                    type = true;
                }
                if (s.contains("Content-Disposition") || s.contains("Content-Type: application/octet-stream")) {
                    //文件下载 Content-Disposition:attachment;filename=测试.xlsx
                    data.setSign("文件下载");
                    type = true;
                }
            }
            if (type) {
                data.setContent(strContent);
                String[] s = strContent.split("GET|POST|PUT|DELETE");
                if (s.length == 2) {
                    String[] https = s[1].split("HTTP");
                    data.setInterface_uri(https[0].split("\\?")[0].trim());
                    String key = data.getSource_address() + Const.AUDIT_SPLIT_JOIN + data.getDes_address() +
                            Const.AUDIT_SPLIT_JOIN + data.getInterface_uri().trim();
                    interfaceInfoCount(key);
                    //通过请求的ack_number匹配响应的seq_number
                    ackMap.put(data.getAck_number(), data);
                    if (!resultMap.containsKey(key)) {
                        resultMap.put(key, data);
                    }
                } else {
                    //根据响应的seq_number获取请求信息
                    if (ackMap.containsKey(data.getSeq_number())) {
                        PcapData pcapData = ackMap.get(data.getSeq_number());
                        data.setInterface_uri(pcapData.getInterface_uri());
                        String key = data.getSource_address() + Const.AUDIT_SPLIT_JOIN + data.getDes_address() +
                                Const.AUDIT_SPLIT_JOIN + data.getInterface_uri().trim();
                        interfaceInfoCount(key);
                        resultMap.put(key, data);
                    }
                }
            }

        }
    }

    /**
     * 统计接口检测总数
     *
     * @param key
     */
    private void interfaceInfoCount(String key) {
        if (countMap.containsKey(key)) {
            countMap.put(key, countMap.get(key) + 1);
        } else {
            countMap.put(key, 1);
        }
    }

    private static String content2Str(byte[] content) {
        char[] chars = new char[content.length];
        for (int i = 0; i < content.length; i++) {
            chars[i] = (char) content[i];
        }
        return String.valueOf(chars);
    }

    /**
     * 字节数据转字符串处理中文乱码
     *
     * @param content
     * @param encode
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String content2Str(byte[] content, String encode) throws UnsupportedEncodingException {
        return new String(content, encode);
    }


    private static void reverseByteArray(byte[] arr) {
        byte temp;
        int n = arr.length;
        for (int i = 0; i < n / 2; i++) {
            temp = arr[i];
            arr[i] = arr[n - 1 - i];
            arr[n - 1 - i] = temp;
        }
    }

    private static int byteArrayToInt(byte[] b, int offset) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }

    private static short byteArrayToShort(byte[] b, int offset) {
        short value = 0;
        for (int i = 0; i < 2; i++) {
            int shift = (2 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }

        return value;
    }


    /**
     * 获取一个文件夹下的所有文件全路径和文件名
     *
     * @param path
     * @param fileNameMap
     */
    public static void getAllFileName(String path, Map<String, String> fileNameMap) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    if (f.getAbsolutePath().endsWith("pcap")) {
                        fileNameMap.put(f.getAbsolutePath(), f.getName());
                    }
                }
            }
        }
    }

    /*
     *@Decription 变量销毁
     *<AUTHOR>
     *@date 2020/7/10
     */
    public static void destruction() {
        countMap = new HashMap<>();
        resultMap = new HashMap<>();
        ackMap = new HashMap<>();
        sensitivedataMap = new HashMap<>();
        resultTypeSet = new HashSet<>();
        sensitiveDataCountResultMap = new HashMap<>();
        resTypeMap = new HashMap<>();
        totalCountByMethodMap = new HashMap<>();
    }


    /**
     * @Description:检测参数数据通过通用检测规则
     * <AUTHOR> by wangqi
     * @date 2020-09-03
     */
    private List<Integer> checkSensitiveDataByUseRule(String strKey, List<String> list) {
        // 校验参数是否包含敏感数据并返回相应权重
        resultTypeSet = new HashSet<>();
        List<Integer> resultTypeList = ProCommonRule.checkIsClearType(sensitivedataMap, sensitiveDataCountResultMap,
                strKey, list.toArray(new String[list.size()]), resultTypeSet, resTypeMap);

        // 统计检测次数
        if (list.size() > 0) {
            if (null == totalCountByMethodMap) {
                totalCountByMethodMap = new HashMap<>();
                totalCountByMethodMap.put(strKey, list.size());
            } else {
                if (totalCountByMethodMap.containsKey(strKey)) {
                    totalCountByMethodMap.put(strKey, totalCountByMethodMap.get(strKey).intValue() + list.size());
                } else {
                    totalCountByMethodMap.put(strKey, list.size());
                }
            }
        }

        return resultTypeList;
    }

    /**
     * @param map:需要根据value排序的map
     * @Description：按照行为时间进行排序，方式采集时时间顺序错误导致审计出错
     * <AUTHOR>
     * @date 2019年12月9日 下午3:13:02
     */
    private static String mapSort(Map<String, Integer> map) {
        if (map == null || map.size() == 0) {
            return null;
        }
        ArrayList<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            // 定义一个比较器
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                Integer num1 = o1.getValue();
                Integer num2 = o2.getValue();
                return num2.compareTo(num1);
            }
        });
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<String, Integer> l : list) {
            jsonObject.put(l.getKey(), l.getValue());
        }
        return jsonObject.toString();
    }
}
