package com.wzsec.clean.common.analysis.thread;

import com.alibaba.fastjson.JSONObject;
import com.wzsec.clean.common.rule.IcLogCheckTaskClean;
import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.ES7Util;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.filter.clean.HainanBigData.PcapMessageByHaiNan;
import com.wzsec.clean.common.analysis.PacketParser;
import com.wzsec.clean.filter.parser.FtpClean;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.PcapTask;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;

/**
 * Title: PcapClean
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/5
 */
@Component
@Slf4j
public class PcapCleanThread implements FileProcessHandler {

    private static Logger logger = LoggerFactory.getLogger(PcapCleanThread.class);

    private static int threadNum = 2;// 默认开启线程数量

    static {
        Integer temp = threadNum = ConfigurationManager.getInteger("CleanPcapThreadNum");
        if (temp != null) {
            threadNum = temp;
        }
    }


    /**
     * pcap文件清洗
     *
     * @param index ES索引
     * <AUTHOR>
     * @date 2020/11/5
     */
    public static void pcapCleanToES(String parserBeforeDir, String index, String checktime, PcapTask pcapTask) {

        // 多线程处理文件
        PcapFileCleanThread pcapFileCleanThread = new PcapFileCleanThread();
        List<String> fileTypes = new ArrayList<>();
        fileTypes.add("pcap");
        pcapFileCleanThread.fileCkeckThreadInit(parserBeforeDir, fileTypes, threadNum, new PcapCleanThread(),
                index, checktime, pcapTask);
        pcapFileCleanThread = null;
    }

    /**
     * 线程具体执行方法
     *
     * @param filePath          文件名称（完整地址）
     * @param countDownLatch    线程管理
     * @param countDownLatchAll 总线程管理
     * @param desPath           des路径
     * @param checktime         任务开始时间
     * @param pcapTask          任务对象
     * @Description
     * <AUTHOR>
     * @date 2020年11月06日
     */
    @Override
    public void exec(String filePath, CountDownLatch countDownLatch, String desPath,
                     CountDownLatch countDownLatchAll, String checktime, PcapTask pcapTask) {
        String threadName = Thread.currentThread().getName();
        String logHead = "线程【" + threadName + "】";
        //logger.info(logHead + "已开启，开始处理[" + filePath + "]文件");
        long t1 = System.currentTimeMillis();// 此批线程开始执行时间
        String[] paths = filePath.split(Matcher.quoteReplacement(File.separator));
        String filename = "";
        if (paths.length > 1) {
            filename = paths[paths.length - 1];
        }
        try {
            //流量检测方式 1：ES 、2：Disk File
            logger.info(logHead + "开始清洗文件：" + filePath);
            // HTTP传输文件还原路径
            String httprestoreFilePath = ConfigurationManager.getProperty("HttpRestoreFilePath").trim();
            String ftpRestoreFilePath = ConfigurationManager.getProperty("FTPRestoreFilePath");
            String pcapDate = ConfigurationManager.getProperty("PcapCleanBeforeDate").trim();
            String IntervalDay = ConfigurationManager.getProperty("PcapClean.IntervalDay").trim();
            //间隔天数
            int INTERVAL_DAY = Integer.parseInt(IntervalDay);
            //pcap检测日期
            if ("".equals(pcapDate) || pcapDate == null) {
                //默认检测前一天
                pcapDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", INTERVAL_DAY);
            }
            httprestoreFilePath = httprestoreFilePath + File.separator + pcapDate;
            ftpRestoreFilePath = ftpRestoreFilePath + File.separator + pcapDate;

            if (Const.FROM_ES.equals(pcapTask.getChecksource())) {
                //清洗保存到ES
                String username = ConfigurationManager.getProperty("netflow.es.username");
                String password = ConfigurationManager.getProperty("netflow.es.password");
                String hosts = ConfigurationManager.getProperty("netflow.es.hostlist");
                // String port = ConfigurationManager.getProperty("netflow.es.port");
                // 获取连接
                RestHighLevelClient client = ES7Util.getClient(username, password, hosts);
                // 清洗保存map
                PacketParser.parserToES(filePath, desPath, filename, client, httprestoreFilePath, ftpRestoreFilePath, checktime, pcapTask);

                // 写入ES
                if (PacketParser.httpJsonMap != null) {
                    String httpIndexName = "";
                    if (desPath.endsWith(Const.AUDIT_SPLIT_JOIN)) {
                        httpIndexName = desPath + Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + checktime;
                    } else {
                        httpIndexName = desPath + Const.AUDIT_SPLIT_JOIN + Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + checktime;
                    }
                    // http索引名称
                    for (String key : PacketParser.httpJsonMap.keySet()) {
                        //将清洗之后数据保存到ES
                        boolean exists = ES7Util.existsIndex(client, httpIndexName);
                        JSONObject json = PacketParser.httpJsonMap.get(key);
                        if (exists) {
                            ES7Util.addData(client, httpIndexName, json);
                        } else {
                            //创建索引
                            boolean createIndex = ES7Util.createIndex(client, httpIndexName);
                            if (createIndex) {
                                ES7Util.addData(client, httpIndexName, json);
                            }
                        }
                    }
                }

                // 保存完清空结果json map
                PacketParser.httpJsonMap = new HashMap<>();

                // 关闭连接
                ES7Util.closeEsClient(client);

                //从本地磁盘读取
            } else if (Const.FROM_DISK.equals(pcapTask.getChecksource())) {
                //判断  1:服务器 2:nginx代理
                String flowdatatype = ConfigurationManager.getProperty("netflow.collectdatatype").trim();
                if (Const.FLOWDATA_APISERVER.equals(flowdatatype)) {
                    // 清洗保存map  1.数据从服务器获取
                    if (Const.INTERFACE_FLOW_SIGN.equals(pcapTask.getType())) {   // 检测类型（0：接口日志，1：接口流量）
                        //清洗HTTP流量到本地磁盘
                        PacketParser.parser(filePath, desPath, filename, httprestoreFilePath, ftpRestoreFilePath, pcapTask);
                    }
                    if (Const.FTP_FLOW_SIGN.equals(pcapTask.getType())) {
                        //清洗FTP流量
                        FtpClean.parser(filePath);
                    }

                } else if (Const.FLOWDATA_NGINXPROXY.equals(flowdatatype)) {
                    // 清洗保存map  2.数据从Nginx代理获取
                    PacketParser.parserByProxy(filePath, desPath, filename, httprestoreFilePath, ftpRestoreFilePath, pcapTask);
                }

                List<ApiCallNetFlow> list = new ArrayList<>();

                // 清洗之后的文件路径
                String afterPath = desPath + File.separator + Const.HTTP_SIGN + File.separator
                        + Const.NETFLOW + "_clean_" + filename.replace("pcap", "txt");
                String cleanoutputtype = ConfigurationManager.getProperty("netflow.cleanoutputtype").trim();
                // 写入文件
                if (PacketParser.httpJsonMap != null || PcapMessageByHaiNan.httpJsonByHaiNanMap != null) {
                    if (PacketParser.httpJsonMap.size() > 0) {
                        for (String key : PacketParser.httpJsonMap.keySet()) {
                            //流量检测到本地配置 1:本地 2:ES 3:kafka
                            if (Const.CLEAN_DATA_OUTTYPE_DISK.equals(cleanoutputtype)) {
//                            log.info("清洗到本地");
                                PacketParser.append3File(PacketParser.httpJsonMap.get(key).toString(), afterPath);
                            } else if (Const.CLEAN_DATA_OUTTYPE_ES.equals(cleanoutputtype)) {
                                //log.info("清洗到ES");
                                if (Const.FLOWDATA_APISERVER.equals(flowdatatype)) {
//                                IcLogCheckTaskClean.getCleanedSingleLine(PacketParser.httpJsonMap.get(key).toString());
//                                    IcLogCheckTaskClean.getCleanedSingleLine(PacketParser.httpJsonMap.get(key).toString(),
//                                            PacketParser.transferByteMap.get(key) == null ? "" : PacketParser.transferByteMap.get(key),
//                                            list, "");
                                }
                            } else if (Const.CLEAN_DATA_OUTTYPE_KAFKA.equals(cleanoutputtype)) {
                                log.info("清洗到kafka");
                            }
                        }
                    }

                    // TODO 海南流量清洗重构
                    if (PcapMessageByHaiNan.httpJsonByHaiNanMap != null) {
                        for (String key : PcapMessageByHaiNan.httpJsonByHaiNanMap.keySet()) {
                            if (Const.CLEAN_DATA_OUTTYPE_ES.equals(cleanoutputtype)) {
                                if (Const.FLOWDATA_NGINXPROXY.equals(flowdatatype)) {
                                    if (PcapMessageByHaiNan.httpJsonByHaiNanMap.containsKey(key)) {
                                        IcLogCheckTaskClean.cleanWriteByHaiNan(PcapMessageByHaiNan.httpJsonByHaiNanMap.get(key).toString());
                                        PcapMessageByHaiNan.httpJsonByHaiNanMap.remove(key);
                                    }
                                }
                            }
                        }
                    }
                }
                // 保存完清空结果json map
                PacketParser.httpJsonMap = new HashMap<>();
                PacketParser.interfaceTransferMap = new HashMap<>();
                PacketParser.dataMap = new HashMap<>();
                PacketParser.filetransferMap = new HashMap<>();
                PacketParser.transferByteMap = new HashMap<>();
                PacketParser.realityByteMap = new HashMap<>();
                PacketParser.fileByteMap = new HashMap<>();
                PcapMessageByHaiNan.dataByHaiNanMap = new ConcurrentHashMap<>();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        long t2 = System.currentTimeMillis();// 此批线程结束执行时间
        logger.info(logHead + "执行完毕，文件[" + filePath + "]处理完毕，执行耗时：" + (t2 - t1) / 1000 + "s(秒)");
        countDownLatch.countDown();// 此线程执行完毕，通知线程开启处，配合countDownLatch.await()
        countDownLatchAll.countDown();// 通知总计数
    }

}
