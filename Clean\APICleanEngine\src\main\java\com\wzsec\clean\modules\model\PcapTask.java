package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SaaS任务Entity
 *
 * <AUTHOR>
 * @version 2020-04-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PcapTask {

    private static final long serialVersionUID = 1L;
    private String id;        // id
    private String taskname;        // 任务名称
    private String strategyconfig;        // 策略配置
    private String systemid;        // 对接系统标识
    private String userid;        // 用户id
    private String username;        // 作业提交人姓名
    private String isvaild;        // 是否有效（1有效，0无效）
    private String createuser;        // 创建人
    private String createtime;        // 创建时间
    private String updatetime;        // 更改时间
    private String sparefield1;        // 备用字段1
    private String sparefield2;        // 备用字段2
    private String updateuser;        // 更新人
    private String type;            // 检测类型（0：接口日志，1：接口流量）
    private String checksource;        // 检测源（0：ES，1：磁盘）
    private String checktime;        // 检测时间cron表达式

}
