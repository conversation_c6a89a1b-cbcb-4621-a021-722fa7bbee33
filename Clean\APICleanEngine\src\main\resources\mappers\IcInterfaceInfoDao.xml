<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wzsec.clean.modules.dao.IcInterfaceInfoDao">

    <select id="selectInterfaceInfo" resultType="hashmap">
        select * from sdd_ic_interfaceinfo
    </select>

    <select id="queryApicodeList" resultType="string">
        SELECT DISTINCT apicode FROM ic_interfaceinfo
    </select>


    <select id="cleanAPIServiceFlag" resultType="String">
        SELECT value FROM dict_detail JOIN dict ON dict_detail.dict_id = dict.id WHERE value = 'true' and name = #{flag}
    </select>


    <select id="cleanAPIServiceList" resultType="string">
        SELECT value FROM dict_detail WHERE dict_id = #{flag}
    </select>


</mapper>
