package com.wzsec.clean.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于从HTTP请求头中提取真实的客户端IP地址
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Slf4j
public class RealIPExtractor {

    /**
     * 常见的包含真实IP的HTTP头字段
     * 按照可信度和使用频率排序：
     * 1. X-Forwarded-For: 最常用的代理IP头，包含客户端和所有经过的代理服务器IP
     * 2. X-Real-IP: 通常由反向代理设置，直接包含原始客户端IP
     * 3. X-Remote-Addr: 某些代理服务器使用的头，包含直接连接的客户端IP
     * 4. X-Remote-IP: 某些代理服务器使用的头，功能类似X-Remote-Addr
     */
    private static final String X_FORWARDED_FOR = "x-forwarded-for";
    private static final String X_REAL_IP = "x-real-ip";
    private static final String X_REMOTE_ADDR = "x-remote-addr";
    private static final String X_REMOTE_IP = "x-remote-ip";

    /**
     * 从HTTP请求头字符串中提取真实IP地址
     * 按照预定义的优先级顺序依次检查各HTTP头字段：
     * 1. X-Forwarded-For - 最常用且包含完整代理链信息,需提取第一个IP
     * 2. X-Real-IP - 通常由反向代理直接设置为原始客户端IP
     * 3. X-Remote-Addr - 部分代理服务器使用
     * 4. X-Remote-IP - 部分代理服务器使用
     *
     * @param requestData 完整的HTTP请求头字符串
     * @return {@code String }
     */
    public static String extractRealIPFromHeader(String requestData) {
        if (StringUtils.isBlank(requestData)) {
            return null;
        }

        // 将请求数据转为小写以便不区分大小写匹配
        String lowerCaseRequestData = requestData.toLowerCase();

        // 按优先级尝试提取IP
        String ip = extractHeaderValue(lowerCaseRequestData, X_FORWARDED_FOR);
        if (StringUtils.isNotBlank(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个（最原始的客户端IP）
            ip = extractFirstIP(ip);
            return ip;
        }

        ip = extractHeaderValue(lowerCaseRequestData, X_REAL_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        ip = extractHeaderValue(lowerCaseRequestData, X_REMOTE_ADDR);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        ip = extractHeaderValue(lowerCaseRequestData, X_REMOTE_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        return null;
    }

    /**
     * 从请求头映射中提取真实IP地址
     * 1. X-Forwarded-For - 最常用且包含完整代理链信息,需提取第一个IP
     * 2. X-Real-IP - 通常由反向代理直接设置为原始客户端IP
     * 3. X-Remote-Addr - 部分代理服务器使用
     * 4. X-Remote-IP - 部分代理服务器使用
     *
     * @param requestHeaderMap 请求头映射，键为头字段名，值为头字段值
     * @return {@code String }
     */
    public static String extractRealIPFromHeaderMap(Map<String, String> requestHeaderMap) {
        if (requestHeaderMap == null || requestHeaderMap.isEmpty()) {
            return null;
        }

        // 按优先级尝试提取IP
        String ip = null;

        // 尝试从X-Forwarded-For获取（最常用且包含完整代理链信息）
        ip = getHeaderValueIgnoreCase(requestHeaderMap, X_FORWARDED_FOR);
        if (StringUtils.isNotBlank(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个（最原始的客户端IP）
            ip = extractFirstIP(ip);
            return ip;
        }

        // 尝试从X-Real-IP获取（通常由反向代理直接设置为原始客户端IP）
        ip = getHeaderValueIgnoreCase(requestHeaderMap, X_REAL_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        // 尝试从X-Remote-Addr获取
        ip = getHeaderValueIgnoreCase(requestHeaderMap, X_REMOTE_ADDR);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        // 尝试从X-Remote-IP获取
        ip = getHeaderValueIgnoreCase(requestHeaderMap, X_REMOTE_IP);
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        return null;
    }

    /**
     * 从请求头字符串中提取指定头字段的值
     *
     * @param header     请求头字符串，应已转为小写
     * @param headerName 头字段名称，小写形式
     * @return {@code String }
     */
    private static String extractHeaderValue(String header, String headerName) {
        String pattern = headerName + ": (.+)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(header);
        if (m.find()) {
            return m.group(1).trim();
        }
        return null;
    }

    /**
     * 从请求头映射中获取指定头字段的值（不区分大小写）
     * 1. 直接使用原始小写形式查找
     * 2. 使用首字母大写形式查找（如"X-Forwarded-For"）
     * 3. 遍历所有键进行不区分大小写比较
     *
     * @param headerMap  请求头映射
     * @param headerName 头字段名称，小写形式
     * @return {@code String }
     */
    private static String getHeaderValueIgnoreCase(Map<String, String> headerMap, String headerName) {
        // 直接尝试获取（小写形式）
        String value = headerMap.get(headerName);
        if (StringUtils.isNotBlank(value)) {
            return value.trim();
        }

        // 尝试获取（大写首字母形式，如"X-Forwarded-For"）
        String capitalizedHeaderName = capitalizeWords(headerName);
        value = headerMap.get(capitalizedHeaderName);
        if (StringUtils.isNotBlank(value)) {
            return value.trim();
        }

        // 遍历所有键，不区分大小写比较（最全面但效率较低的方法）
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(headerName)) {
                return entry.getValue().trim();
            }
        }

        return null;
    }

    /**
     * 将字符串中的每个单词首字母大写
     *
     * @param str str
     * @return {@code String }
     */
    private static String capitalizeWords(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }

        String[] words = str.split("-");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            if (words[i].length() > 0) {
                if (i > 0) {
                    result.append("-");
                }
                result.append(Character.toUpperCase(words[i].charAt(0)));
                if (words[i].length() > 1) {
                    result.append(words[i].substring(1));
                }
            }
        }

        return result.toString();
    }

    /**
     * 从X-Forwarded-For值中提取第一个IP地址
     * X-Forwarded-For格式通常为: client, proxy1, proxy2, ...
     * 其中第一个IP是最原始的客户端IP,后续的IP是请求经过的各级代理服务器IP
     *
     * @param xForwardedFor xForwardedFor
     * @return {@code String }
     */
    private static String extractFirstIP(String xForwardedFor) {
        if (StringUtils.isBlank(xForwardedFor)) {
            return xForwardedFor;
        }

        String[] ips = xForwardedFor.split(",");
        if (ips.length > 0) {
            return ips[0].trim();
        }

        return xForwardedFor.trim();
    }
}