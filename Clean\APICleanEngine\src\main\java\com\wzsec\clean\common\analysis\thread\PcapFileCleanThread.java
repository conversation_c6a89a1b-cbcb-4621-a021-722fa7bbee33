package com.wzsec.clean.common.analysis.thread;

import com.wzsec.clean.modules.model.PcapTask;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @ClassName: PcapFileCleanThread
 * @Description: 文件清洗线程类
 * @date 2020年11月06日
 */
@Slf4j
public class PcapFileCleanThread {

    private final static Logger logger = LoggerFactory.getLogger(PcapFileCleanThread.class);

    /**
     * 文件处理初始化，多线程处理文件
     *
     * @param currentThreadSum：线程数量、一次文件读取数量、过多导致内存溢出
     * @param service：文件处理接口
     * <AUTHOR>
     * @date 2020年11月06日
     */
    public void fileCkeckThreadInit(String parserBeforeDir, List<String> fileTypes, int currentThreadSum,
                                    FileProcessHandler service, String pcapCleanAfterPath, String checktime, PcapTask pcapTask) {
        List<String> fileNameList = new ArrayList<>();
        findFiles(fileNameList, parserBeforeDir);// 获取目录下所有指定类型文件
        //log.info("---> {},获取到的文件数量为: {}", parserBeforeDir, fileNameList.size());
        int size = fileNameList.size();
        if (size == 0) {
            StringBuffer fileTypeSB = new StringBuffer();
            for (String fileType : fileTypes) {
                fileTypeSB.append(fileType).append("、");
            }
            logger.info(fileTypeSB.toString() + "等类型文件数量为0，跳过多线程检测，走主线程检测。");
            return;
        }
        logger.info("【主线程】读取文件线程start。。。");
        //logger.info("【主线程】额定线程数量：" + currentThreadSum + " (同时检测文件数量),执行类：" + service.getClass().getName());
        long startTime = System.currentTimeMillis();// 开始执行时间
        CountDownLatch countDownLatchAll = new CountDownLatch(size);// 用于告知所有线程执行完毕
        int openThreadNum = 0;
        for (int i = 0; i < size; i++) {// 当一个线程结束，再开启新线程，始终保持满线程运行
            int activeThreadNum = getActiveThreadSum(this.getClass().getSimpleName());// 当前活动线程数
            int canOpenThreadNum = 0;
            CountDownLatch countDownLatch = new CountDownLatch(1);// 用于告知有空闲线程
            if (activeThreadNum > 0) {// 有活动线程，需判断可开启线程数
                canOpenThreadNum = currentThreadSum - activeThreadNum;// 可开启线程数
            } else if (activeThreadNum == 0) {// 所有通道均空闲
                canOpenThreadNum = currentThreadSum;
            }
            if (canOpenThreadNum == 0) {
                //logger.info("【主线程】额定线程数量：" + currentThreadSum + "，正在运行线程数量：" + activeThreadNum + "，可开启线程数量："
                //        + canOpenThreadNum + "，跳过开启新线程");
                i--;// 倒退循环
                continue;
            }
            logger.info("【主线程】目录" + parserBeforeDir + "下文件数量：" + size + "，已检测文件数量：" + openThreadNum + "，需开启总线程数量：" + size
                    + "，已开启线程数量：" + openThreadNum + "，未开启线程数量：" + (size - openThreadNum));
            StringBuffer threadNames = new StringBuffer();
            int needOpenThreadNum = canOpenThreadNum;
            if (i + canOpenThreadNum >= size) {// 最后一批未检测数量不足以支撑每个空闲线程都开启时
                needOpenThreadNum = size - openThreadNum;
            }
            //logger.info("【主线程】额定线程数量：" + currentThreadSum + "，正在运行线程数量：" + activeThreadNum + "，可开启线程数量："
            //        + canOpenThreadNum + "，实际开启线程数量：" + needOpenThreadNum);
            for (int j = 0; j < needOpenThreadNum; j++) {
                String filePath = fileNameList.get(i + j);// 当前需检测文件
                openThreadNum++;// 记录已开启线程数
                Runnable task = () -> service.exec(filePath, countDownLatch, pcapCleanAfterPath, countDownLatchAll, checktime, pcapTask);
                Thread thread = new Thread(task);
                thread.setName(this.getClass().getSimpleName() + "-" + openThreadNum);
                thread.start();
                threadNames.append("，线程[").append(thread.getName()).append("]已开启");
            }
            if (needOpenThreadNum > 0) {
                i += (needOpenThreadNum - 1);// 记录已检测文件数
            }
            logger.info("【主线程】成功开启 " + needOpenThreadNum + "个线程" + threadNames);
            try {
                logger.info("【主线程】正在等待任一线程执行完毕。。。");
                countDownLatch.await();// 等待N个线程执行后再继续执行，配合countDownLatch.countDown()
                if (openThreadNum < size) {// 非最后一个线程需等待有空闲线程
                    logger.info("【主线程】有空闲线程，可开启新线程。");
                } else {
                    logger.info("【主线程】所有线程已开启，无文件需要做处理。");
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        try {
            logger.info("【主线程】正在等待所有线程执行完毕。。。");
            countDownLatchAll.await();// 等待所有线程执行完毕
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        long endTime = System.currentTimeMillis();// 结束执行时间
        logger.info("【主线程】任务结束，总耗时：" + (endTime - startTime) / 1000 + "s(秒)");
        logger.info("【主线程】读取文件线程end。。。");
    }

    /**
     * @param threadName：根据名字获取指定类型线程
     * @return int：线程数量
     * @Description：获取指定类型线程数量
     * <AUTHOR>
     * @date 2020年11月06日
     */
    public int getActiveThreadSum(String threadName) {
        int threadSum = 0;
        ThreadGroup currentGroup = Thread.currentThread().getThreadGroup();
        Thread[] lstThreads = new Thread[currentGroup.activeCount()];
        currentGroup.enumerate(lstThreads);
        for (Thread thread : lstThreads) {
            if (thread.getName().contains(threadName)) {// 对线程根据线程名进行过滤
                threadSum++;
            }
        }
        return threadSum;
    }

    /**
     * 获取文件夹路径
     *
     * @param fileList 文件列表
     * @param path     路径
     */
    private static void findFiles(List<String> fileList, String path) {
        File[] allFiles = new File(path).listFiles();
        for (int i = 0; i < allFiles.length; i++) {
            File file = allFiles[i];
            if (file.isFile()) {
                fileList.add(String.valueOf(file));
            } else {
                findFiles(fileList, file.getAbsolutePath());
            }
        }
    }




}
