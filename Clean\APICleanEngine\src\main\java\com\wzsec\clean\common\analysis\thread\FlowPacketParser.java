package com.wzsec.clean.common.analysis.thread;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.FileTools;
import com.wzsec.clean.common.utils.ParamUtil;
import com.wzsec.clean.filter.parser.ApiFileRecognition;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.ApiWeaknesscheckService;
import com.wzsec.clean.modules.service.IcAlarmDisposalService;
import com.wzsec.clean.modules.service.IcInterfaceInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


/**
 * 流量数据包解析
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@Component
public class FlowPacketParser {

    // 被动模式、主动模式标识
    public static boolean ispassive = false;

    @Autowired
    private IcInterfaceInfoService icInterfaceInfoService;

    @Autowired
    private IcAlarmDisposalService icAlarmDisposalService;

    @Autowired
    private ApiWeaknesscheckService apiWeaknesscheckService;

    private static FlowPacketParser packetParser;

    private static String fileRecognitionStatus = ConfigurationManager.getProperty("apifilerecognition.status").trim();

    @PostConstruct
    public void init() {
        packetParser = this;
        packetParser.icInterfaceInfoService = this.icInterfaceInfoService;
        packetParser.icAlarmDisposalService = this.icAlarmDisposalService;
        packetParser.apiWeaknesscheckService = this.apiWeaknesscheckService;
    }

    /**
     * 主要针对服务器接口流量检测(通用直采定义)
     *
     * @param fielDir 文件路径
     * @throws IOException ioexception
     */
    public static void parser(String fielDir,
                              Map<String, JSONObject> dataMap,
                              Map<String, JSONObject> filetransferMap,
                              Map<String, JSONObject> interfaceTransferMap,
                              Map<String, String> transferByteMap,
                              Map<String, Integer> realityByteMap,
                              Map<String, byte[]> fileByteMap,
                              Map<String, JSONObject> httpJsonMap,
                              Map<String, String> ftpUserMap,
                              Map<String, Integer> ftpPortMap,
                              Map<String, String> ftpDataTransferMap,
                              Map<String, FTPResultDetail> ftpTransferMap,
                              Map<String, FTPResultDetail> ftpResultMap,
                              Map<String, FlowCombination> textFileMap) throws Exception {

        FileInputStream fis = new FileInputStream(fielDir);
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        int m = fis.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);


        while (m > 0) {
            Packet data = new Packet();

            byte[] content = new byte[0];
            byte[] ipv_headerlen = new byte[0];
            byte[] pro = new byte[0];
            try {
                m = fis.read(buffer_4);
                if (m < 0) {
                    break;
                }
                reverseByteArray(buffer_4);
                data.setTime_s(byteArrayToInt(buffer_4, 0));  //时间戳(秒)

                m = fis.read(buffer_4);
                reverseByteArray(buffer_4);
                data.setTime_ms(byteArrayToInt(buffer_4, 0));  //时间戳(微秒)

                m = fis.read(buffer_4);
                reverseByteArray(buffer_4);
                data.setpLength(byteArrayToInt(buffer_4, 0));  //抓包长度

                m = fis.read(buffer_4);
                reverseByteArray(buffer_4);
                data.setLength(byteArrayToInt(buffer_4, 0));  //实际长度


                content = new byte[data.getpLength()];
                m = fis.read(content);

                //pcap Internet Protocol Version 中 header 报文长度 截取后四位
                ipv_headerlen = new byte[1];
                for (int i = 0; i < 1; i++) {
                    int b = i + 14;
                    ipv_headerlen[0] = content[b];
                }

                String ip_header = Integer.toBinaryString(ipv_headerlen[0]);
                String ip_header_bin = "";
                if (ip_header.length() > 4) {
                    ip_header_bin = ip_header.substring(ip_header.length() - 4);
                } else {
                    ip_header_bin = ip_header;
                }
                int ip_header_len = Integer.parseInt(ip_header_bin, 2);

                //pcap Transmission Control Protocol 中 header 报文长度 7位截取前3位，8位截取前4位   TODO
                StringBuilder tcp_header = new StringBuilder();
                for (int i = 0; i < 1; i++) {
                    int b = i + 46;
                    tcp_header.append(Integer.toHexString(content[b] & 0xff));
                }
                Integer tcp_header_dec = Integer.valueOf(tcp_header.toString(), 16);
                String tcp_header_bin = Integer.toBinaryString(tcp_header_dec);
                String tcp_header_bin_str = "";
                if (tcp_header_bin.length() > 4) {
                    tcp_header_bin_str = tcp_header_bin.substring(0, tcp_header_bin.length() - 4);
                } else {
                    tcp_header_bin_str = tcp_header_bin;
                }
                Integer tcp_header_len = Integer.valueOf(tcp_header_bin_str, 2);

                //总长度
                StringBuilder tl = new StringBuilder();
                for (int i = 0; i < 2; i++) {
                    int b = i + 16;
                    String hexStr = Integer.toHexString(content[b] & 0xff);
                    tl.append(appendZero(hexStr));
                }
                Integer len = Integer.valueOf(tl.toString(), 16);


                //截取有效负载payload长度=总长度 - (ip_header_len * 4) - (tcp_header_len * 4)
                int payloadLen = len - (ip_header_len * buffer_4.length) - (tcp_header_len * buffer_4.length);
                if (payloadLen > 0) {
                    byte[] contentbyte = new byte[payloadLen];

                    System.arraycopy(content, content.length - contentbyte.length, contentbyte, 0, payloadLen);  //TODO

                    data.setContent_byte(contentbyte);   //TODO 数据包

                } else {
                    continue;
                }

                pro = new byte[1];
                for (int i = 0; i < 1; i++) {
                    int b = i + 23;
                    pro[i] = content[b];
                }

                StringBuilder sbr = new StringBuilder();
                for (int i = 0; i < 4; i++) {
                    int b = i + 26;
                    sbr.append(content[b] & 0xff);
                    sbr.append(".");
                }
                sbr.deleteCharAt(sbr.length() - 1);
                data.setSourceip(sbr.toString());  // 原地址

                StringBuilder sba = new StringBuilder();
                for (int i = 0; i < 4; i++) {
                    int b = i + 30;
                    sba.append(content[b] & 0xff);
                    sba.append(".");
                }
                sba.deleteCharAt(sba.length() - 1);
                data.setDesip(sba.toString());  //TODO 目标地址
            } catch (Exception e) {
                e.printStackTrace();
                //Console.log("数据包解析出现异常,异常信息为: {}", e.getMessage());
            }

            if ((short) ipv_headerlen[0] == 69) {    //TODO 原有:69
                if ((short) pro[0] == 6) {
                    //TCP协议
                    StringBuilder sbd = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 34;
                        String hexStr = Integer.toHexString(content[b] & 0xff);
                        sbd.append(appendZero(hexStr));
                    }
                    Integer souport = Integer.valueOf(sbd.toString(), 16);
                    data.setSourceport(String.valueOf(souport));  // 源端口

                    StringBuilder sbe = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 36;
                        String hexStr = Integer.toHexString(content[b] & 0xff);
                        sbe.append(appendZero(hexStr));
                    }
                    Integer desport = Integer.valueOf(sbe.toString(), 16);
                    data.setDesport(String.valueOf(desport));  // 目标端口

                    StringBuilder sbf = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 38;
                        sbf.append(content[b] & 0xff);
                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    data.setSeq_number(sbf.toString());  // 序号


                    StringBuilder sbg = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 42;
                        sbg.append(content[b] & 0xff);
                    }
                    sbg.deleteCharAt(sbg.length() - 1);
                    data.setAck_number(sbg.toString()); // 确认号

                    //数据包转文本内容
                    String strContent = content2Str(data.getContent_byte(), "UTF-8");

                    //Console.log("数据包解析的文件内容为: {}", strContent);
                    //Console.log("------------------------------");

                    data.setContent(strContent);


                    // HTTP流量处理
                    cleanHttpFlow(data, dataMap, filetransferMap, interfaceTransferMap,
                            transferByteMap, realityByteMap, fileByteMap, httpJsonMap);

                    // 文档文件识别
                    if (fileRecognitionStatus.equals(Const.AUTHMETHOD_ON)) {
                        ApiFileRecognition.interfaceTextFileExtraction(data,textFileMap);
                    }

                    //FTP流量处理
                    //try {
                    //    cleanFTPFlow(data, ftpUserMap, ftpPortMap, ftpDataTransferMap,
                    //            ftpTransferMap, ftpResultMap);
                    //} catch (Exception e) {
                    //    e.printStackTrace();
                    //}
                }
            }

        }

        fis.close();
    }


    /**
     * 主要针对海南代理接口流量检测(适配海南大数据局)
     *
     * @param fielDir 文件路径
     * @throws IOException ioexception
     */
    public static void parserByProxy(String fielDir,
                                     Map<String, JSONObject> dataMap,
                                     Map<String, JSONObject> httpJsonMap) throws IOException {

        FileInputStream is = new FileInputStream(fielDir);
        Pcap pcap = null;
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        pcap = new Pcap();
        PcapHeader header = new PcapHeader();
        int m = is.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        header.setMagic(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_2);
        reverseByteArray(buffer_2);
        header.setMagor_version(byteArrayToShort(buffer_2, 0));
        m = is.read(buffer_2);
        reverseByteArray(buffer_2);
        header.setMinor_version(byteArrayToShort(buffer_2, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setTimezone(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setSigflags(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setSnaplen(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setLinktype(byteArrayToInt(buffer_4, 0));
        pcap.setHeader(header);

        while (m > 0) {
            Packet data = new Packet();

            // TODO 时间戳（秒）
            m = is.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            //TODO 时间戳（微妙）
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            // TODO 抓包长度
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            //TODO 实际长度
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));
            byte[] content = new byte[data.getpLength()];


            m = is.read(content);
            //TCP协议
            //TODO 源端口
            StringBuilder sbd = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 34;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                sbd.append(appendZero(hexStr));
            }
            Integer souport = Integer.valueOf(sbd.toString(), 16);
            data.setSourceport(String.valueOf(souport));


            //TODO 目标端口
            StringBuilder sbe = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 36;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                sbe.append(appendZero(hexStr));
            }
            Integer desport = Integer.valueOf(sbe.toString(), 16);
            data.setDesport(String.valueOf(desport));

            // TODO 序号
            StringBuilder sbf = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 38;
                sbf.append(content[b] & 0xff);
            }
            sbf.deleteCharAt(sbf.length() - 1);
            data.setSeq_number(sbf.toString());

            //TODO 确认号
            StringBuilder sbg = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 42;
                sbg.append(content[b] & 0xff);
            }
            sbg.deleteCharAt(sbg.length() - 1);
            data.setAck_number(sbg.toString());
            data.setContent_byte(content);

            String strContent = content2Str(content, "UTF-8");
            data.setContent(strContent);

            // TODO 海南HTTP流量清洗
            cleanHttpFlowMessageNew(data, dataMap, httpJsonMap);

        }

        is.close();
    }


    /**
     * TODO  new 海南流量清洗场景
     *
     * @param data 数据
     */
    public static void cleanHttpFlowMessageNew(Packet data,
                                               Map<String, JSONObject> dataMap,
                                               Map<String, JSONObject> httpJsonMap) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        if (strContent.contains(Const.HTTP_SIGN)) {
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");
            boolean type = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.contains("Accept") && !s.contains("Accept-")) {
                    type = true;
                    continue;
                }
                // 判断响应头
                if (s.contains("Content-Type")) {
                    type = true;
                    continue;
                }
            }

            if (type) {

                String responseHeader = ""; //响应头

                String[] s1 = strContent.split("GET|POST|PUT|DELETE");
                if (s1.length == 2) {
                    String requestId = ""; //请求编码
                    String requestTime = ""; //请求时间
                    String apicode = ""; //接口编码
                    String xff = ""; //代理路由
                    String requestHeader = ""; //请求头

                    //TODO 新增字段
                    String desip = "";
                    String sourceip = "";
                    String reqcontent = "";
                    String interfaceuri = "";
                    String rescontent = "";
                    String code = "";
                    String apiname = "";
                    String ak = "";


                    String[] s = strContent.split("\r\n\r\n")[0].split("\r\n");

                    for (int i = 0; i < s.length; i++) {

                        // 请求标识
                        if (s[i].trim().contains("x-request-id")) {
                            requestId = s[i].trim().substring(("x-request-id").length() + 1).trim();
                        }

                        // 请求时间
                        if (s[i].trim().contains("hnjhpt_rtime")) {
                            requestTime = s[i].trim().substring(("hnjhpt_rtime").length() + 1).trim();
                        }

                        // apicode
                        if (s[i].trim().contains("_api_name")) {
                            apicode = s[i].trim().substring(("_api_name").length() + 1).trim();
                        }

                        // apiname
                        if (s[i].trim().contains("hnjhpt_sid")) {
                            apiname = s[i].trim().substring(("hnjhpt_sid").length() + 1).trim();
                        }

                        //ak标识
                        if (s[i].trim().contains("_api_access_key")) {
                            ak = s[i].trim().substring(("_api_access_key").length() + 1).trim();
                        }

                        // xff
                        if (s[i].trim().contains("x-forwarded-for") || s[i].trim().contains("X-Forwarded-For")) {
                            //路由流转
                            if (s[i].trim().contains("x-forwarded-for")) {
                                xff = s[i].trim().substring(("x-forwarded-for").length() + 1).trim();
                            } else {
                                xff = s[i].trim().substring(("X-Forwarded-For").length() + 1).trim();
                            }
                        }

                        // 客户端及服务端IP
                        if (s[i].trim().contains("X-Real-IP") || s[i].trim().contains("x-real-ip")) {
                            //客户端IP
                            desip = s[i].trim().substring(("X-Real-IP").length() + 1).trim();
                        }

                        if (s[i].trim().contains("x-forwarded-for") || s[i].trim().contains("X-Forwarded-For")) {
                            //服务端IP
                            String XFF = s[i].trim().substring(("x-forwarded-for").length() + 1).trim();

                            try {
                                sourceip = XFF.substring(0, xff.indexOf(","));
                            } catch (Exception e) {
                                sourceip = XFF;
                            }
                        }


                    }

                    String requestBody = "";
                    requestHeader = StringUtils.join(dataArr, "\r\n");
                    if (dataArr.length > 1) {
                        try {
                            requestBody = strContent.split("\r\n\r\n")[1];
                        } catch (Exception e) {
                            requestBody = "";
                        }
                    }


                    String[] split1 = strContent.split("GET|POST|PUT|DELETE");
                    if (split1.length == 2) {
                        String[] http = split1[1].split("HTTP");
                        // 接口路径
                        interfaceuri = http[0].trim();
                    }

                    //TODO 请求体
                    joInfo.put("apicode", apicode);
                    joInfo.put("xff", xff);
                    joInfo.put("requestTime", requestTime);
                    joInfo.put("requestHeader", requestHeader);
                    joInfo.put("requestBody", requestBody);

                    //TODO  新增字段
                    joInfo.put("apiname", apiname);
                    joInfo.put("starttime", requestTime);
                    joInfo.put("desip", desip);
                    joInfo.put("sourceip", sourceip);
                    joInfo.put("reqcontent", requestBody);
                    joInfo.put("interfaceuri", URLUtil.decode(interfaceuri));
                    joInfo.put("ak", ak);

                    dataMap.put(requestId, joInfo);

                } else {

                    // 响应第一段
                    if (strContent.contains(Const.HTTP_SIGN)) {
                        String restId = "";
                        //状态码
                        String statuscode = "200";

                        String[] s2 = strContent.split("\r\n\r\n")[0].split("\r\n");
                        for (int i = 0; i < s2.length; i++) {
                            if (s2[i].trim().contains("x-request-id")) {
                                restId = s2[i].trim().substring(("x-request-id").length() + 1).trim();
                            }

                            if (s2[i].trim().contains(Const.HTTP_SIGN)) {
                                String[] arr = s2[i].split("\\s+");
                                int index = 0;
                                for (int j = 0; j < arr.length; j++) {
                                    if (arr[j].contains(Const.HTTP_SIGN)) {
                                        index = j + 1;
                                    }
                                }
                                try {
                                    statuscode = arr[index];
                                } catch (Exception e) {
                                    statuscode = "";
                                }
                            }

                        }

                        //根据响应的seq_number获取请求信息
                        if (dataMap.containsKey(restId)) {
                            //responseHeader = strContent.split("\r\n")[0].split(" ")[1];

                            responseHeader = StringUtils.join(dataArr, "\r\n");
                            String responseBody = "";
                            if (dataArr.length > 1) {
                                try {
                                    responseBody = strContent.split("\r\n\r\n")[1];
                                } catch (Exception e) {
                                    responseBody = "";
                                }
                            }

                            //String requestheader = (String) dataByHaiNanMap.get(restId).get("requestHeader");
                            //String requsetbody = (String) dataByHaiNanMap.get(restId).get("requestBody");
                            //String requestTime = (String) dataByHaiNanMap.get(restId).get("requestTime");
                            String apicode = (String) dataMap.get(restId).get("apicode");
                            String xff = (String) dataMap.get(restId).get("xff");
                            joType.put("seqnum", restId);
                            //joType.put("time", requestTime);
                            joType.put("apicode", apicode);
                            joType.put("responsebody", responseBody);

                            //TODO  新增字段
                            String apiname = (String) dataMap.get(restId).get("apiname");
                            String starttime = (String) dataMap.get(restId).get("starttime");
                            String desip = (String) dataMap.get(restId).get("desip");
                            String sourceip = (String) dataMap.get(restId).get("sourceip");
                            String reqcontent = (String) dataMap.get(restId).get("reqcontent");
                            String interfaceuri = (String) dataMap.get(restId).get("interfaceuri");
                            String ak = (String) dataMap.get(restId).get("ak");
                            joType.put("apiname", apiname);
                            joType.put("starttime", data.getTime_s());
                            joType.put("desip", desip);
                            joType.put("sourceip", sourceip);
                            joType.put("reqcontent", reqcontent);
                            joType.put("interfaceuri", interfaceuri);
                            joType.put("ak", ak);

                            if (responseBody.startsWith("{") && !responseBody.endsWith("}")) {
                                int endLocation = responseBody.lastIndexOf("}") + 1;
                                responseBody = responseBody.substring(0, endLocation);
                            }

                            joType.put("rescontent", responseBody);  //响应参数
                            joType.put("statuscode", statuscode);

                            httpJsonMap.put(restId, joType);
                            dataMap.remove(restId);
                        }
                    }
                }
            }
        }
    }


    /**
     * 清洗HTTP流量
     *
     * <AUTHOR>
     * @date 2020-11-05
     */
    public static void cleanHttpFlow(Packet data,
                                     Map<String, JSONObject> dataMap,
                                     Map<String, JSONObject> filetransferMap,
                                     Map<String, JSONObject> interfaceTransferMap,
                                     Map<String, String> transferByteMap,
                                     Map<String, Integer> realityByteMap,
                                     Map<String, byte[]> fileByteMap,
                                     Map<String, JSONObject> httpJsonMap) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        // 响应第一段、文件传输响应多部分、接口传输响应多部分
        if (strContent.contains(Const.HTTP_SIGN) ||
                filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN) ||
                interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {

            // '\r\n\r\n'截取请求头
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");
            boolean type = false;
            boolean interfaceTransfer = true;
            boolean fileTransfer = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.startsWith("Accept") && !s.startsWith("Accept-")) {
                    type = true;
                    continue;

                }
                // 判断响应头
                if (s.startsWith("Content-Type")) {
                    if (ParamUtil.checkContentTypeIsTxt(s)) {
                        type = true;
                        // 判断多部分文件传输
                        if (s.contains("application/octet-stream")) {
                            fileTransfer = true;
                            interfaceTransfer = false;
                        }
                        continue;
                    }
                }
                // 判断响应头 文件传输
                if (s.startsWith("Content-Disposition:")) {
                    type = true;
                    String[] options = s.split(":")[1].split(";");
                    String downFileName = "";
                    for (String option : options) {
                        if (option.contains("filename")) {
                            downFileName = option.split("=")[1].replaceAll("\\\"", "").trim();
                        }
                    }
                    // 文件名-文件字节大小
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String contentTypeSize = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), downFileName);
                    }
                }
                // 文件名-文件字节大小
                if (fileTransfer == true && s.startsWith("Content-Length")) {
                    String contentTypeSize = s.split(":")[1].trim();
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String downFileName = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), contentTypeSize);
                    }
                }
            }
            // 判断接口是否多部分传输
            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
            }
            // 判断文件是否多部分传输
            if (filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
                fileTransfer = true;
                interfaceTransfer = false;
            }
            if (type) {
                String[] s = strContent.split("GET|POST|PUT|DELETE");
                if (s.length == 2) {
                    //请求
                    String reqmethod = strContent.split("\r\n")[0].split(" ")[0];
                    String[] http = s[1].split(Const.HTTP_SIGN);
                    String acknum = data.getAck_number();
//                    String interfaceuri = http[0].split("\\?")[0].trim();
                    String interfaceuri = http[0].trim();
                    String sourceip = data.getSourceip();
                    String sourceport = data.getSourceport();
                    String desip = data.getDesip();
                    String desport = data.getDesport();
                    String reqcontent = strContent;
                    joInfo.put("seqnumber", acknum);
                    joInfo.put("interfaceuri", interfaceuri);
                    joInfo.put("sourceip", sourceip);
                    joInfo.put("sourceport", sourceport);
                    joInfo.put("desip", desip);
                    joInfo.put("desport", desport);
                    joInfo.put("reqmethod", reqmethod);
                    joInfo.put("starttime", data.getTime_s());
                    joInfo.put("reqcontent", reqcontent);
                    dataMap.put(acknum, joInfo);
                } else {
                    // 响应第一段
                    if (strContent.startsWith(Const.HTTP_SIGN)) {
                        //根据响应的seq_number获取请求信息
                        if (dataMap.containsKey(data.getSeq_number())) {
                            String resstatuscode = strContent.split("\r\n")[0].split(" ")[1];
                            JSONObject reqJson = dataMap.get(data.getSeq_number());
                            String seqnumber = data.getSeq_number();
                            String interfaceuri = reqJson.get("interfaceuri").toString();
                            String sourceip = data.getSourceip();
                            String sourceport = data.getSourceport();
                            String desip = data.getDesip();
                            String desport = data.getDesport();
                            //String resstatus = "";
                            String rescontent = strContent;
                            //支持类型 xml, html, jsonp
                            if (FileTools.isHtml(rescontent)) {
                                rescontent = FileTools.isHtmlJsonTransfer(rescontent);
                            } else if (FileTools.isXML(rescontent)) {
                                rescontent = FileTools.isXmlJsonTransfer(rescontent);
                            } else if (FileTools.isJsonp(rescontent)) {
                                rescontent = FileTools.isJsonpTransfer(rescontent);
                            }
                            joInfo.put("seqnumber", seqnumber);
                            joInfo.put("interfaceuri", interfaceuri);
                            joInfo.put("sourceip", sourceip);
                            joInfo.put("sourceport", sourceport);
                            joInfo.put("desip", desip);
                            joInfo.put("desport", desport);
                            joInfo.put("endtime", data.getTime_s());
                            joInfo.put("resstatuscode", resstatuscode);
                            joInfo.put("rescontent", rescontent);
                            joType.put("req", reqJson);
                            joType.put("res", joInfo);
                            dataMap.remove(seqnumber);

                            // 接口传输map
                            if (interfaceTransfer) {
                                if (!interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                    httpJsonMap.put(data.getAck_number(), joType);
                                }
                            }

                            // 文件传输map
                            if (fileTransfer) {
                                if (!filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                }
                                // 文件传输实际字节大小
                                if (realityByteMap.containsKey(data.getAck_number())) {
                                    Integer len = realityByteMap.get(data.getAck_number());
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length + len);
                                } else {
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length);
                                }
                                // 组装多部分传输字节数组
                                if (fileByteMap.containsKey(data.getAck_number())) {
                                    byte[] bytes = fileByteMap.get(data.getAck_number());
                                    byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                    System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                    System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                    System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                    fileByteMap.put(data.getAck_number(), newByte);
                                } else {
                                    fileByteMap.put(data.getAck_number(), data.getContent_byte());
                                }
                            }
                        }
                    } else {
                        /** 响应多部分 多部分传输 多个响应ack相同
                         根据响应的文件多部分ack_number获取字节
                         接口传输保存结果 */
                        if (interfaceTransfer) {
                            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN) &&
                                    interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                JSONObject reqJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String rescontent = resJson.getString("rescontent");
                                StringBuilder rescontentBuilder = new StringBuilder();
                                rescontentBuilder.append(rescontent).append(data.getContent());
                                resJson.put("rescontent", rescontentBuilder.toString());
                                JSONObject joResult = new JSONObject(true);
                                joResult.put("req", reqJson);
                                joResult.put("res", resJson);
                                httpJsonMap.put(data.getAck_number(), joResult);
                            }
                        }

                        //文件传输
                        if (fileTransfer) {
                            int contentLen = 0;
                            int realityLen = 0;
                            String downFileName = "";

                            // 文件传输实际字节大小
                            if (realityByteMap.containsKey(data.getAck_number())) {
                                Integer len = realityByteMap.get(data.getAck_number());
                                contentLen = data.getContent_byte().length + len;
                                realityByteMap.put(data.getAck_number(), contentLen);
                            }

                            // 组装多部分传输字节数组
                            if (fileByteMap.containsKey(data.getAck_number())) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                fileByteMap.put(data.getAck_number(), newByte);
                            }

                            if (transferByteMap.containsKey(data.getAck_number())) {
                                String[] filenameAndSize = transferByteMap.get(data.getAck_number()).split(Const.AUDIT_SPLIT_JOIN);
                                if (filenameAndSize.length == 2) {
                                    downFileName = filenameAndSize[0];
                                    realityLen = Integer.parseInt(filenameAndSize[1]);
                                }

                            }

                            // 判断拼起来的包长度是否大于文件字节真实长度
                            if (contentLen > realityLen) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                // 开始索引
                                int startIndex = contentLen - realityLen;
                                byte[] fileByte = new byte[realityLen];
                                System.arraycopy(bytes, startIndex, fileByte, 0, fileByte.length);
                                //FileUtils.getFileByBytes(fileByte, resotreFilePath, downFileName);
                                //// 读取文件内容转成str
                                //String fileContent = getFileContent(resotreFilePath, downFileName);
                                JSONObject reqJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String interfaceuri = reqJson.get("interfaceuri").toString();
                                String resstatuscode = resJson.get("resstatuscode").toString();
                                String rescontent = resJson.get("rescontent").toString();
                                String seqnumber = resJson.get("seqnumber").toString();
                                String sourceip = data.getSourceip();
                                String sourceport = data.getSourceport();
                                String desip = data.getDesip();
                                String desport = data.getDesport();
                                //String resstatus = "";
                                joInfo.put("seqnumber", seqnumber);
                                joInfo.put("interfaceuri", interfaceuri);
                                joInfo.put("sourceip", sourceip);
                                joInfo.put("sourceport", sourceport);
                                joInfo.put("desip", desip);
                                joInfo.put("desport", desport);
                                joInfo.put("endtime", data.getTime_s());
                                joInfo.put("resstatuscode", resstatuscode);
                                rescontent = rescontent.split("\r\n\r\n")[0] + "\r\n\r\n";
                                joType.put("req", reqJson);
                                joType.put("res", joInfo);
                                filetransferMap.remove(data.getAck_number() + Const.REQ_SIGN);
                                filetransferMap.remove(data.getAck_number() + Const.RES_SIGN);
                                httpJsonMap.put(data.getAck_number(), joType);
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 清洗FTP流量
     *
     * <AUTHOR>
     * @date 2020-11-05
     */
    private static void cleanFTPFlow(Packet data,
                                     Map<String, String> ftpUserMap,
                                     Map<String, Integer> ftpPortMap,
                                     Map<String, String> ftpDataTransferMap,
                                     Map<String, FTPResultDetail> ftpTransferMap,
                                     Map<String, FTPResultDetail> ftpResultMap) {

        String strContent = data.getContent();
        FTPResultDetail ftpData = new FTPResultDetail();

        //FTP 协议
        String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();

        // 解决无法确定被动模式下客户端端口问题
        String datakey1 = "";
        String datakey2 = "";
        if (ispassive) {
            datakey1 = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport();
            datakey2 = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();
        } else {
            datakey1 = key;
        }
        // 拆分数据传输的内容
        String ftpTransferKey = "";
        if (ftpDataTransferMap.containsKey(datakey1)) {
            ftpTransferKey = ftpDataTransferMap.get(datakey1);
        } else if (ftpDataTransferMap.containsKey(datakey2)) {
            ftpTransferKey = ftpDataTransferMap.get(datakey1);
        }
        if (StringUtils.isNotEmpty(ftpTransferKey)) {
            int bytes = data.getContent_byte().length;

            //计算文件大小
            if (ftpResultMap.containsKey(ftpTransferKey)) {
                FTPResultDetail ftpResultDetail = ftpResultMap.get(ftpTransferKey);
                if (StringUtils.isNotEmpty(ftpResultDetail.getFilesize())) {
                    String filesize = ftpResultDetail.getFilesize();
                    long size = Long.valueOf(filesize).longValue() + (long) bytes;
                    ftpResultDetail.setFilesize(String.valueOf(size));
                    ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                } else {
                    ftpResultDetail.setFilesize(String.valueOf(bytes));
                    ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                }
            }
        }
        //保存数据传输的基本信息
        if (!ftpTransferMap.containsKey(key)) {
            ftpData.setSourceip(data.getSourceip());
            ftpData.setSourceport(data.getSourceport());
            ftpData.setDesip(data.getDesip());
            ftpData.setDesport(data.getDesport());
            ftpTransferMap.put(key, ftpData);
        }
        //截取4位去空格，FTP请求指令是4位，响应码是3位
        String code = "";
        if (strContent.length() > 4) {
            code = strContent.substring(0, 4).trim();
        }

        if (code.length() == 3) {
            switch (code) {
                case Const.FTP_CWD:
                    String path = strContent.split(Const.FTP_CWD)[1].trim();
                    if (ftpTransferMap.containsKey(key)) {
                        FTPResultDetail ftpResultDetail = ftpTransferMap.get(key);
                        ftpResultDetail.setFilepath(path);
                    }
                    break;
                case Const.FTP_226_TRANSFER_COMPLETE:
                    //传输完成
                    JSONObject joInfo = new JSONObject(true);
                    Set<String> ftpResultKeyMap = ftpResultMap.keySet();
                    for (String reskey : ftpResultKeyMap) {
                        FTPResultDetail ftpResult = ftpResultMap.get(reskey);
                        String sourceip = ftpResult.getSourceip();
                        String sourceport = ftpResult.getSourceport();
                        String desip = ftpResult.getDesip();
                        String desport = ftpResult.getDesport();
                        String sign = ftpResult.getSign();
                        String operationtime = ftpResult.getOperationtime();
                        String username = ftpResult.getUsername();
                        String password = ftpResult.getPassword();
                        String filename = ftpResult.getFilename();
                        String filepath = ftpResult.getFilepath();
                        String filesize = ftpResult.getFilesize();
                        joInfo.put("sourceip", sourceip);
                        joInfo.put("sourceport", sourceport);
                        joInfo.put("desip", desip);
                        joInfo.put("desport", desport);
                        joInfo.put("sign", sign);
                        joInfo.put("operationtime", operationtime);
                        joInfo.put("username", username);
                        joInfo.put("password", password);
                        joInfo.put("filename", filename);
                        joInfo.put("filepath", filepath);
                        joInfo.put("filesize", filesize);

                    }
                    ftpResultMap = new HashMap<>();
                    break;
                case Const.FTP_227_PASV:
                    ispassive = true;
                    String passivekey = data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport() + Const.AUDIT_SPLIT_JOIN +
                            data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport();
                    // 被动模式
                    String ipAndPort = strContent.substring(strContent.indexOf("(") + 1, strContent.lastIndexOf(")"));
                    //IP 地址和两字节的端口 ID
                    int port = getPort(ipAndPort);
                    ftpPortMap.put(passivekey, port);
                    break;
                default:
                    break;
            }
        } else {
            switch (code) {
                case Const.FTP_USER:
                    //用户名
                    String username = strContent.split(Const.FTP_USER)[1].trim();
                    ftpUserMap.put(key, username);
                    break;
                case Const.FTP_PASS:
                    //密码
                    if (ftpUserMap.containsKey(key)) {
                        String user = ftpUserMap.get(key);
                        String password = strContent.split(Const.FTP_PASS)[1].trim();
                        ftpUserMap.put(key, user + Const.AUDIT_SPLIT_JOIN + password);
                    }
                    break;
                case Const.FTP_PORT:
                    ispassive = false;
                    //IP 地址和两字节的端口 ID
                    int port = getPort(strContent.split(" ")[1]);
                    ftpPortMap.put(key, port);
                    break;
                case Const.FTP_STOR:
                    if (ftpTransferMap.containsKey(key)) {
                        String ftpfilename = strContent.split(Const.FTP_STOR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_STOR_SIGN);
                        ftpResultDetail.setFilename(ftpfilename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        ftpResultDetail.setOperationtime(String.valueOf(data.getTime_s()));
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = 0;
                        if (ftpPortMap.containsKey(key)) {
                            dataPort = ftpPortMap.get(key);
                        }
                        String dataKey = "";
                        String fileKey = "";
                        if (ispassive) {
                            // 被动
                            /*dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                    (Integer.parseInt(data.getSourceport())+1) + Const.AUDIT_SPLIT_JOIN +
                                    data.getDesip() + Const.AUDIT_SPLIT_JOIN + dataPort;*/
                            dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        } else {
                            // 主动
                            dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                    dataPort + Const.AUDIT_SPLIT_JOIN +
                                    data.getDesip() + Const.AUDIT_SPLIT_JOIN + Const.FTP_DATA_TRANSFER_PORT;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        }
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                case Const.FTP_RETR:
                    if (ftpTransferMap.containsKey(key)) {
                        String ftpfilename = strContent.split(Const.FTP_RETR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_RETR_SIGN);
                        ftpResultDetail.setFilename(ftpfilename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        ftpResultDetail.setOperationtime(String.valueOf(data.getTime_s()));
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = 0;
                        if (ftpPortMap.containsKey(key)) {
                            dataPort = ftpPortMap.get(key);
                        }
                        String dataKey = "";
                        String fileKey = "";
                        if (ispassive) {
                            // 被动
                            /*dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                    dataPort + Const.AUDIT_SPLIT_JOIN +
                                    data.getSourceip() + Const.AUDIT_SPLIT_JOIN + (Integer.parseInt(data.getSourceport())+1);*/
                            dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        } else {
                            // 主动
                            dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                    Const.FTP_DATA_TRANSFER_PORT + Const.AUDIT_SPLIT_JOIN +
                                    data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;
                        }

                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                default:
                    break;
            }
        }
    }


    public static void reverseByteArray(byte[] arr) {
        byte temp;
        int n = arr.length;
        for (int i = 0; i < n / 2; i++) {
            temp = arr[i];
            arr[i] = arr[n - 1 - i];
            arr[n - 1 - i] = temp;
        }
    }

    public static int byteArrayToInt(byte[] b, int offset) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }


    private static short byteArrayToShort(byte[] b, int offset) {
        short value = 0;
        for (int i = 0; i < 2; i++) {
            int shift = (2 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }


    /**
     * 字节数据转字符串处理中文乱码
     *
     * @param content
     * @param encode
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String content2Str(byte[] content, String encode) throws UnsupportedEncodingException {
        return new String(content, encode);
    }

    /**
     * FTP客户端数据传输端口计算
     *
     * @param token
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private static int getPort(String token) {
        String[] portCommand = token.replaceAll("[()]\\.", "").split(",");
        int port = 0;
        if (portCommand.length == 6) {
            port = (Integer.parseInt(portCommand[4]) * 256) + Integer.parseInt(portCommand[5].replaceAll("\r\n", ""));
        }
        return port;
    }


    private static String appendZero(String str) {
        if (str.length() == 1) {
            return "0" + str;
        } else {
            return str;
        }
    }

}
