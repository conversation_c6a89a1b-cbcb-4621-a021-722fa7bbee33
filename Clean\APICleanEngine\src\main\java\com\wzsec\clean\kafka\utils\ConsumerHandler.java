package com.wzsec.clean.kafka.utils;

import com.wzsec.clean.common.utils.ConfigurationManager;
import org.apache.kafka.clients.consumer.*;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class ConsumerHandler {
    //kafka消费对象
    private KafkaConsumer<Object, Object> consumer;
    //线程池对象
    private ExecutorService executors;
    @Autowired
    private RestHighLevelClient client;

    //kafka属性配置()
    public static Properties initConfig() {
        String username = ConfigurationManager.getProperty("kafka.ssl.username").trim();
        String password = ConfigurationManager.getProperty("kafka.ssl.password").trim();
        String jaasTemplate = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";
        String jaasCfg = String.format(jaasTemplate, username, password);
        Properties props = new Properties();
        props.put("bootstrap.servers", ConfigurationManager.getProperty("spring.kafka.bootstrap-servers").trim());
        props.put("group.id", ConfigurationManager.getProperty("spring.kafka.consumer.group-id").trim());
        props.put("enable.auto.commit", "true");
        props.put("auto.commit.interval.ms", "1000");
        props.put("session.timeout.ms", "30000");
        props.put("auto.offset.reset","earliest") ;
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("security.protocol", "SASL_PLAINTEXT");
        props.put("sasl.mechanism", "SCRAM-SHA-256");
        props.put("sasl.jaas.config", jaasCfg);

        return props;
    }

    //初始化kafka连接
    @PostConstruct
    public void initKafkaConfig() {
        Properties properties = initConfig();
        consumer = new KafkaConsumer<>(properties);
        consumer.subscribe(Collections.singleton(ConfigurationManager.getProperty("spring.kafka.consumer.group-id").trim()));
    }

    /**
     * 多线程消费kafka数据
     *
     * @param workerNum
     */
    public void execute(int workerNum) {
        executors = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

        while (true) {
            ConsumerRecords<Object, Object> consumerRecords = consumer.poll(100);
            if (!consumerRecords.isEmpty()) {
                for (final ConsumerRecord record : consumerRecords) {
                    executors.submit(new Worker(record, client));
                }
            }
        }
    }
}

