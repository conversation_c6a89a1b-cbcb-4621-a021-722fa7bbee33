package com.wzsec.clean;

import cn.hutool.core.lang.Console;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.DateUtils;
import com.wzsec.clean.modules.model.PcapFlowCombination;
import org.apache.commons.lang3.StringUtils;
import org.pcap4j.core.*;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.TcpPacket;
import org.pcap4j.packet.namednumber.IpNumber;
import org.pcap4j.packet.namednumber.TcpPort;

import java.io.*;
import java.net.Inet4Address;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.zip.DeflaterInputStream;
import java.util.zip.GZIPInputStream;

/**
 * pcap读取用例
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
public class PcapOffline {
    public static void main(String[] args) {
        // 请替换为你的 pcap 文件路径
        String pcapFilePath = "E:\\pcap\\pcapcleanbeforepath\\2022_11_09_23_48_49.pcap";

        Map<String, PcapFlowCombination> pcapFlowCombinationMap = new HashMap<>();
        packetReadCleaning(pcapFilePath, pcapFlowCombinationMap);

        int counter = 0;
        int totalNumber = 0;
        for (Map.Entry<String, PcapFlowCombination> stringPcapFlowCombinationEntry : pcapFlowCombinationMap.entrySet()) {

            PcapFlowCombination value = stringPcapFlowCombinationEntry.getValue();

            String completeData_resp = value.getResponse_data();
            String completeData_req = value.getRequest_data();

            if (StringUtils.isNotBlank(completeData_resp) && StringUtils.isNotBlank(completeData_req)) {
                try {
                    String[] split_resp = value.getResponse_data().split("\r\n\r\n");
                    String Data_resp = split_resp[1]; // 体


                    String[] split_req = value.getRequest_data().split("\r\n\r\n");
                    String Data_req = split_req[1]; // 体

                    double ratio = (double) (completeData_resp.length() + completeData_req.length()) / (Data_resp.length() + Data_req.length());
                    Console.log((int) (ratio));
                    totalNumber = totalNumber + (int) (ratio );
                    counter++;
                } catch (Exception e) {
                }
            }

        }

        Console.log("采用完整计数: {}, 采用总数: {}, 均值为: {}", counter, totalNumber, (double)totalNumber / counter);

        Console.log("检测完成...");

    }

    /**
     * 数据包读取清理
     *
     * @param pcapFilePath           pcap文件路径
     * @param pcapFlowCombinationMap pcap流量组合图
     */
    public static void packetReadCleaning(String pcapFilePath, Map<String, PcapFlowCombination> pcapFlowCombinationMap) {
        try (PcapHandle handle = Pcaps.openOffline(pcapFilePath)) {
            handle.setFilter("tcp", BpfProgram.BpfCompileMode.OPTIMIZE);
            while (true) {
                try {
                    Packet packet = handle.getNextPacketEx();
                    Timestamp timestamp = handle.getTimestamp();
                    String requestTime = DateUtils.timestampToString(timestamp, "yyyy-MM-dd HH:mm:ss");
                    processPacket(packet, requestTime, pcapFlowCombinationMap);

                } catch (EOFException e) {
                    break; // 到达文件结尾
                } catch (TimeoutException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        } catch (PcapNativeException | NotOpenException e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理数据包
     *
     * @param packet                 小包裹
     * @param requestTime            请求时间
     * @param pcapFlowCombinationMap pcap流量组合图
     */
    private static void processPacket(Packet packet, String requestTime, Map<String, PcapFlowCombination> pcapFlowCombinationMap) throws IOException {


        if (packet.contains(IpV4Packet.class)) {
            IpV4Packet ipV4Packet = packet.get(IpV4Packet.class);
            if (ipV4Packet != null && ipV4Packet.getHeader() != null) {
                Inet4Address srcAddr = ipV4Packet.getHeader().getSrcAddr();
                Inet4Address dstAddr = ipV4Packet.getHeader().getDstAddr();

                IpNumber protocol = ipV4Packet.getHeader().getProtocol();
                if (protocol.equals(IpNumber.TCP)) {
                    TcpPacket tcpPacket = packet.get(TcpPacket.class);

                    if (tcpPacket != null && tcpPacket.getPayload() != null) {

                        TcpPort srcPort = tcpPacket.getHeader().getSrcPort();
                        TcpPort dstPort = tcpPacket.getHeader().getDstPort();

                        byte[] payload = tcpPacket.getPayload().getRawData();
                        String content = new String(payload, StandardCharsets.UTF_8);

                        String seq_number = String.valueOf(tcpPacket.getHeader().getSequenceNumberAsLong());
                        String ack_number = String.valueOf(tcpPacket.getHeader().getAcknowledgmentNumberAsLong());

                        if (content.contains(Const.HTTP_SIGN) && !content.contains("OPTIONS")) {
                            // 判断请求头
                            if (content.contains("Accept") && (content.contains("GET") || content.contains("POST") || content.contains("PUT"))) {
                                PcapFlowCombination pcapFlowCombination = new PcapFlowCombination();
                                pcapFlowCombination.setRequest_time(requestTime);
                                pcapFlowCombination.setRequest_data(content); //请求体
                                pcapFlowCombination.setRequest_bytes(payload); //请求体字节
                                // 获取源和目标IP地址
                                pcapFlowCombination.setClient_ip(dstAddr.getHostAddress()); //客户端IP
                                pcapFlowCombination.setServer_ip(srcAddr.getHostAddress()); //服务端IP
                                // 获取源和目标端口
                                pcapFlowCombination.setServer_port(String.valueOf(srcPort.valueAsInt())); //服务端端口
                                pcapFlowCombination.setClient_port(String.valueOf(dstPort.valueAsInt())); //客户端端口

                                String request_url = extractUrl(content);
                                pcapFlowCombination.setRequest_url(request_url);

                                pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                            }
                            // 判断响应头
                            if (content.trim().startsWith("HTTP/1.") || content.trim().startsWith("HTTP/2.")) {
                                // 请求对应响应更新到Map集合
                                if (pcapFlowCombinationMap.containsKey(seq_number)) {
                                    PcapFlowCombination pcapFlowCombination = pcapFlowCombinationMap.get(seq_number);
                                    pcapFlowCombination.setResponse_data(content); //响应体
                                    pcapFlowCombination.setResponse_bytes(payload); //响应体字节
                                    pcapFlowCombinationMap.remove(seq_number);

                                    // System.out.println(Arrays.toString(payload));

                                    String responseString = new String(payload, StandardCharsets.UTF_8);

                                    // 找到响应头和响应体的分隔点
                                    int indexOfSeparator = responseString.indexOf("\r\n\r\n");

                                    if (indexOfSeparator != -1) {
                                        // 提取响应头的内容
                                        String responseHeader = responseString.substring(0, indexOfSeparator);

                                        // 提取响应体的内容
                                        String response = responseString.substring(indexOfSeparator + 4); // 4 是 "\r\n\r\n" 的长度

                                        byte[] responseBody = response.getBytes();


                                        // 根据 Content-Encoding 判断是否需要解压缩
                                        if (responseHeader.contains("Content-Encoding: gzip")) {
                                            // 使用 GZIP 解压缩
                                            // System.out.println(Arrays.toString(responseBody));


                                            // 找到GZIP数据的起始位置
                                            int gzipStartIndex = findGZIPStartIndex(payload);

                                            if (gzipStartIndex != -1) {
                                                // 从gzipStartIndex开始截取GZIP数据
                                                byte[] gzipData = new byte[payload.length - gzipStartIndex];
                                                System.arraycopy(payload, gzipStartIndex, gzipData, 0, gzipData.length);

                                                // 解压缩GZIP数据
                                                byte[] uncompressedData = decompressGZIP(gzipData);

                                                // 处理解压缩后的数据，例如将字节数组转换为字符串
                                                String result = new String(uncompressedData);
                                                // System.out.println("GZIP数据: " + result);
                                            } else {
                                                // System.out.println("GZIP数据未找到");
                                            }


                                        } else if (responseHeader.contains("Content-Encoding: deflate")) {
                                            // 使用 Deflate 解压缩
                                            responseBody = decompressDeflate(responseBody);
                                        }
                                        // 将解压缩后的字节数组转换为字符串
                                        String decodedResponse = new String(responseBody, "UTF-8");

                                        // 输出解压缩后的内容
                                        // System.out.println("解压缩后的内容: " + decodedResponse);
                                    } else {
                                        // System.out.println("找不到响应头和响应体的分隔点");
                                    }

                                    // System.out.println(Arrays.toString(payload));
                                    // System.out.println("-------------------------");


                                    pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                                }
                            }
                        } else {
                            //对不完整响应进行组装
                            PcapFlowCombination pcapFlowCombination = pcapFlowCombinationMap.get(ack_number);
                            if (pcapFlowCombinationMap.containsKey(ack_number)) {
                                String response_data = pcapFlowCombination.getResponse_data();

                                if (StringUtils.isNotBlank(response_data)) {
                                    if (response_data.startsWith("HTTP/1.") || response_data.startsWith("HTTP/2.")) {
                                        //如果包含key值,需要对value进行更新,同时将content_byte进行组装
                                        byte[] bytes = combineBytes(pcapFlowCombination.getResponse_bytes(), payload);
                                        pcapFlowCombination.setResponse_bytes(bytes);
                                        pcapFlowCombination.setResponse_data(pcapFlowCombination.getResponse_data() + content);
                                        pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                                    }
                                } else {
                                    //对不完整响应进行组装 上传 ask对应
                                    //如果包含key值,需要对value进行更新,同时将content_byte进行组装
                                    byte[] bytes = combineBytes(pcapFlowCombination.getRequest_bytes(), payload);
                                    pcapFlowCombination.setRequest_bytes(bytes);
                                    pcapFlowCombination.setRequest_data(pcapFlowCombination.getRequest_data() + content);
                                    pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    // 找到GZIP数据的起始位置
    private static int findGZIPStartIndex(byte[] data) {
        for (int i = 0; i < data.length - 1; i++) {
            if (data[i] == 31 && data[i + 1] == -117) {
                return i;
            }
        }
        return -1;
    }

    // 解压缩GZIP数据
    private static byte[] decompressGZIP(byte[] gzipData) {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(gzipData);
             GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }


    private static byte[] decompressDeflate(byte[] compressedData) throws IOException {
        try (InputStream inputStream = new ByteArrayInputStream(compressedData);
             DeflaterInputStream deflateInputStream = new DeflaterInputStream(inputStream);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;

            // 从 Deflate 输入流读取数据并写入输出流
            while ((bytesRead = deflateInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 返回解压缩后的字节数组
            return outputStream.toByteArray();
        }
    }

    /**
     * 将两个字节数组组装成一个新的字节数组
     *
     * @param bytes1 字节1
     * @param bytes2 字节2
     * @return {@link byte[]}
     */
    private static byte[] combineBytes(byte[] bytes1, byte[] bytes2) {
        byte[] combinedBytes = new byte[bytes1.length + bytes2.length];
        System.arraycopy(bytes1, 0, combinedBytes, 0, bytes1.length);
        System.arraycopy(bytes2, 0, combinedBytes, bytes1.length, bytes2.length);
        return combinedBytes;
    }

    /**
     * 提取url
     *
     * @param httpRequest http请求
     * @return {@link String}
     */
    private static String extractUrl(String httpRequest) {
        // 将请求按行分割
        String[] lines = httpRequest.split("\n");

        // 提取请求行
        String requestLine = lines[0].trim();

        // 分割请求行，提取 URL 部分
        String[] parts = requestLine.split("\\s+");
        if (parts.length >= 2) {
            return parts[1];
        } else {
            return null;
        }
    }


}
