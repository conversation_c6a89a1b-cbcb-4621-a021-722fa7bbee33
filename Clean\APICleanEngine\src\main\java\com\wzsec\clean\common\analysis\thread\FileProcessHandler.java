package com.wzsec.clean.common.analysis.thread;

import com.wzsec.clean.modules.model.PcapTask;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @ClassName: FileProcessHandler
 * @Description: 文件处理接口
 * @date 2020年11月06日
 */
public interface FileProcessHandler {

    /**
     * 线程具体执行方法
     *
     * @param filePath           文件名称（完整地址）
     * @param countDownLatch     线程管理
     * @param countDownLatchAll  总线程管理
     * @param pcapCleanAfterPath pcap清洗后路径
     * @param checktime          任务开始时间
     * @param pcapTask           pcap任务
     * <AUTHOR>
     * @date 2019年7月31日
     */
    void exec(String filePath, CountDownLatch countDownLatch, String pcapCleanAfterPath,
              CountDownLatch countDownLatchAll, String checktime, PcapTask pcapTask);

}
