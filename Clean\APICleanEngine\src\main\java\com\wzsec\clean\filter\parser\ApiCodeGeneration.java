package com.wzsec.clean.filter.parser;

import cn.hutool.core.date.DateUtil;
import com.wzsec.clean.common.rule.IcLogCheckTaskClean;
import com.wzsec.clean.common.rule.SensitiveDataDiscovery;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.filter.clean.GuanWang.GuanWangFlowClean;
import com.wzsec.clean.modules.dao.ApiDao;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.PcapService;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 接口编码生成(含检测更新接口信息表)
 *
 * <AUTHOR>
 * @date 2024-01-02
 */
public class ApiCodeGeneration {

    private static PcapService pcapService = SpringUtils.getApplicationContext().getBean(PcapService.class);

    private static final Object lock = new Object();

    // 输入限制
    private static final int MAX_INPUT_LENGTH = 10000;


    public static String getInterfaceCode(String interfaceuri,
                                          String uri,
                                          PcapFlowCombination pcapFlowCombination,
                                          List<String> apiList,
                                          Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                          List<String> interfaceDiscoveryList) throws Exception {

        String apicode = MD5Util.encrypt(uri);

        // TODO 该处存在共享资源问题,为保证线程安全,该处写库逻辑加入锁
        synchronized (lock) {
            // TODO 从接口流量中截取接口编码标识
            String collectdatatype = ConfigurationManager.getProperty("netflow.collectdatatype").trim();

            if (Const.FLOW_DATA_general.equals(collectdatatype)) {  //通用
                uri = getInterfaceURI(interfaceuri); //获取URI

                if (!interfaceDiscoveryList.contains(apicode)) {
                    insertApiDiscovery(icInterfaceInfoMap, pcapFlowCombination, uri);
                    interfaceDiscoveryList.add(apicode);
                }

            } else if (Const.FLOW_DATA_GuanWang.equals(collectdatatype)) {  //国家管网
                // TODO 插入接口信息表及接口发现表抽取
                uri = addPipelineInterface(interfaceuri, pcapFlowCombination, apiList, icInterfaceInfoMap, interfaceDiscoveryList, apicode, collectdatatype);

            } else if (Const.FLOW_DATA_SECURITY_PLATFORM.equals(collectdatatype)) { //数安平台(为避免对管网产生影响,该处抽取)
                // 数安平台
                uri = getInterfaceURI(interfaceuri); //获取URI
                if (!interfaceDiscoveryList.contains(apicode)) {
                    insertApiDiscovery_SecurityPlatform(icInterfaceInfoMap, pcapFlowCombination, uri);
                    interfaceDiscoveryList.add(apicode);
                }
            } else if (Const.FLOW_DATA_QinHai.equals(collectdatatype)) {  //青海

                if (!apiList.contains(apicode)) {
                    apiList.add(apicode);
                    IcInterfaceInfo icInterfaceInfo = getIcInterfaceInfo(interfaceuri, uri, pcapFlowCombination, apicode);
                    if (pcapService.queryNumberInterfaces(apicode) == 0) {
                        pcapService.saveIcInterfaceinfoByQinHai(icInterfaceInfo);
                    }
                }

            } else if (Const.FLOW_DATA_Suzhou.equals(collectdatatype)) {
                //苏州
                apicode = getSuzhouApicode(interfaceuri);
                if (apiList.size() > 0 && !apiList.contains(apicode)) {
                    insertApiDiscovery(icInterfaceInfoMap, pcapFlowCombination, uri);
                    //如果没有接口编码不写入es
                    return null;
                }
            } else if (Const.FLOW_DATA_Jinhua.equals(collectdatatype)) {
                //金华
                uri = getJinhuaURI(interfaceuri); //获取URI
                if (uri.contains(".htm")) {
                    apicode = uri.substring(0, uri.indexOf("."));
                }
            } else if (Const.FLOW_DATA_Yongkang.equals(collectdatatype)) {
                //永康
                uri = getYongkangURI(interfaceuri); //获取URI
                apicode = pcapService.queryInterfaceCoding(MD5Util.encrypt(uri));
                if (StringUtils.isEmpty(apicode)) {
                    apicode = MD5Util.encrypt(uri);
                }
            } else if (Const.FLOW_DATA_ZhiWangKeJi.equals(collectdatatype)) {  //智网科技

                if (!apiList.contains(apicode)) {
                    apiList.add(apicode);

                    IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
                    icInterfaceInfo.setApicode(apicode);//接口编码
                    icInterfaceInfo.setSparefield4(uri);//URL

//                    icInterfaceInfo.setApiport(pcapFlowCombination.getServer_port());
//                    icInterfaceInfo.setApiip(pcapFlowCombination.getServer_ip());

                    icInterfaceInfo.setApiport(pcapFlowCombination.getClient_ip());
                    icInterfaceInfo.setApiip(pcapFlowCombination.getClient_port());

                    icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
                    icInterfaceInfo.setSparefield2(pcapFlowCombination.getReq_method());//请求类似 get post
                    icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式

                    // 数组越界处理
                    if (interfaceuri.contains("?")) {
                        String[] split = interfaceuri.split("\\?", 2); // 使用限制分割次数为2，以处理参数中包含问号的情况
                        icInterfaceInfo.setUrl(split[0]);              // 接口地址
                        icInterfaceInfo.setReq_example(split.length > 1 ? split[1] : ""); // 请求示例，如果没有参数则为空字符串
                    } else {
                        icInterfaceInfo.setUrl(interfaceuri);           // 接口地址
                        icInterfaceInfo.setReq_example("");             // 请求示例为空字符串
                    }

                    icInterfaceInfo.setDeleted("0"); // TODO 是否删除
                    icInterfaceInfo.setSparefield1("1"); //TODO 默认为1

                    icInterfaceInfo.setSyncstate(Const.INTERFACE_INFORMATION_SOURCE_DISCOVER);//TODO 默认为 2 接口发现
                    icInterfaceInfo.setCreatetime(TimeUtils.getReqTime());
                    if (pcapService.queryNumberInterfaces(apicode) == 0) {
                        pcapService.saveIcInterfaceinfoByQinHai(icInterfaceInfo);
                    }
                }
            } else if (Const.FLOW_DATA_UNIVERSALWRITE.equals(collectdatatype)) { //TODO 通用[清洗接口信息写入规范和发现表]
                if (!apiList.contains(apicode)) {
                    apiList.add(apicode);
                    //入库接口信息表
                    IcInterfaceInfo icInterfaceInfo = getIcInterfaceInfo(interfaceuri, uri, pcapFlowCombination, apicode);
                    if (pcapService.queryNumberInterfaces(apicode) == 0) {
                        pcapService.saveIcInterfaceinfoByQinHai(icInterfaceInfo);
                        //入库接口发现表
                        insertApiDiscoveryBeCommon(icInterfaceInfoMap, pcapFlowCombination, uri);
                    }
                }
            } else if (Const.FLOW_DATA_ShangFei.equals(collectdatatype)) { //TODO 商飞
                if (!apiList.contains(apicode)) {
                    apiList.add(apicode);
                    //入库接口信息表
                    IcInterfaceInfo icInterfaceInfo = getIcInterfaceInfo(interfaceuri, uri, pcapFlowCombination, apicode);
                    if (pcapService.queryNumberInterfaces(apicode) == 0) {
                        pcapService.saveIcInterfaceinfoByQinHai(icInterfaceInfo);
                        //入库接口发现表
                        insertApiDiscoveryBeCommon(icInterfaceInfoMap, pcapFlowCombination, uri);
                    }
                }
            }

            //TODO 苏州需截取url中接口编码 如果不存在则写入接口发现表中 其他版本写入接口信息表
            // 根据配置文件设置,是否将新增接口写入接口信息表
            String insertInterfaceInfoStatus = ConfigurationManager.getProperty("insertInterfaceInfo.status").trim();
            if (Const.STATUS_ON.equals(insertInterfaceInfoStatus) &&
                    !Const.FLOW_DATA_Suzhou.equals(collectdatatype)
                    && !Const.FLOW_DATA_Jinhua.equals(collectdatatype)
                    && !Const.FLOW_DATA_Yongkang.equals(collectdatatype)) {
                if (!apiList.contains(apicode)) {
                    IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
                    apiList.add(apicode);
                    icInterfaceInfo.setApicode(apicode);//接口编码
                    icInterfaceInfo.setSparefield4(uri);//URL
                    icInterfaceInfo.setApiport(pcapFlowCombination.getClient_ip());
                    icInterfaceInfo.setApiip(pcapFlowCombination.getClient_port());
                    icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
                    icInterfaceInfo.setSparefield2(pcapFlowCombination.getReq_method());//请求类似 get post
                    icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
                    icInterfaceInfo.setUrl(interfaceuri);//接口地址
                    icInterfaceInfo.setSyncstate(Const.INTERFACE_INFORMATION_SOURCE_DISCOVER);//TODO 默认为 2 接口发现
                    icInterfaceInfo.setCreatetime(TimeUtils.getReqTime());
                    if (Const.FLOW_DATA_SECURITY_PLATFORM.equals(collectdatatype)) { // TODO 数安平台接口ip,端口插入接口信息表
                        icInterfaceInfo.setApiip(pcapFlowCombination.getClient_ip()); // 数安平台_接口ip
                        icInterfaceInfo.setApiport(pcapFlowCombination.getClient_port()); // 数安平台_接口端口
                    }
                    pcapService.saveIcInterfaceinfo(icInterfaceInfo);
                }
            }
        }
        return apicode;
    }

    /**
     * [国家管网] 新增接口插入接口信息表及接口发现表
     *
     * @param interfaceuri           接口
     * @param pcapFlowCombination    pcap流量组合
     * @param apiList                api列表
     * @param icInterfaceInfoMap     ic接口信息
     * @param interfaceDiscoveryList 接口发现
     * @param apicode                接口编码
     * @param collectdatatype        数据类型
     * @return {@link String }
     * @throws Exception 例外
     */
    private static String addPipelineInterface(String interfaceuri, PcapFlowCombination pcapFlowCombination, List<String> apiList, Map<String, IcInterfaceInfo> icInterfaceInfoMap, List<String> interfaceDiscoveryList, String apicode, String collectdatatype) throws Exception {
        String uri;
        uri = getInterfaceURI(interfaceuri); //获取URI

        // 新增接口写入接口发现表(client_ip作为接口IP)
        if (!interfaceDiscoveryList.contains(apicode)) {
            insertApiDiscoveryNegation(icInterfaceInfoMap, pcapFlowCombination, uri);
            interfaceDiscoveryList.add(apicode);
        }

        // 新增接口写入接口信息表(client_ip作为接口IP)
        if (!apiList.contains(apicode)) {
            IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
            apiList.add(apicode);
            icInterfaceInfo.setApicode(apicode);//接口编码
            icInterfaceInfo.setSparefield4(uri);//URL

            //TODO 管网接口审计采集的接口流量内网IP、弹性IP关联转换
//            icInterfaceInfo.setApiip(pcapFlowCombination.getClient_ip());
            icInterfaceInfo.setApiip(GuanWangFlowClean.networkSegmentMatching(pcapFlowCombination.getClient_ip()));

            icInterfaceInfo.setApiport(pcapFlowCombination.getClient_port());
            icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
            icInterfaceInfo.setSparefield2(pcapFlowCombination.getReq_method());//请求类似 get post
            icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
            icInterfaceInfo.setUrl(interfaceuri);//接口地址
            icInterfaceInfo.setSyncstate(Const.INTERFACE_INFORMATION_SOURCE_DISCOVER);//TODO 默认为 2 接口发现
            icInterfaceInfo.setCreatetime(TimeUtils.getReqTime());
            ApiDao.saveInterfaceInfoByPipeline(icInterfaceInfo);
        }
        return uri;
    }

    private static IcInterfaceInfo getIcInterfaceInfo(String interfaceuri, String uri, PcapFlowCombination pcapFlowCombination, String apicode) {
        IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
        icInterfaceInfo.setApicode(apicode);//接口编码
        icInterfaceInfo.setSparefield4(uri);//URL

//                    icInterfaceInfo.setApiport(pcapFlowCombination.getServer_port());
//                    icInterfaceInfo.setApiip(pcapFlowCombination.getServer_ip());

        icInterfaceInfo.setApiport(pcapFlowCombination.getClient_port());
        icInterfaceInfo.setApiip(pcapFlowCombination.getClient_ip());

        icInterfaceInfo.setInputparams("request_data"); //输入参数
        icInterfaceInfo.setOutputparams("response_data"); //输出参数

        icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
        icInterfaceInfo.setSparefield2(pcapFlowCombination.getReq_method());//请求类似 get post
        icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式

        // if (interfaceuri.contains("?")) {
        //     String[] split = interfaceuri.split("\\?");
        //     icInterfaceInfo.setUrl(split[0]);//接口地址
        //     icInterfaceInfo.setReq_example(split[1]);//请求示例
        // } else {
        //     icInterfaceInfo.setUrl(interfaceuri);//接口地址
        // }

        // 数组越界处理
        if (interfaceuri.contains("?")) {
            String[] split = interfaceuri.split("\\?", 2); // 使用限制分割次数为2，以处理参数中包含问号的情况
            icInterfaceInfo.setUrl(split[0]);              // 接口地址
            icInterfaceInfo.setReq_example(split.length > 1 ? split[1] : ""); // 请求示例，如果没有参数则为空字符串
        } else {
            icInterfaceInfo.setUrl(interfaceuri);           // 接口地址
            icInterfaceInfo.setReq_example("");             // 请求示例为空字符串
        }

        icInterfaceInfo.setDeleted("0"); // TODO 是否删除
        icInterfaceInfo.setSparefield1("1"); //TODO 默认为1

        icInterfaceInfo.setSyncstate(Const.INTERFACE_INFORMATION_SOURCE_DISCOVER);//TODO 默认为 2 接口发现
        icInterfaceInfo.setCreatetime(TimeUtils.getReqTime());
        return icInterfaceInfo;
    }


    /**
     * 获取接口URI - 通用
     *
     * @param interfaceuri 接口
     * @return {@link String}
     */
    private static String getInterfaceURI(String interfaceuri) {
        String uri = interfaceuri;
        //通过uri生成接口编码插入更新接口信息表
        if (interfaceuri.contains("?")) {
            uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
        }
        return uri;
    }


    /**
     * 苏州从接口URL获取接口编码
     *
     * @param url url
     * @return {@link String}
     */
    private static String getSuzhouApicode(String url) {
        String apicode = "";
        if (url.contains("?")) {
            int indexOfQuestionMark = url.indexOf('?');
            if (indexOfQuestionMark != -1) {
                String partBeforeQuestionMark = url.substring(0, indexOfQuestionMark);
                String lastSegment = String.valueOf(partBeforeQuestionMark.lastIndexOf('/') + 1);
                return partBeforeQuestionMark.substring(Integer.parseInt(lastSegment));
            } else {
                return url;
            }
        } else {
            int lastSlashIndex = url.lastIndexOf('/');
            apicode = url.substring(lastSlashIndex + 1);
        }
        return apicode;
    }

    /**
     * 获取接口URI - 金华
     *
     * @param interfaceuri 接口
     * @return {@link String}
     */
    private static String getJinhuaURI(String interfaceuri) {
        String uri = interfaceuri;
        //通过uri生成接口编码插入更新接口信息表
        if (interfaceuri.contains(".htm") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")
                && !interfaceuri.contains(".php") && !interfaceuri.contains(".db")
                && !interfaceuri.contains(".sql") && !interfaceuri.contains(".key")
                && !interfaceuri.contains(".com") && !interfaceuri.contains(".tar.z")
                && !interfaceuri.contains(".gz") && !interfaceuri.contains(".cgj")
                && !interfaceuri.contains(".tar.Z") && !interfaceuri.contains("http")) {
            uri = interfaceuri.substring(interfaceuri.lastIndexOf("/") + 1);
        }
        return uri;
    }

    /**
     * 获取接口URI - 永康
     *
     * @param interfaceuri 接口
     * @return {@link String}
     */
    private static String getYongkangURI(String interfaceuri) {
        String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
        //通过uri生成接口编码插入更新接口信息表
        if (interfaceuri.contains("?") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")
                && !interfaceuri.contains(".php") && !interfaceuri.contains(".db")
                && !interfaceuri.contains(".sql") && !interfaceuri.contains(".key")
                && !interfaceuri.contains(".com") && !interfaceuri.contains(".tar.z")
                && !interfaceuri.contains(".gz") && !interfaceuri.contains(".cgj")
                && !interfaceuri.contains(".tar.Z") && !interfaceuri.contains("http")) {
            /**
             * 接口里面是 <接口路径>
             * 捕获流量是 <完整路径= 基础路径+接口路径>
             */
            uri = uri.replaceFirst(Const.FLOW_CLEANING_FOUNDATIONPATH, "").trim();
        }
        return uri;
    }


    /**
     * 入库接口发现表
     *
     * @param icInterfaceInfoMap  ic接口信息图
     * @param pcapFlowCombination pcap流量组合
     * @param uri                 uri
     * @throws NoSuchAlgorithmException     没有这样算法例外
     * @throws UnsupportedEncodingException 不支持编码异常
     */
    private static void insertApiDiscovery(Map<String, IcInterfaceInfo> icInterfaceInfoMap, PcapFlowCombination pcapFlowCombination, String uri) throws Exception {
        Apidiscovery apidiscovery = new Apidiscovery();
        apidiscovery.setApicode(MD5Util.encrypt(uri));//接口编码
        apidiscovery.setApiname(icInterfaceInfoMap.get(MD5Util.encrypt(uri)) == null ? "" : icInterfaceInfoMap.get(MD5Util.encrypt(uri)).getApiname());
        apidiscovery.setUrl(uri);
//        apidiscovery.setApiport(pcapFlowCombination.getServer_port());
//        apidiscovery.setApiip(pcapFlowCombination.getServer_ip());
        apidiscovery.setApiport(pcapFlowCombination.getClient_port());
        apidiscovery.setApiip(pcapFlowCombination.getClient_ip());

        String rescontent = IcLogCheckTaskClean.getResponseParaValue(pcapFlowCombination.getResponse_body_data());//响应
        String reqcontent = IcLogCheckTaskClean.getRequestParaValue(pcapFlowCombination.getRequest_body_data());//请求

        String resRisk = optimizedAssetDiscovery(apidiscovery, pcapFlowCombination);

        apidiscovery.setReq_example(reqcontent);
        apidiscovery.setRes_example(rescontent);
        apidiscovery.setRisk(resRisk);
        apidiscovery.setAccessdomain(Const.API_ACCESSDOMAIN_INTRANET_CHARS);
        apidiscovery.setApistatus(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);
        apidiscovery.setInserttime(DateUtil.now());
        apidiscovery.setSparefield1(pcapFlowCombination.getClient_ip() + ":" + pcapFlowCombination.getClient_port());
        ApiDao.saveApidiscovery(apidiscovery);
    }


    /**
     * TODO  入库通用版接口发现表
     *
     * @param icInterfaceInfoMap  ic接口信息图
     * @param pcapFlowCombination pcap流量组合
     * @param uri                 uri
     */
    private static void insertApiDiscoveryBeCommon(Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                                   PcapFlowCombination pcapFlowCombination,
                                                   String uri) throws Exception {
        Apidiscovery apidiscovery = new Apidiscovery();
        apidiscovery.setApicode(MD5Util.encrypt(uri));//接口编码
        apidiscovery.setApiname(icInterfaceInfoMap.get(MD5Util.encrypt(uri)) == null ? "" : icInterfaceInfoMap.get(MD5Util.encrypt(uri)).getApiname());
        apidiscovery.setUrl(uri);
        apidiscovery.setApiport(pcapFlowCombination.getClient_port());
        apidiscovery.setApiip(pcapFlowCombination.getClient_ip());

        String rescontent = IcLogCheckTaskClean.getResponseParaValue(pcapFlowCombination.getResponse_body_data());//响应
        String reqcontent = IcLogCheckTaskClean.getRequestParaValue(pcapFlowCombination.getRequest_body_data());//请求

        String resRisk = optimizedAssetDiscovery(apidiscovery, pcapFlowCombination);

        apidiscovery.setReq_example(reqcontent);
        apidiscovery.setRes_example(rescontent);
        apidiscovery.setRisk(resRisk);
        apidiscovery.setAccessdomain(Const.API_ACCESSDOMAIN_INTRANET_CHARS);
        apidiscovery.setApistatus(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);
        apidiscovery.setInserttime(DateUtil.now());
        apidiscovery.setSparefield1(pcapFlowCombination.getClient_ip() + ":" + pcapFlowCombination.getClient_port());
        ApiDao.saveApiDiscoveryBeCommon(apidiscovery);
    }


    /**
     * @description 入库接口加密流量表
     * <AUTHOR>
     * @date 2025-01-17
     */
    public static void insertApiEncrypteDtraffic(List<ApiEncryptedtraffic> apiEncryptedtrafficList) {
        for (ApiEncryptedtraffic apiEncryptedtraffic : apiEncryptedtrafficList) {
            ApiDao.saveApiEncrypteDtraffic(apiEncryptedtraffic);
        }
    }

    /**
     * [国家管网]入库接口发现表
     *
     * @param icInterfaceInfoMap  ic接口信息图
     * @param pcapFlowCombination pcap流量组合
     * @param uri                 uri
     * @throws NoSuchAlgorithmException     没有这样算法例外
     * @throws UnsupportedEncodingException 不支持编码异常
     */
    private static void insertApiDiscoveryNegation(Map<String, IcInterfaceInfo> icInterfaceInfoMap, PcapFlowCombination pcapFlowCombination, String uri) throws Exception {
        Apidiscovery apidiscovery = new Apidiscovery();
        apidiscovery.setApicode(MD5Util.encrypt(uri));//接口编码
        apidiscovery.setApiname(icInterfaceInfoMap.get(MD5Util.encrypt(uri)) == null ? "" : icInterfaceInfoMap.get(MD5Util.encrypt(uri)).getApiname());
        apidiscovery.setUrl(uri);

        //TODO 管网接口审计采集的接口流量内网IP、弹性IP关联转换
//        apidiscovery.setApiip(pcapFlowCombination.getClient_ip()); //接口IP
        apidiscovery.setApiip(GuanWangFlowClean.networkSegmentMatching(pcapFlowCombination.getClient_ip())); //接口IP

        apidiscovery.setApiport(pcapFlowCombination.getClient_port()); // 接口端口
        String rescontent = IcLogCheckTaskClean.getResponseParaValue(pcapFlowCombination.getResponse_body_data());//响应
        String reqcontent = IcLogCheckTaskClean.getRequestParaValue(pcapFlowCombination.getRequest_body_data());//请求

        String resRisk = optimizedAssetDiscovery(apidiscovery, pcapFlowCombination);
        apidiscovery.setReq_example(removeUnwantedCharacters(reqcontent));
        apidiscovery.setRes_example(removeUnwantedCharacters(rescontent));

        apidiscovery.setRisk(resRisk);
        apidiscovery.setAccessdomain(Const.API_ACCESSDOMAIN_INTRANET_CHARS);
        apidiscovery.setApistatus(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);
        apidiscovery.setInserttime(DateUtil.now());
        apidiscovery.setSparefield1(pcapFlowCombination.getClient_ip() + ":" + pcapFlowCombination.getClient_port());
        ApiDao.saveApidiscovery(apidiscovery);

        // 更新接口发现标签
        updateInterfaceDiscoveryLabel(pcapFlowCombination, apidiscovery);
    }

    /**
     * 删除不需要字符
     *
     * @param input 输入
     * @return {@link String }
     */
    public static String removeUnwantedCharacters(String input) {
        // 替换掉 emoji 和控制字符
        String simplifiedData = StringUtils.replacePattern(input, "[\\p{C}]+|[\\x{1F600}-\\x{1F64F}]|[\\x{1F300}-\\x{1F5FF}]|[\\uD83C\\uDC00-\\uD83D\\uDE4F]", "").trim();
        return simplifiedData.length() > MAX_INPUT_LENGTH ? "" : simplifiedData;
    }

    /**
     * 细化接口发现指标
     *
     * @param apidiscovery
     * @return
     */
    private static String optimizedAssetDiscovery(Apidiscovery apidiscovery, PcapFlowCombination pcapFlowCombination) {

        String rescontent = IcLogCheckTaskClean.getResponseParaValue(pcapFlowCombination.getResponse_body_data());//响应
        String reqcontent = IcLogCheckTaskClean.getRequestParaValue(pcapFlowCombination.getRequest_body_data());//请求
        String requestData = pcapFlowCombination.getRequest_data() == null ? "" : pcapFlowCombination.getRequest_data(); //请求内容
        String responseData = pcapFlowCombination.getResponse_data() == null ? "" : pcapFlowCombination.getResponse_data();  //响应内容
        String url = apidiscovery.getUrl();

        //通过请求内容判断
        String[] reqFieldDisassembly = reqcontent.split("。|，|\\\\|\\t|\\\t|\t|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");

        List<String> reqDataToBeIdentifiedList = Arrays.stream(reqFieldDisassembly)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());

        //判断敏感类型
        Set<String> reqSensitiveDataMap = SensitiveDataDiscovery.sensitiveDataStatistics(reqDataToBeIdentifiedList);

        //通过响应内容判断
        String[] resFieldDisassembly = rescontent.split("。|，|\\\\|\\t|\\\t|\t|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");

        List<String> resDataToBeIdentifiedList = Arrays.stream(resFieldDisassembly)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());

        //判断敏感类型
        Set<String> resSensitiveDataMap = SensitiveDataDiscovery.sensitiveDataStatistics(resDataToBeIdentifiedList);
        String resRisk = SensitiveDataDiscovery.alarmLevelDefinition(resSensitiveDataMap);  //告警级别

        apidiscovery.setReqdatatag(StringUtils.join(reqSensitiveDataMap, ","));
        apidiscovery.setResdatatag(StringUtils.join(resSensitiveDataMap, ","));

        //识别JSON、HTML、XML、JSONP格式
        if (FileTools.isHtml(rescontent)) {
            apidiscovery.setDataformat(Const.INTERFACEDISCOVERY_DATAFORMAT_HTML);
        } else if (FileTools.isXML(rescontent)) {
            apidiscovery.setDataformat(Const.INTERFACEDISCOVERY_DATAFORMAT_XML);
        } else if (FileTools.isJsonp(rescontent)) {
            apidiscovery.setDataformat(Const.INTERFACEDISCOVERY_DATAFORMAT_JSONP);
        } else if (FileTools.isJsonValid(rescontent)) {
            apidiscovery.setDataformat(Const.INTERFACEDISCOVERY_DATAFORMAT_JSON);
        }
        apidiscovery.setCategory(Const.INTERFACEDISCOVERY_CATEGORY_DATAUSAGEAPI);


        if (requestData.contains("Content-Type: multipart/form-data;") && requestData.contains("boundary=----")) {
            apidiscovery.setCategory(Const.INTERFACEDISCOVERY_CATEGORY_FILEUPLOADAPI);
        } else if (responseData.contains("Content-Disposition: attachment;")) {
            apidiscovery.setCategory(Const.INTERFACEDISCOVERY_CATEGORY_FILEDOWNLOADAPI);
        } else if (url.endsWith(Const.INTERFACEDISCOVERY_CATEGORY_LOGIN)
                && (requestData.contains(Const.INTERFACEDISCOVERY_CATEGORY_USER)
                || requestData.contains(Const.INTERFACEDISCOVERY_CATEGORY_PASS))) {
            apidiscovery.setCategory(Const.INTERFACEDISCOVERY_CATEGORY_LOGINAPI);
        } else if (url.endsWith(Const.INTERFACEDISCOVERY_CATEGORY_DATA)
                && url.endsWith(Const.INTERFACEDISCOVERY_CATEGORY_QUERY)
                && url.endsWith(Const.INTERFACEDISCOVERY_CATEGORY_SEARCH)) {
            apidiscovery.setCategory(Const.INTERFACEDISCOVERY_CATEGORY_LOGINAPI);
        }

        return resRisk;
    }


    /**
     * 入库接口发现表(数安平台）
     *
     * @param icInterfaceInfoMap  ic接口信息图
     * @param pcapFlowCombination pcap流量组合
     * @param uri                 uri
     * @throws NoSuchAlgorithmException     没有这样算法例外
     * @throws UnsupportedEncodingException 不支持编码异常
     */
    private static void insertApiDiscovery_SecurityPlatform(Map<String, IcInterfaceInfo> icInterfaceInfoMap, PcapFlowCombination pcapFlowCombination, String uri) throws Exception {
        ApiDiscoverySecurityPlatform apiDiscoverySecurityPlatform = new ApiDiscoverySecurityPlatform();
        apiDiscoverySecurityPlatform.setApicode(MD5Util.encrypt(uri));//接口编码
        apiDiscoverySecurityPlatform.setApiname(icInterfaceInfoMap.get(MD5Util.encrypt(uri)) == null ? "" : icInterfaceInfoMap.get(MD5Util.encrypt(uri)).getApiname());
        apiDiscoverySecurityPlatform.setApiip(pcapFlowCombination.getClient_ip()); // TODO 2024.06.27 数安平台取值有误
        apiDiscoverySecurityPlatform.setApiport(pcapFlowCombination.getClient_port()); // TODO 2024.06.27 数安平台取值有误
        apiDiscoverySecurityPlatform.setUrl(uri);
        String reqcontent = IcLogCheckTaskClean.getRequestParaValue(pcapFlowCombination.getRequest_body_data());//请求
        // GET请求对?后参数进行截取并解码,放入请求示例中
        if (pcapFlowCombination.getReq_method().equals(Const.GET_SIGN)) {
            reqcontent = ParamUtil.extractAndDecodeParams(pcapFlowCombination.getRequest_url());
        }
        String rescontent = IcLogCheckTaskClean.getResponseParaValue(pcapFlowCombination.getResponse_body_data());//响应
        apiDiscoverySecurityPlatform.setReq_example(reqcontent);
        // apiDiscoverySecurityPlatform.setRes_example(rescontent); // TODO 注释,响应参数根据需求是否写库
        apiDiscoverySecurityPlatform.setRequestmode(pcapFlowCombination.getReq_method()); // TODO 新增请求方法
        apiDiscoverySecurityPlatform.setApistatus(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);
        apiDiscoverySecurityPlatform.setInserttime(DateUtil.now());
        pcapService.saveApiDiscovery_SecurityPlatform(apiDiscoverySecurityPlatform);
    }


    /**
     * 更新接口发现标签
     *
     * @param pcapFlowCombination Pcap流动组合
     * @param apidiscovery apidiscovery
     */
    private static void updateInterfaceDiscoveryLabel(PcapFlowCombination pcapFlowCombination, Apidiscovery apidiscovery) {

        // 检测API协议类型
        if (pcapFlowCombination != null) {
            List<String> apiProtocolTypeList = ApiProtocolDetector.detectProtocol(pcapFlowCombination);

            // 更新接口发现标签
            for (String apiProtocolType : apiProtocolTypeList) {
                if (StringUtils.isNotBlank(apiProtocolType)) {
                    String labelId = ApiDao.findLabelId(apiProtocolType);
                    if (labelId != null) {
                        ApiDao.saveLabelApi(labelId, apidiscovery.getApicode());
                    }
                }
            }
        }
    }

}
