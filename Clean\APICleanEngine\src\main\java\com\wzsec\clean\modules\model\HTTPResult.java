package com.wzsec.clean.modules.model;


public class HTTPResult {
    private String id;
    private String taskname;//任务号
    private String interfaceuri;//接口名称
    private String protocol;//协议类型
    private String sourceip;//原地址
    private String sourceport;//原端口
    private String desip;//目的地址
    private String desport;//目的端口
    private String totalcount;//接口调用数量
    private String risk;//风险程度
    private String riskcount;//风险程度
    private String checktime;//检测时间
    private String sparefield1;//备用字段1
    private String sparefield2;//备用字段2
    private String sparefield3;//备用字段3
    private String sparefield4;//备用字段4

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskname() {
        return taskname;
    }

    public void setTaskname(String taskname) {
        this.taskname = taskname;
    }

    public String getInterfaceuri() {
        return interfaceuri;
    }

    public void setInterfaceuri(String interfaceuri) {
        this.interfaceuri = interfaceuri;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getSourceip() {
        return sourceip;
    }

    public void setSourceip(String sourceip) {
        this.sourceip = sourceip;
    }

    public String getSourceport() {
        return sourceport;
    }

    public void setSourceport(String sourceport) {
        this.sourceport = sourceport;
    }

    public String getDesip() {
        return desip;
    }

    public void setDesip(String desip) {
        this.desip = desip;
    }

    public String getDesport() {
        return desport;
    }

    public void setDesport(String desport) {
        this.desport = desport;
    }

    public String getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(String totalcount) {
        this.totalcount = totalcount;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public String getRiskcount() {
        return riskcount;
    }

    public void setRiskcount(String riskcount) {
        this.riskcount = riskcount;
    }

    public String getChecktime() {
        return checktime;
    }

    public void setChecktime(String checktime) {
        this.checktime = checktime;
    }

    public String getSparefield1() {
        return sparefield1;
    }

    public void setSparefield1(String sparefield1) {
        this.sparefield1 = sparefield1;
    }

    public String getSparefield2() {
        return sparefield2;
    }

    public void setSparefield2(String sparefield2) {
        this.sparefield2 = sparefield2;
    }

    public String getSparefield3() {
        return sparefield3;
    }

    public void setSparefield3(String sparefield3) {
        this.sparefield3 = sparefield3;
    }

    public String getSparefield4() {
        return sparefield4;
    }

    public void setSparefield4(String sparefield4) {
        this.sparefield4 = sparefield4;
    }
}
