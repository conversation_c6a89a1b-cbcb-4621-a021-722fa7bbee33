package com.wzsec.clean.kafka.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @描述: 网关调用日志bean  由四种日志 RecLog ReqLog RespLog RetMessage 组成
 * @文件名:
 * @创建人: gechangwei
 * @创建时间: 2019/11/22 12:59
 * @修改人:
 * @修改备注: Copyright 北京和信金谷科技有限公司 2019/11/22
 */
@Setter
@Getter
public class GatewayInvokeLog implements Serializable {


    /**
     * 示例：
     * logId : 32423423234324234324234324
     * receiveTime : 1568794092621
     * logTime : 1568794093411
     * transId : 32423423-234324-234324-234324
     * apiCode : SIM
     * apiName : 模拟器
     * servCode : simulate
     * version : 1.0
     * busiType : 1
     * fromIp : 127.0.0.1
     * timestamp : 1568794093411
     * token : eewrew<PERSON>ewrew
     * componentHost : ***************
     * componentPort : 8123
     * statusCode : 0
     * statusDesc : 成功
     * exceptionStack :
     * exceptionTime : 0
     * callTime : 790
     * apiStatus : published
     * httpMethod : POST
     * consumerId : 923100001
     * consumerAppName : 合肥资源管理
     * consumerAppId : HF_RES
     * consumerAppSecret : 32jk242jk234
     * consumerAppIpListType : white
     * consumerAppIpList : 127.0.0.1
     * platformTime : 245
     * externalTime : 545
     * receiveSize : 9
     * requestSize : 9
     * responseSize : 48
     * returnSize : 48
     */
    //消息ID
    private String logId;
    //接收时间
    private Date receiveTime;
    //日志产生时间
    private Date logTime;
    //业务ID
    private String transId;
    //北向能力编码
    private String apiCode;
    //北向能力名称
    private String apiName;
    //北向服务编码
    private String servCode;
    //北向版本号
    private String version;
    //业务类型
    private String busiType;
    //来访IP
    private String fromIp;
    //来访时间戳
    private String timestamp;
    //来访token
    private String token;
    //网关标识
    private String componentKey;
    //网关IP
    private String componentHost;
    //网关端口
    private String componentPort;
    //调用状态编码
    private String statusCode;
    //状态描述
    private String statusDesc;
    //异常堆栈信息
    private String exceptionStack;
    //异常产生时间
    private Date exceptionTime;
    //调用时长
    private long callTime;
    //能力状态
    private String apiStatus;
    //请求方法
    private String httpMethod;
    //消费者ID
    private String consumerId;
    //消费方应用名称
    private String consumerAppName;
    //消费方应用ID
    private String consumerAppId;
    //消费方应用密钥
    private String consumerAppSecret;
    //消费方IP名单类型
    private String consumerAppIpListType;
    //消费方IP列表
    private String consumerAppIpList;
    //平台耗时
    private long platformTime;
    //业务耗时
    private long externalTime;
    //接收报文大小 (接收到用户的请求的大小)
    private long receiveSize;
    //请求报文大小(调用南向能力的请求报文大小)
    private long requestSize;
    //响应报文大小(调用南向能力的请求报文大小)
    private long responseSize;
    //返回报文大小(返回给用户的请求的大小)
    private long returnSize;
    //四种消息列表(REC REQ RESP RET)
    private List<Message> messageList;
}
