package com.wzsec.clean.filter.clean.ShangFei;

import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.filter.parser.ApiCodeGeneration;
import com.wzsec.clean.filter.parser.ApiFileRecognition;
import com.wzsec.clean.filter.parser.PcapTrafficAnalysis;
import com.wzsec.clean.modules.model.*;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 尚飞流净
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
public class ShangFeiFlowClean {

    /**
     * 商飞场景清洗
     *
     * @param pcapFlowCombination pcap流量组合图
     */
    public static ApiCallNetFlow shangFeiClean(PcapFlowCombination pcapFlowCombination,
                                               String waterMarkInfo,
                                               List<String> apiList,
                                               Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                               List<String> interfaceDiscoveryList,
                                               List<String> cleanAPIServiceList,
                                               Map<String, AccountLoginRecord> accountLoginRecordMap) throws Exception {

        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();

        String requestUrl = pcapFlowCombination.getRequest_url();  //请求URL

        String clientIp = pcapFlowCombination.getClient_ip(); //客户端IP
        String clientPort = pcapFlowCombination.getClient_port();  // 客户端端口
        String serverIp = pcapFlowCombination.getServer_ip();  //服务端IP
        String serverPort = pcapFlowCombination.getServer_port();  // 服务端端口
        String requestTime = pcapFlowCombination.getRequest_time(); //请求时间
        String requestData = pcapFlowCombination.getRequest_data() == null ? "" : pcapFlowCombination.getRequest_data(); //请求内容
        String responseData = pcapFlowCombination.getResponse_data() == null ? "" : pcapFlowCombination.getResponse_data();  //响应内容

        byte[] request_bytes = pcapFlowCombination.getRequest_bytes();
        byte[] response_bytes = pcapFlowCombination.getResponse_bytes();

        String requestBody = "";
        String responseBodyData = "";
        try {
            requestBody = ApiFileRecognition.extractTextFromMultipart(request_bytes);
            responseBodyData = ApiFileRecognition.extractTextFromBody(response_bytes);
        } catch (Exception e) {
            requestBody = "";
            responseBodyData = "";
        }

        //判断该接口是否属于上传下载操作
        if (checkStatusApiFile(requestData, responseData)) {
            apiCallNetFlow.setResBody(responseData);
            apiCallNetFlow.setReqBody(requestData);
            apiCallNetFlow.setResByte(responseBodyData);
            apiCallNetFlow.setReqByte(requestBody);
        }


        // 请求响应状态, 请求无值导致截取报错,该处置为空
        String resstatuscode = "";
        try {
            resstatuscode = responseData.split("\r\n")[0].split(" ")[1];
        } catch (Exception e) {
            resstatuscode = "";
        }

        // 请求方法
        String reqmethod = "";
        try {
            reqmethod = requestData.split("\r\n")[0].split(" ")[0];
        } catch (Exception e) {
            reqmethod = "";
        }

        // 请求体
        String reqcontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            try {
                reqcontent = PcapTrafficAnalysis.decodeRequestBody(requestData);
            } catch (Exception e) {
                reqcontent = requestData;
            }
        }

        // 响应体
        String rescontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            rescontent = PcapTrafficAnalysis.extractResponseBody(responseData);
            rescontent = PcapTrafficAnalysis.extractContent(rescontent);
        }

        //  TODO 获取响应数据
        String resstatus = "";
        String repstatus = "";
        if (resstatuscode.startsWith(Const.API_STATUSCODE_5PREFIX)) {
            resstatus = Const.SERVER_ERROR_STATUS;
            repstatus = Const.CLIENT_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_4PREFIX)) {
            resstatus = Const.CLIENT_ERROR_STATUS;
            repstatus = Const.SERVER_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_1PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_2PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_3PREFIX)) {
            resstatus = Const.SERVER_CORRECT_STATUS; //响应状态   服务端
            repstatus = Const.CLIENT_CORRECT_STATUS; //请求状态   客户端
        }

        //  TODO 2.获取请求数据
        // 请求URI
        String interfaceuri = requestUrl.replace("//", "/");
        if (interfaceuri.contains("http:")) {
            interfaceuri = interfaceuri.split("http:")[1];
        }

        // 获取url及接口编码
        String uri = PcapTrafficAnalysis.getInterfaceURI(interfaceuri);

        if (uri.startsWith("/mojo-gateway")) {
            pcapFlowCombination.setResponse_body_data(rescontent); // 响应体
            pcapFlowCombination.setRequest_body_data(reqcontent);  // 请求体
            pcapFlowCombination.setReq_method(reqmethod); //请求方法

            apiCallNetFlow.setAccount("");

            if (interfaceuri.toLowerCase().contains("appkey")) {
                String appKey = PcapTrafficAnalysis.extractAppKey(interfaceuri);
                apiCallNetFlow.setAccount(appKey); //TODO 该处涉及不标准URL导致截取报错,最后时间参数无key值 账号
            } else {
                String client_ip = pcapFlowCombination.getClient_ip();
                String auth = PcapTrafficAnalysis.extractAuthorization(requestData);
                String cliip = accountLoginRecordMap.get(auth) == null ? "" : accountLoginRecordMap.get(auth).getClientip();
                if (cliip.equals(client_ip)) {
                    String account = accountLoginRecordMap.get(auth).getAccount();
                    apiCallNetFlow.setAccount(account);
                }
            }

            // TODO 4. 获取接口编码,新增接口写入接口信息表
            String apicode = ApiCodeGeneration.getInterfaceCode(interfaceuri, uri, pcapFlowCombination,
                    apiList, icInterfaceInfoMap, interfaceDiscoveryList);

            Map<String, String> requestHeardMap = PcapTrafficAnalysis.decodeHeader(requestData); // 请求头
            Map<String, String> responseHeardMap = PcapTrafficAnalysis.decodeHeader(responseData); // 响应头

            apiCallNetFlow.setApicode(apicode); //接口编码
            apiCallNetFlow.setApiuri(interfaceuri); //接口路径

            Params params = new Params();
            params.setParams(reqcontent == null ? "" : reqcontent);
            params.setHead(requestHeardMap);  // 请求头
            // 处理jwt签名算法相关
            String jwtSignatureAlgorithm = JwtSignatureChecker.getJwtSignatureAlgorithm(requestHeardMap);
            if (StringUtils.isNotBlank(jwtSignatureAlgorithm)) {
                params.setJwtSignatureAlgorithm(jwtSignatureAlgorithm);
            }

            String[] reqlines = requestData.split("\r\n");
            if (reqlines.length > 0) {
                params.setReqheader(reqlines[0]); //请求标头
            }

            apiCallNetFlow.setReqcontent(params); // 请求内容
            DataRescontent dataRescontent = new DataRescontent();
            dataRescontent.setStatusCode(resstatuscode); //状态码
            dataRescontent.setHead(responseHeardMap); //响应头
            dataRescontent.setData(rescontent + waterMarkInfo);

            String[] reslines = responseData.split("\r\n");
            if (reslines.length > 0) {
                dataRescontent.setResheader(reslines[0]); // 响应标头
            }

            apiCallNetFlow.setRescontent(dataRescontent);  //响应内容

            apiCallNetFlow.setApiip(clientIp);  //服务端IP TODO 该处调整
            apiCallNetFlow.setClientip(serverIp); //客户端IP TODO 该处调整

            apiCallNetFlow.setClientmac(serverPort); // 客户端端口 TODO 该处调整

            String ipRegion = IPUtils.getIpRegion(serverIp);
            apiCallNetFlow.setClientIpRegion(ipRegion); //客户端IP归属地

            apiCallNetFlow.setApiport(clientPort); //服务端端口 TODO 该处调整

            apiCallNetFlow.setReqmethod(reqmethod); //请求方法
            apiCallNetFlow.setResstatus(resstatus); //响应状态

            apiCallNetFlow.setRepstatus(repstatus); //请求状态
            apiCallNetFlow.setCalltime(requestTime); // 请求时间
            apiCallNetFlow.setCleantime(TimeUtils.getReqTime()); //  清洗时间

            apiCallNetFlow.setSystem(ConfigurationManager.getProperty("netflow.systemFullName").trim()); // 系统标识

            apiCallNetFlow.setDatatype("netflow"); // 数据类型(固定值)

            // TODO 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
            ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
            eventDTO.setCategory("netflow");
            apiCallNetFlow.setEvent(eventDTO);

            // 新增字段 接口响应字符数
            long resSize = rescontent.length();
            String contentLength = responseHeardMap.get("Content-Length");
            if (contentLength == null) {
                contentLength = responseHeardMap.get("content-length");
            }
            if (contentLength != null && contentLength.matches("\\d+")) { // 检查是否为数字
                resSize = Long.parseLong(contentLength);
            }
            apiCallNetFlow.setRessize(resSize);

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = formatter.parse(requestTime);
            int hour = date.getHours();
            apiCallNetFlow.setReqtimetag(String.valueOf(hour));

            apiCallNetFlow.setAkapicode(serverIp + "," + apicode); //先采用客户端IP+接口编码组合,对应请求和响应标识
        }

        return apiCallNetFlow;

    }

    /**
     * 判断该接口是否属于上传下载操作
     *
     * @param requestData  请求体
     * @param responseData 响应体
     * @return
     */
    private static Boolean checkStatusApiFile(String requestData, String responseData) {
        boolean apiFileStatus = false;
        if (requestData.contains("Content-Type") && requestData.contains("boundary=----")) {
            apiFileStatus = true;
        } else if (responseData.contains("Content-Disposition: attachment;")) {
            apiFileStatus = true;
        }
        return apiFileStatus;
    }
}
