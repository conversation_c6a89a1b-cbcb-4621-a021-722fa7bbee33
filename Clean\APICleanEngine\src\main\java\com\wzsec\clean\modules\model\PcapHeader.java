package com.wzsec.clean.modules.model;

/**
 * pcap头
 */
public class PcapHeader {

    /**
     * ⽂件识别头,为0xA1B2C3D4
     */
    private int magic;

    /**
     * 主要版本
     */
    private short magor_version;

    /**
     * 次要版本
     */
    private short minor_version;

    /**
     * 当地标准时间
     */
    private int timezone;

    /**
     * 时间戳的精度
     */
    private int sigflags;

    /**
     * 最⼤的存储长度
     */
    private int snaplen;

    /**
     * 链路类型
     */
    private int linktype;


    public int getMagic() {
        return magic;
    }

    public void setMagic(int magic) {
        this.magic = magic;
    }

    public short getMagor_version() {
        return magor_version;
    }

    public void setMagor_version(short magor_version) {
        this.magor_version = magor_version;
    }

    public short getMinor_version() {
        return minor_version;
    }

    public void setMinor_version(short minor_version) {
        this.minor_version = minor_version;
    }

    public int getTimezone() {
        return timezone;
    }

    public void setTimezone(int timezone) {
        this.timezone = timezone;
    }

    public int getSigflags() {
        return sigflags;
    }

    public void setSigflags(int sigflags) {
        this.sigflags = sigflags;
    }

    public int getSnaplen() {
        return snaplen;
    }

    public void setSnaplen(int snaplen) {
        this.snaplen = snaplen;
    }

    public int getLinktype() {
        return linktype;
    }

    public void setLinktype(int linktype) {
        this.linktype = linktype;
    }

    @Override
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("magic=").append("0x" + Integer.toHexString(this.magic));
        s.append("\nmagor_version=").append(this.magor_version);
        s.append("\nminor_version=").append(this.minor_version);
        s.append("\ntimezone=").append(this.timezone);
        s.append("\nsigflags=").append(this.sigflags);
        s.append("\nsnaplen=").append(this.snaplen);
        s.append("\nlinktype=").append(this.linktype);
        return s.toString();
    }

}
