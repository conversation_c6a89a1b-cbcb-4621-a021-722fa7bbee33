package com.wzsec.clean.filter.clean.Lanxi;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 兰溪流量清洗抽取
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
public class LanxiFlowClean {
    /**
     * 兰溪获取请求参数
     *
     * @param input
     * @return
     */
    public static String getRequestParameters(String input) {
        String requestParameter = input;
        if (input.contains("Content-Disposition: form-data; name=")) {
            Pattern pattern1 = Pattern.compile("(?<=\\n)[^\\n]+");
            Matcher matcher2 = pattern1.matcher(input);
            List<String> keyList = new LinkedList<>();
            List<String> valueList = new LinkedList<>();
            while (matcher2.find()) {
                String dataValue = matcher2.group().replace("\r", "").replace("\n", "").replace(" ", "");
                if (dataValue.contains("Content-Disposition:form-data;name=")) {
                    Pattern pattern = Pattern.compile("name=\"([^\"]+)\"");
                    Matcher matcher = pattern.matcher(dataValue);
                    if (matcher.find()) {
                        keyList.add(matcher.group(1));
                    }
                }
                if (!dataValue.contains("Content-Disposition:") && !dataValue.contains("Content-Type:")
                        && !dataValue.contains("Content-Length:") && !dataValue.contains("--") && StringUtils.isNotBlank(dataValue)) {
                    valueList.add(dataValue);
                }
            }
            Map<String, String> map = new HashMap<>();
            if (keyList.size() == valueList.size()) {
                for (int i = 0; i < keyList.size(); i++) {
                    map.put(keyList.get(i), valueList.get(i));
                }
            }
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                requestParameter = objectMapper.writeValueAsString(map);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return requestParameter;
    }
}
