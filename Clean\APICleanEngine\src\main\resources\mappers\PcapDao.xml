<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wzsec.clean.modules.dao.PcapDao">

    <select id="selectSensitiveData" resultType="com.wzsec.clean.modules.model.SMSensitiveData">
        select * from sm_sensitivedata where testscenario like concat('%',#{signSensitive},'%')
    </select>

    <select id="selectResultType" resultType="hashmap">
        select * from at_resulttype
    </select>

    <select id="selectAuthcheckresultApicode" resultType="string">
        SELECT DISTINCT(apicode) FROM sdd_api_authcheckresult
    </select>

    <select id="queryInterfaceCoding" resultType="string">
        SELECT DISTINCT(apicode) FROM sdd_ic_interfaceinfo WHERE sparefield3 = #{interfaceIdentification}
    </select>

    <select id="queryNumberInterfaces" resultType="int">
        SELECT COUNT(apicode) FROM sdd_ic_interfaceinfo WHERE apicode = #{apicode}
    </select>

    <select id="queryIcModelconfig" resultType="hashmap">
        SELECT suggestbegindate,suggestenddate FROM sdd_ic_modelconfig WHERE apicode = #{interfaceIdentification} and suggestbegindate is not null limit 1;
    </select>

    <select id="getInterfaceName" resultType="string">
        SELECT DISTINCT apiname FROM sdd_ic_interfaceinfo WHERE apicode = #{apicode}
    </select>


    <insert id="saveAuthcheckresult" parameterType="com.wzsec.clean.modules.model.ApiAuthcheckresult">
        insert into sdd_api_authcheckresult(
        apicode,apiname,url,serviceip,serviceport,protocol,reqmethod,reqformat,authmethod,authinfo,risk,inserttime
        )
         values (
         #{apicode},
         #{apiname},
         #{url},
         #{serviceip},
         #{serviceport},
         #{protocol},
         #{reqmethod},
         #{reqformat},
         #{authmethod},
         #{authinfo},
         #{risk},
         #{inserttime}
         )
    </insert>

    <insert id="saveIcCallschedule" parameterType="com.wzsec.clean.modules.model.IcCallschedule">
        insert into das_ic_callschedule(
        apicode,calltime,timeframe,ip
        )
         values (
         #{apicode},
         #{calltime},
         #{timeframe},
         #{ip}
         )
    </insert>

    <insert id="saveIcInterfaceinfo" parameterType="com.wzsec.clean.modules.model.IcInterfaceInfo">
        insert into sdd_ic_interfaceinfo(
            apiip,apiport,apicode,interface_status,sparefield2,sparefield4,data_format,url,syncstate,createtime
        )
         values (
         #{apiip},
         #{apiport},
         #{apicode},
         #{interface_status},
         #{sparefield2},
         #{sparefield4},
         #{data_format},
         #{url},
         #{syncstate},
         #{createtime}
         )
    </insert>


    <insert id="saveIcInterfaceinfoByQinHai" parameterType="com.wzsec.clean.modules.model.IcInterfaceInfo">
        insert into sdd_ic_interfaceinfo(
            apicode,interface_status,sparefield2,sparefield4,data_format,url,apiip,apiport,deleted,sparefield1,syncstate,createtime,req_example,inputparams,outputparams
        )
        values (
                   #{apicode},
                   #{interface_status},
                   #{sparefield2},
                   #{sparefield4},
                   #{data_format},
                   #{url},
                   #{apiip},
                   #{apiport},
                   #{deleted},
                   #{sparefield1},
                   #{syncstate},
                   #{createtime},
                   #{req_example},
                   #{inputparams},
                   #{outputparams}
               )
    </insert>


    <insert id="saveApidiscovery" parameterType="com.wzsec.clean.modules.model.Apidiscovery">
        insert into das_api_apidiscovery(
                apicode,
                apiname,
                apiip,
                apiport,
                url,
                req_example,
                res_example,
                category,
                risk,
                labels,
                accessdomain,
                apistatus,
                inserttime,
                updateuser,
                updatetime,
                sparefield1,
                sparefield2,
                sparefield3,
                sparefield4,
                reqdatatag,
                resdatatag,
                dataformat
        )
         values (
                #{apicode},
                #{apiname},
                #{apiip},
                #{apiport},
                #{url},
                #{req_example},
                #{res_example},
                #{category},
                #{risk},
                #{labels},
                #{accessdomain},
                #{apistatus},
                #{inserttime},
                #{updateuser},
                #{updatetime},
                #{sparefield1},
                #{sparefield2},
                #{sparefield3},
                #{sparefield4},
                #{reqdatatag},
                #{resdatatag},
                #{dataformat}
         )
    </insert>

    <!--数安平台接口发现表INSERT-->
    <insert id="saveApiDiscovery_SecurityPlatform" parameterType="com.wzsec.clean.modules.model.ApiDiscoverySecurityPlatform">
        insert into sdd_ic_apidiscovery(apicode,
                                        apiname,
                                        apiip,
                                        apiport,
                                        url,
                                        req_example,
                                        res_example,
                                        requestmode,
                                        apistatus,
                                        inserttime,
                                        updateuser,
                                        updatetime,
                                        sparefield1,
                                        sparefield2,
                                        sparefield3,
                                        sparefield4)
        values (#{apicode},
                #{apiname},
                #{apiip},
                #{apiport},
                #{url},
                #{req_example},
                #{res_example},
                #{requestmode},
                #{apistatus},
                #{inserttime},
                #{updateuser},
                #{updatetime},
                #{sparefield1},
                #{sparefield2},
                #{sparefield3},
                #{sparefield4})
    </insert>


    <insert id="saveIcModelconfig" parameterType="com.wzsec.clean.modules.model.IcModelconfig">
        insert into sdd_ic_modelconfig(
        apicode,suggestbegindate,suggestenddate
        )
         values (
         #{apicode},
         #{suggestbegindate},
         #{suggestenddate}
         )
    </insert>

    <update id="update" parameterType="com.wzsec.clean.modules.model.IcModelconfig">
        UPDATE sdd_ic_modelconfig
        SET suggestbegindate = #{suggestbegindate},
            suggestenddate = #{suggestenddate}
        WHERE apicode = #{apicode}
    </update>

    <insert id="saveOtherRiskDetect" parameterType="com.wzsec.clean.modules.model.ApiOtherRiskDetect">
        insert into das_api_otherriskdetect(
        apicode,apiname,apiurl,requestip,serviceip,incident,eventdetails,risk,starttime,checktime,sparefield1,sparefield2,sparefield3,sparefield4
        )
         values (
            #{apicode},
            #{apiname},
            #{apiurl},
            #{requestip},
            #{serviceip},
            #{incident},
            #{eventdetails},
            #{risk},
            #{starttime},
            #{checktime},
            #{sparefield1},
            #{sparefield2},
            #{sparefield3},
            #{sparefield4}
         )
    </insert>


    <select id="getFileListInfo" resultType="string">
        select name from fa_list where purpose=#{phonedetection} and type=#{whitelist}
    </select>

    <select id="getResultTypeById" resultType="hashmap">
        select resulttype,resultweight from at_resulttype where id = #{id}
    </select>

    <insert id="saveHttpResult" parameterType="com.wzsec.clean.modules.model.HTTPResult">
        insert into if_checkresult(
        id,
        taskname,
        interfaceuri,
        protocol,
        sourceip,
        sourceport,
        desip,
        desport,
        totalcount,
        risk,
        riskcount,
        checktime,
        sparefield1,
        sparefield2,
        sparefield3,
        sparefield4
        )
         values (
         #{id},
         #{taskname},
         #{interfaceuri},
         #{protocol},
         #{sourceip},
         #{sourceport},
         #{desip},
         #{desport},
         #{totalcount},
         #{risk},
         #{riskcount},
         #{checktime},
         #{sparefield1},
         #{sparefield2},
         #{sparefield3},
         #{sparefield4}
         )
    </insert>

    <insert id="saveHttpResultDetail" parameterType="com.wzsec.clean.modules.model.HTTPResultDetail">
        insert into if_checkresultdetail(
        id,
        seqnumber,
        interfaceuri,
        sourceip,
        sourceport,
        desip,
        desport,
        reqmethod,
        resstatuscode,
        resstatus,
        reqcontent,
        rescontent,
        checkrule,
        resulttype,
        resulttypecount,
        checktotalcount,
        ratio,
        sensitivedata,
        risk,
        userid,
        username,
        starttime,
        endtime,
        checktime,
        sparefield1,
        sparefield2,
        sparefield3,
        sparefield4
        )
         values (
         #{id},
         #{seqnumber},
         #{interfaceuri},
         #{sourceip},
         #{sourceport},
         #{desip},
         #{desport},
         #{reqmethod},
         #{resstatuscode},
         #{resstatus},
         #{reqcontent},
         #{rescontent},
         #{checkrule},
         #{resulttype},
         #{resulttypecount},
         #{checktotalcount},
         #{ratio},
         #{sensitivedata},
         #{risk},
         #{userid},
         #{username},
         #{starttime},
         #{endtime},
         #{checktime},
         #{sparefield1},
         #{sparefield2},
         #{sparefield3},
         #{sparefield4}
         )
    </insert>

    <insert id="saveFtpResultDetail" parameterType="com.wzsec.clean.modules.model.FTPResultDetail">
        insert into ff_checkresultdetail(
        id,
        sourceip,
        sourceport,
        desip,
        desport,
        sign,
        username,
        password,
        filename,
        filepath,
        filesize,
        resulttype,
        sensitivedata,
        risk,
        resulttypecount,
        totalcount,
        ratio,
        operationtime,
        checktime,
        sparefield1,
        sparefield2,
        sparefield3,
        sparefield4
        )
         values (
         #{id},
         #{sourceip},
         #{sourceport},
         #{desip},
         #{desport},
         #{sign},
         #{username},
         #{password},
         #{filename},
         #{filepath},
         #{filesize},
         #{resulttype},
         #{sensitivedata},
         #{risk},
         #{resulttypecount},
         #{totalcount},
         #{ratio},
         #{operationtime},
         #{checktime},
         #{sparefield1},
         #{sparefield2},
         #{sparefield3},
         #{sparefield4}
        )
    </insert>


    <insert id="saveFTPResult" parameterType="com.wzsec.clean.modules.model.FTPResult">
        insert into ff_checkresult(
        id,
        taskname,
        protocol,
        sourceip,
        sourceport,
        desip,
        desport,
        risk,
        riskcount,
        checktime,
        sparefield1,
        sparefield2,
        sparefield3,
        sparefield4
        )
         values (
         #{id},
         #{taskname},
         #{protocol},
         #{sourceip},
         #{sourceport},
         #{desip},
         #{desport},
         #{risk},
         #{riskcount},
         #{checktime},
         #{sparefield1},
         #{sparefield2},
         #{sparefield3},
         #{sparefield4}
        )
    </insert>

    <select id="selectApicode" resultType="string">
        select distinct apicode from sdd_ic_interfaceinfo
    </select>


    <select id="getInterfaceDiscoveryTable" resultType="java.lang.String">
        select distinct apicode from das_api_apidiscovery
    </select>


    <select id="getInterfaceDiscoverySecurityPlatform" resultType="java.lang.String">
        select distinct apicode from sdd_ic_apidiscovery
    </select>

</mapper>
