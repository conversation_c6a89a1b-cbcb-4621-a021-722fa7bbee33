#清洗程序服务端口号
server.port=8288

# MySQL 数据库配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=<EMAIL>

# ob 数据库配置
#spring.datasource.driver-class-name=com.alipay.oceanbase.jdbc.Driver
#spring.datasource.url=**************************************************************************************************************************************************************
#spring.datasource.username=root@sys
#spring.datasource.password=<EMAIL>

# kingbase8 数据库配置
#spring.datasource.driver-class-name=com.kingbase8.Driver
#spring.datasource.url=*****************************************************************************************************************************************************
#spring.datasource.username=system
#spring.datasource.password=<EMAIL>

# openGauss 数据库配置
#spring.datasource.driver-class-name=com.huawei.gauss200.jdbc.Driver
#spring.datasource.url=***************************************
#spring.datasource.username=gaussdb
#spring.datasource.password=Admin@123456

#系统打印日志配置
logging.config=classpath:logback.xml

#mybatis xml配置
mybatis.mapper-locations=classpath:mappers/*.xml


##############################################
# 清洗任务开关 0:默认开启 1:关闭
netflow.cleanstatus=0
#流量清洗开关配置 0:默认清洗 1:关闭  (0:默认前一天,cron表达式示例 00 00 01 * * ? ; 1: 清洗前一个小时的流量文件,cron表达式示例 0 2 * * * ?  )
netflowclean.cron=30 28 08 * * ?
#流量清洗模式选择(0:默认前一天; 1: 清洗前一个小时 )
netflow.cleaningmode=0

#流量清洗检测源（1：ES，2：本地磁盘）,检测类型（0：FTP流量，1：接口流量）,检测任务名称,系统全称
netflow.detectionSource=2
netflow.detectionType=1
netflow.detectionTask=netflowClean
#海南根据系统定义(注: 字典system_number配置系统编号对应名称)
netflow.systemFullName=02

#设置程序重启开关 0:开启 1:默认关闭
netflow.restartSwitch=1
#设置程序重启命令
netflow.cleanRestartCommand=nohup java -Xms2g -Xmx4g -XX:NewRatio=1 -XX:SurvivorRatio=8 -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -jar /data/zhengh/cleanTool/APICleanEngine-2.2.1.jar >nohup.out 2>&1 &
# 设置内存阈值 默认设置为3G (增加到3G以适应新的堆内存设置)
netflow.memoryThreshold=3221225472

#接口文件敏感内容检测 0:开启 1:默认关闭
apifilerecognition.status=1

#流量清洗加密流量写入开关 0:开启 1:默认关闭
netflow.encryptedpcap.status=1

#流量清洗网络会话分析写入开关 0:开启 1:默认关闭
netflow.networksessionpcap.status=1
##############################################


#####################################################################
######################根据目录路径pcap数据 流量清洗 ##################
#pcap临时目录 存放远程传输的tar.gz(4.海南适用)
PcapTmpPath=E:\\pcap\\pcaptmp
#清洗前文件存放的路径
PcapCleanBeforePath=E:\\pcap\\pcapcleanbeforepath\\
#清洗后文件存放的路径
PcapCleanAfterPath=E:\\pcap\\pcapcleanafterpath
#存放文件路径
HttpRestoreFilePath=E:\\pcap\\restorefile\\http
FTPRestoreFilePath=E:\\pcap\\restorefile\\ftp
#pcap存储目录日期(可以指定检测那天文件目录, 默认检测前一天)
PcapCleanBeforeDate=
#清洗流量文件间隔天数(默认相隔一天)
PcapClean.IntervalDay=1
#pcap流量包清洗线程数 (减少线程数以降低内存压力)
CleanPcapThreadNum=1
#pcap流量包清洗线程超时回收时间
CleanPcapThreadTimeoutTime=8

# ###### Pcap流量结果清洗到elasticsearch 配置 ##########
elasticsearch.hostlist=**************:9200
elasticsearch.username=elastic
elasticsearch.password=wzsec@2022

# 流量清洗输出类型配置 1:本地 2:ES 3:kafka
netflow.cleanoutputtype=2

# 采集的流量类型  1:苏州  2:金华  3:永康  4:海南  5:青海  6:管网 7:智网科技 8:海南[通用] 9:数安平台 10:中移在线
# 11:通用[清洗接口信息写入规范和发现表] 12:商飞
netflow.collectdatatype=4
# 接口信息表新增接口写入开关 0:默认开启 1:关闭
insertInterfaceInfo.status=1


#####################################################################


################Packetbeat ES检测方式##################
#Packetbeat检测日期(默认检测前一天)
netflow.checkdate=
#间隔天数
netflow.checkintervalday=1

#ES  通过查询ES流量数据 进行清洗([中移在线]清洗检测源为ES,采集流量类型为10)
netflow.es.index.prefix=aop_trans_
netflow.es.hostlist=**************:9200
netflow.es.username=
netflow.es.password=
netflow.es.scrollsize=1
netflow.es.scrolltimeValueMinutes=5
########################################################################

######################## kafka 环境配置 ###########################################
###消费kafka并写入ES触发开关 0:开启 1:默认关闭
KafkaMessageConsume.status=0
# 消费Kakfa写入ES场景  0:智网 1:软研院 2:中移在线
KafkaMessageConsume.scene=2
###消费kafka并写入ES触发时间设置
KafkaMessageConsume.cron=30 26 08 * * ?
# topic
spring.kafka.consumer.group-id=test-kafka
spring.kafka.bootstrap-servers=*************:9092
kafka.ssl.username=ncoppkafka
kafka.ssl.password=1qaz!QAZ
###################################################################################

#################告警推送syslog配置#################
syslog.host=*************
syslog.port=11514
# syslog端口(tcp,udp)
syslog.protocol=tcp

#################暗水印配置#################
# 数据提供方 例：xxx大数据局
dataProvider=丽水市大数据局
# 数据使用方 例: xxx教育局
dataUse=水务局
# 根据数量进行溯源
dataraceNum=100
###########################################

#流量清洗加密触发开关 (0:开启 1:默认关闭)
netflow.encryptionSwitch=1
#流量清洗加密后路径
netflow.encryptionPath=E:\\pcap\\encryption

#流量清洗解密触发开关 (0:开启 1:默认关闭)
netflow.decryptSwitch=1
#流量清洗解密触发时间
netflow.decryptCron=50 58 09 * * ?
#流量清洗解密后路径
netflow.decryptPath=E:\\pcap\\decrypted
