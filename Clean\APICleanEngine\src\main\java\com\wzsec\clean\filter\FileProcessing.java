package com.wzsec.clean.filter;

import cn.hutool.core.lang.Console;
import com.wzsec.clean.common.analysis.PcapParser_v3;
import com.wzsec.clean.common.analysis.thread.PacketbeatParser;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.filter.clean.HainanBigData.FileReadByHainanBigData;
import com.wzsec.clean.filter.clean.chinaMobileOnline.ElasticsearchClusterScrollRetry;
import com.wzsec.clean.filter.clean.networksession.NetworkSessionAudit;
import com.wzsec.clean.filter.clean.networksession.NetworkSessionStatistics;
import com.wzsec.clean.filter.parser.TreatmentMethods;
import com.wzsec.clean.modules.dao.ApiDao;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.IcInterfaceInfoService;
import com.wzsec.clean.modules.service.PcapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pcap4j.packet.IpV4Packet;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 流量文件清洗检测入口(文件处理)
 *
 * <AUTHOR>
 * @date 2023-08-10
 */

@Slf4j
@Component
@EnableScheduling
public class FileProcessing {

    //pcap文件清洗处理模式
    private static final String cleaningMode = ConfigurationManager.getProperty("netflow.cleaningmode").trim();

    private static final String status = ConfigurationManager.getProperty("netflow.cleanstatus").trim();
    private static final String detectionSource = ConfigurationManager.getProperty("netflow.detectionSource").trim();
    private static final String detectionType = ConfigurationManager.getProperty("netflow.detectionType").trim();
    private static final String detectionTask = ConfigurationManager.getProperty("netflow.detectionTask").trim();

    private static final String pcapTmpPath = ConfigurationManager.getProperty("PcapTmpPath").trim();
    private static final String pcapCleanBeforePath = ConfigurationManager.getProperty("PcapCleanBeforePath").trim();

    private static final String dataProvider = ConfigurationManager.getProperty("dataProvider").trim();
    private static final String dataUse = ConfigurationManager.getProperty("dataUse").trim();
    private static final int waterLineSpacing = Integer.parseInt(ConfigurationManager.getProperty("dataraceNum").trim());

    //TODO 1. 创建线程池
    private static final ExecutorService threadPool = Executors.newFixedThreadPool(
            Integer.parseInt(ConfigurationManager.getProperty("CleanPcapThreadNum").trim()), new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("CleanPcapThread-" + threadNumber.getAndIncrement());
                    return thread;
                }
            });

    private static final String collectdatatype = ConfigurationManager.getProperty("netflow.collectdatatype").trim(); //从接口流量中截取接口编码标识

    // 接口传输文本文件识别
    private static final ThreadLocal<Map<String, FlowCombination>> threadTextFileMap = ThreadLocal.withInitial(HashMap::new);

    private static final ThreadLocal<Map<String, PcapFlowCombination>> threadPcapFlowCombinationMap = ThreadLocal.withInitial(ConcurrentHashMap::new);

    private static final ThreadLocal<List<ApiCallNetFlow>> threadEsDataList = ThreadLocal.withInitial(ArrayList::new);

    private static final ThreadLocal<Integer> threadCleanSum = ThreadLocal.withInitial(() -> 0);  //文件处理线程写入条数

    private static int numberOfFiles = 0;

    private static final IcInterfaceInfoService icInterfaceInfoService = SpringUtils.getApplicationContext().getBean(IcInterfaceInfoService.class);

    private static final PcapService pcapService = SpringUtils.getApplicationContext().getBean(PcapService.class);

    private static final Accumulator accumulator = new Accumulator();

    private static final ElasticsearchClusterScrollRetry elasticSearchDataTransfer = SpringUtils.getApplicationContext().getBean(ElasticsearchClusterScrollRetry.class);

    @Scheduled(cron = "${netflowclean.cron}")
    public static void executeTheCleaningTaskImmediately() {

        // 启动监控线程 如果超过则重启程序
        // new Thread(MemoryMonitor::monitorMemory).start();

        // 获取开始时间
        Instant startTime = Instant.now();

        accumulator.reset();

        if (Const.FLOW_CLEANING_ON.equals(status)) {

            //配置文件配置任务名称,检测源（0：ES，1：磁盘）,检测类型（0：接口日志，1：接口流量）
            PcapTask pcapTask = new PcapTask();
            pcapTask.setChecksource(detectionSource);
            pcapTask.setType(detectionType);
            pcapTask.setTaskname(detectionTask);

            if (Const.FROM_ES.equals(pcapTask.getChecksource())) {
                // 从ES读取
                log.info("开始执行流量检测任务,执行源:ES");

                // 从ES读取,且指定为中移在线
                if (Const.FLOW_DATA_CHINA_MOBILE_ONLINE.equals(collectdatatype)) {

                    // TODO [中移在线] 源端ES-->目标端ES
                    elasticSearchDataTransfer.elasticsearchRequestsObtainData();

                } else {
                    // 1.Tcpdump 方式
                    PcapParser_v3.checkPcapFromES(pcapTask); //ES 7.9.1
                    PacketbeatParser.checkPcapFromES(pcapTask);
                }

            } else if (Const.FROM_DISK.equals(pcapTask.getChecksource())) {

                // TODO 2.缓存接口信息表数据(数据库交互)
                // 共享数据需确保线程安全
                Map<String, IcInterfaceInfo> icInterfaceInfoMap = Collections.synchronizedMap(new HashMap<>());
                Map<String, AccountLoginRecord> accountLoginRecordMap = Collections.synchronizedMap(new HashMap<>());
                List<String> apicodeList = Collections.synchronizedList(new ArrayList<>());
                List<String> interfaceDiscoveryList = Collections.synchronizedList(new ArrayList<>());
                //TODO 缓存HTTPS流量
                List<ApiEncryptedtraffic> apiEncryptedtrafficList = Collections.synchronizedList(new ArrayList<>());

                //清洗接口清单
                List<String> cleanAPIServiceList = Collections.synchronizedList(new ArrayList<>());

                if (Const.FLOW_DATA_Suzhou.equals(collectdatatype)) {
                    apicodeList = icInterfaceInfoService.queryApicodeList();
                } else if (Const.FLOW_DATA_Hainan.equals(collectdatatype)) { //TODO 海南无需数据库交互
                    apicodeList = new ArrayList<>();
                } else {
                    //获取接口信息(避免重复查询, 仅加载一次)
                    List<IcInterfaceInfo> icInterfaceInfos = ApiDao.selectInterfaceInfo();
                    for (IcInterfaceInfo icInterfaceInfo : icInterfaceInfos) {
                        icInterfaceInfoMap.put(icInterfaceInfo.getApicode(), icInterfaceInfo);
                        apicodeList.add(icInterfaceInfo.getApicode());
                    }
                    if (Const.FLOW_DATA_SECURITY_PLATFORM.equals(collectdatatype)) {
                        interfaceDiscoveryList = pcapService.getInterfaceDiscoverySecurityPlatform();
                    } else {
                        interfaceDiscoveryList = ApiDao.getInterfaceDiscoveryTable();
                    }

                    String apiServiceDictID = ApiDao.cleanAPIServiceFlag("cleanAPIServiceFlag");
                    if (StringUtils.isNotBlank(apiServiceDictID)) {
                        cleanAPIServiceList = ApiDao.cleanAPIServiceList(apiServiceDictID);
                    }
                }

                // 从本地磁盘读取
                cleaningTask(icInterfaceInfoMap, apicodeList, interfaceDiscoveryList,
                        cleanAPIServiceList, accountLoginRecordMap, apiEncryptedtrafficList);

                // TODO [海南大数据局]根据解压路径下文件遍历,不属于当天的进行删除操作
                if (ConfigurationManager.getProperty("netflow.collectdatatype").trim().equals(Const.FLOW_DATA_Hainan)) {
                    FileReadByHainanBigData.cleanNonDailyCompressedFiles();
                }
            }
        } else {
            log.info("pcap流量检测状态未开启");
        }

        // 获取结束时间
        Instant endTime = Instant.now();
        Duration duration = Duration.between(startTime, endTime);
        // log.info("【 执行清洗任务耗时(仅参考): {} 秒 】", duration.getSeconds());

    }

    /**
     * 存量文件清洗
     */
    private static void cleaningTask(Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                     List<String> apicodeList,
                                     List<String> interfaceDiscoveryList,
                                     List<String> cleanAPIServiceList,
                                     Map<String, AccountLoginRecord> accountLoginRecordMap,
                                     List<ApiEncryptedtraffic> apiEncryptedtrafficList) {

        //加按水印方法 (包内方法,每次打印水印信息,暂时注释)
        String waterMarkInfo = "";
        // waterMarkInfo = getWaterMarkInfo(dataProvider, dataUse, true);

        // TODO 3.获取清洗前路径,路径下文件集合,获取ES索引时间
        String parserBeforeDir = getCleaningBeforeDir();
        Console.log("清洗存量流量路径：" + parserBeforeDir);

        //  TODO (海南环境独有,无该路径不影响) 将远程传输过来的tar.gz文件解压至清洗前路径
        if (ConfigurationManager.getProperty("netflow.collectdatatype").trim().equals(Const.FLOW_DATA_Hainan)) {
            if (cleaningMode.equals(Const.CLEANING_MODE_DAY)) { // 按天解压
                FileReadByHainanBigData.hainanEnvironmentalDecompressionToDay(parserBeforeDir);
            } else if (cleaningMode.equals(Const.CLEANING_MODE_HOUR)) {  //按上小时解压
                FileReadByHainanBigData.hainanEnvironmentalDecompressionToHour(parserBeforeDir);
            }
        }

        log.info("=== 开始对 {} 目录下存量文件进行检测 === ", parserBeforeDir);

        List<File> fileList = getFileList(parserBeforeDir);

        numberOfFiles = fileList.size();

        String esIndexTime = getESIndexTime();

        // 多线程处理文件
        if (!fileList.isEmpty()) {

            log.info("【文件清洗开始,开始处理路径为: {} 下文件,共{}个】", pcapCleanBeforePath, fileList.size());

            // 存储每个任务的Future对象
            List<Future<?>> futures = new ArrayList<>();

            // 使用线程池提交任务
            for (File file : fileList) {
                // 提交任务，并获取Future对象，同时传递文件名信息
                Future<?> future = threadPool.submit(() -> {
                    try {
                        processFile(file, waterMarkInfo, icInterfaceInfoMap, apicodeList, interfaceDiscoveryList,
                                esIndexTime, accumulator, cleanAPIServiceList, accountLoginRecordMap, apiEncryptedtrafficList);
                    } catch (Exception e) {
                        // 捕获任务执行中的异常并打印日志
                        log.error("处理文件 {} 时发生异常", file.getName(), e);
                    }
                });
                futures.add(future); // 将Future对象存储起来
            }

            // 设置超时时间并监控每个任务
            timeoutCleanup(futures);
        }

    }

    /**
     * 线程执行文件解析超时清理
     *
     * @param futures futures
     */
    private static void timeoutCleanup(List<Future<?>> futures) {
        String cleanPcapThreadTimeoutTime = ConfigurationManager.getProperty("CleanPcapThreadTimeoutTime").trim();
        for (Future<?> future : futures) {
            try {
                // 设置任务超时时间（单位：分钟）
                future.get(Long.parseLong(cleanPcapThreadTimeoutTime), TimeUnit.MINUTES);
            } catch (TimeoutException e) {
                // 超时处理
                log.error("任务执行超时: {} 分钟，取消任务...", cleanPcapThreadTimeoutTime);

                // 取消任务，并释放资源
                boolean cancelled = future.cancel(true);
                if (cancelled) {
                    log.info("任务超时，成功取消任务");
                } else {
                    log.warn("任务超时，取消任务失败");
                }

                // 清理线程相关的资源
                threadEsDataList.remove();
                threadTextFileMap.remove();
                threadPcapFlowCombinationMap.remove();
            } catch (InterruptedException e) {
                // 处理任务中断异常
                Thread.currentThread().interrupt();  // 重新设置中断标志
                log.error("线程被中断，任务未完成", e);
            } catch (ExecutionException e) {
                // 处理任务执行中的异常
                log.error("任务执行异常", e.getCause());
            }
        }
    }


    /**
     * 文件处理
     *
     * @param file 文件
     */
    private static Future<?> processFile(File file, String waterMarkInfo,
                                         Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                         List<String> apicodeList,
                                         List<String> interfaceDiscoveryList,
                                         String esIndexTime, Accumulator accumulator,
                                         List<String> cleanAPIServiceList,
                                         Map<String, AccountLoginRecord> accountLoginRecordMap,
                                         List<ApiEncryptedtraffic> apiEncryptedtrafficList) {

        // 获取当前线程的名称
        String threadName = Thread.currentThread().getName();
        try {
            // 获取当前线程的独立副本
            List<ApiCallNetFlow> esDataList = threadEsDataList.get();
            Map<String, FlowCombination> textFileMap = threadTextFileMap.get();
            Map<String, PcapFlowCombination> pcapFlowCombinationMap = threadPcapFlowCombinationMap.get();

            //文件清洗线程清洗条数
            Integer cleanSum = threadCleanSum.get();

            // TODO 执行流量清洗任务
            // log.info("线程{}执行对文件{}的清洗检测任务 ", threadName, file.getName());

            try {
                TreatmentMethods.checkPcapFromDisk(file, threadName, cleanSum,
                        esDataList, waterMarkInfo, waterLineSpacing, icInterfaceInfoMap, apicodeList,
                        textFileMap, pcapFlowCombinationMap, interfaceDiscoveryList, esIndexTime, accumulator,
                        cleanAPIServiceList, accountLoginRecordMap, apiEncryptedtrafficList);
            } catch (Exception e) {
                log.error("***** 文件{}执行清洗任务出现异常, {} *****", file, e.getMessage());
                e.printStackTrace();
            } finally {

                //将清洗后pcap数据进行加密存储
                String encryptionSwitch = ConfigurationManager.getProperty("netflow.encryptionSwitch").trim(); //清洗之后加密开关
                cleanEncryptionAccess(file, encryptionSwitch);

                // 处理完文件后，重命名文件（将 ".pcap" 后缀改为 "_bak.pcap"）
                String newFileName = file.getName().replace(".pcap", "_bak.pcap");
                File newFile = new File(file.getParent(), newFileName);

                Path sourcePath = file.toPath();
                Path targetPath = newFile.toPath();

                // TODO ------------------ 临时注释 ------------------
                // try {
                //     Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                //     log.info("〖文件重命名成功: {} -> {}〗", file.getName(), newFileName);
                // } catch (IOException e) {
                //     log.warn("〖文件重命名失败: {}〗", file.getName());
                //     e.printStackTrace();
                // }
                // TODO ------------------ 临时注释 ------------------
            }

            // 清除线程本地数据
            threadEsDataList.remove();
            threadTextFileMap.remove();
            threadPcapFlowCombinationMap.remove();

            numberOfFiles--;

            log.info("【当前线程 {} 对文件 {} 处理完成, 清理变量副本完成,剩余待处理文件{}个 】", threadName, file, numberOfFiles);

        } catch (Exception e) {
            log.error("清洗出现异常...");
            if (e instanceof TimeoutException) {
                log.error("任务执行超时，线程名: {}", threadName);
            } else {
                // 其他异常处理
                e.printStackTrace();
            }
        }

        // 返回一个已完成的 Future<Void>
        return CompletableFuture.completedFuture(null);
    }

    @Scheduled(cron = "${netflow.decryptCron}")
    public static void executeCleanDecryptAccess() {
        //将清洗后pcap数据进行解密存储
        String decryptSwitch = ConfigurationManager.getProperty("netflow.decryptSwitch").trim(); //清洗之后解密开关
        if (decryptSwitch.equals(Const.FLOW_CLEANING_ON)) {
            String encryptionPath = ConfigurationManager.getProperty("netflow.encryptionPath").trim(); //读取加密路径下文件
            String decryptPath = ConfigurationManager.getProperty("netflow.decryptPath").trim(); //解密后路径
            log.info("〖将 {} 路径下加密后文件进行解密写入: 〗", encryptionPath);
            List<File> fileList = getFileList(encryptionPath);

            String parserAfterDir = "";

            if (decryptPath.endsWith(File.separator)) {
                parserAfterDir = decryptPath;
            } else {
                parserAfterDir = decryptPath + File.separator;
            }

            try {
                for (File file : fileList) {
                    String originalPath = file.getName().replace("_encrypted.pcap", "_decrypt.pcap");
                    PcapAesEncryption.decryptEncryptedWrite(file.getPath(), parserAfterDir + originalPath);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 将清洗后pcap数据进行加密存储
     *
     * @param file             原始文件
     * @param encryptionSwitch 加密后文件路径
     * @throws Exception
     */
    private static void cleanEncryptionAccess(File file, String encryptionSwitch) throws Exception {
        if (encryptionSwitch.equals(Const.FLOW_CLEANING_ON)) {
            String encryptionPath = ConfigurationManager.getProperty("netflow.encryptionPath").trim(); //清洗之后加密文件路径
            String parserAfterDir = "";

            if (encryptionPath.endsWith(File.separator)) {
                parserAfterDir = encryptionPath;
            } else {
                parserAfterDir = encryptionPath + File.separator;
            }
            String originalPath = file.getName().replace(".pcap", "_encrypted.pcap");
            PcapAesEncryption.cryptographicWrite(file.getPath(), parserAfterDir + originalPath);
        }
    }

    /**
     * 获取清洗前路径
     *
     * @return {@link String}
     */
    private static String getCleaningBeforeDir() {
        String parserBeforeDir = pcapCleanBeforePath;

        if (cleaningMode.equals(Const.CLEANING_MODE_DAY)) {

            // 指定日期清洗
            String pcapDate = ConfigurationManager.getProperty("PcapCleanBeforeDate").trim();

            boolean validDate = DateUtils.isValidDateFormat(pcapDate, "yyyyMMdd");

            if (!validDate) {
                //默认检测前一天
                pcapDate = TimeUtils.getNextDay(new Date(), 1);
            }

            if (pcapCleanBeforePath.endsWith(File.separator)) {
                parserBeforeDir = pcapCleanBeforePath + pcapDate;
            } else {
                parserBeforeDir = pcapCleanBeforePath + File.separator + pcapDate;
            }
        } else if (cleaningMode.equals(Const.CLEANING_MODE_HOUR)) {
            // 清洗前一个小时数据
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 获取前一个小时的时间
            LocalDateTime previousHourTime = currentTime.minusHours(1);
            DateTimeFormatter firstFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String firstLayerPath = previousHourTime.format(firstFormatter);
            DateTimeFormatter secondFormatter = DateTimeFormatter.ofPattern("yyyy_MM_dd_HH");
            String secondLayerPath = previousHourTime.format(secondFormatter);

            if (pcapCleanBeforePath.endsWith(File.separator)) {
                parserBeforeDir = pcapCleanBeforePath + firstLayerPath + File.separator + secondLayerPath;
            } else {
                parserBeforeDir = pcapCleanBeforePath + File.separator + firstLayerPath + File.separator + secondLayerPath;
            }

        }
        return parserBeforeDir;
    }


    /**
     * 根据路径获取文件
     *
     * @param parserBeforeDir dir之前解析器
     * @return {@link List}<{@link File}>
     */
    private static List<File> getFileList(String parserBeforeDir) {
        List<File> fileList = new ArrayList<>();
        try {
            // 获取需要即时清理文件对象集合
            List<File> files = new ArrayList<>();
            FileUtils.findFiles(files, parserBeforeDir);
            fileList = new ArrayList<>();
            for (File file : files) {
                if (!file.getName().contains("_bak") && file.getName().endsWith(".pcap")) {  // 流量文件格式为: yyyy_MM_dd_HH_mm_ss.pcap
                    fileList.add(file);
                }
            }
        } catch (Exception e) {
            log.error("【获取指定路径: {} 下文件失败 ---> {} 】", parserBeforeDir, e.getMessage());
        }
        return fileList;
    }


    /**
     * 获取ES索引拼接时间
     *
     * @return {@link String}
     */
    private static String getESIndexTime() {

        String pcapDate = "";
        if (cleaningMode.equals(Const.CLEANING_MODE_DAY)) {
            // 指定日期清洗
            pcapDate = ConfigurationManager.getProperty("PcapCleanBeforeDate").trim();

            boolean validDate = DateUtils.isValidDateFormat(pcapDate, "yyyyMMdd");
            if (!validDate) {
                //默认检测前一天
                pcapDate = TimeUtils.getNextDay(new Date(), 1);
            }

        } else if (cleaningMode.equals(Const.CLEANING_MODE_HOUR)) {
            // TODO 清洗前一个小时数据
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();
            // 获取前一个小时的时间
            LocalDateTime previousHourTime = currentTime.minusHours(1);
            DateTimeFormatter firstFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            pcapDate = previousHourTime.format(firstFormatter);
        }

        return DateUtils.EsDateFormat(pcapDate);
    }

}
