package com.wzsec.clean.common.utils;

import com.wzsec.clean.common.rule.Dict;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @Description:常量
 * @date 2020-4-28
 */
public class Const {

    // 使用懒加载和弱引用来管理静态集合，避免内存泄漏
    private static volatile List<String> nameList = null; // 百家姓
    private static volatile List<String> whiteNameList = null; // 姓名白名单
    private static volatile List<String> addreNameList = null; // 地址
    private static volatile List<String> whiteAddreList = null; // 地址白名单
    private static volatile List<String> whitePhoneList = null;// 手机号白名单
    private static final List<String> countryList = Arrays.asList(Dict.countryName); // 国家名，用于姓名误报

    private static final Map<String, String> addressCodeMap = new ConcurrentHashMap<>();//身份证地址编码

    // 提供线程安全的getter方法
    public static List<String> getNameList() {
        return nameList;
    }

    public static List<String> getWhiteNameList() {
        return whiteNameList;
    }

    public static List<String> getAddreNameList() {
        return addreNameList;
    }

    public static List<String> getWhiteAddreList() {
        return whiteAddreList;
    }

    public static List<String> getWhitePhoneList() {
        return whitePhoneList;
    }

    public static List<String> getCountryList() {
        return countryList;
    }

    public static Map<String, String> getAddressCodeMap() {
        return addressCodeMap;
    }

    static {
        for (int i = 0; i < Dict.addressCode.length; i++) {
            String[] temArr = Dict.addressCode[i].split(" ");
            addressCodeMap.put(temArr[0], temArr[1]);
        }
    }


    // 系统统计拼接字符
    public final static String AUDIT_SPLIT_JOIN = "-";

    public final static String AUDIT_POINT_JOIN = ".";

    // 拼接符号
    public final static String JOIN_SYMBOL = "---";

    // 接口流量标识
    public final static String INTERFACE_FLOW_SIGN = "1";//"接口流量";
    public final static String FTP_FLOW_SIGN = "0";//"FTP流量";

    // 经纬度标识
    public final static String ITUDE_SIGN = "经纬度";

    // 手机号标识
    public final static String MOBILENUMBER_SIGN = "手机号";

    // 地址标识
    public final static String ADDRESS_SIGN = "地址";

    // 身份证号标识
    public final static String IDENTITY_SIGN = "身份证号";

    // 邮箱标识
    public final static String EMAIL_SIGN = "邮箱";

    // AES标识
    public final static String AES_SIGN = "AES加密串";

    // 3DES标识
    public final static String DES_SIGN = "3DES加密串";

    // MD5标识
    public final static String MD5_SIGN = "MD5加密串";

    // SHA256标识
    public final static String SHA256_SIGN = "SHA256加密串";

    // IMSI标识
    public final static String IMSI_SIGN = "IMSI";

    // MEID标识
    public final static String MEID_SIGN = "MEID";

    // IMEI标识
    public final static String IMEI_SIGN = "IMEI";

    // 姓名标识
    public final static String NAME_SIGN = "姓名";

    // 固话标识
    public final static String FIXEDLINE_SIGN = "固话";
    //省份标识
    public final static String PROVINCE_SIGN = "省份";
    //民族标识
    public final static String NATIONAL_SIGN = "民族";
    //企业名称
    public final static String ENTERPRISENAME_SIGN = "企业名称";
    //QQ号
    public final static String QQ_SIGN = "QQ号";
    //社会统一信用代码校验
    public final static String UNIFIEDCREDITCODE_SIGN = "社会统一信用代码";
    //营业执照校验
    public final static String BUSINESSLICENSE_SIGN = "营业执照";
    //银行卡号
    public final static String BANKCARD_SIGN = "银行卡号";
    //组织机构代码
    public final static String VALIDENTPCODE_SIGN = "组织机构代码";
    //税务登记证号码
    public final static String TAXATIONNO_SIGN = "税务登记证号码";
    //车牌号
    public final static String CARNUMBERNO_SIGN = "车牌号";
    //车辆识别号码
    public final static String VIN_SIGN = "车辆识别号码";
    //港澳通行证号码
    public final static String HMPASSCODE_SIGN = "港澳通行证号码";
    //军官证号码
    public final static String OffICERCARD_SIGN = "军官证号码";
    //护照号
    public final static String PASSPORTCARD_SIGN = "护照号";
    //开户许可证
    public final static String ACCOUNTOPENINGPERMITNO_SIGN = "开户许可证";
    //IPv6
    public final static String IPV6_SIGN = "IPv6";
    //IP
    public final static String IP_SIGN = "IP";

    //HTTP标识
    public final static String HTTP_SIGN = "HTTP";
    public final static String REQ_SIGN = "REQ";
    public final static String RES_SIGN = "RES";
    public final static String GET_SIGN = "GET";
    public final static String POST_SIGN = "POST";
    public final static String PUT_SIGN = "PUT";
    public final static String DELETE_SIGN = "DELETE";
    public final static String STATUS = "Status";
    //FTP标识
    public final static String FTP_SIGN = "FTP";

    //明文标识
    public final static String PAL_MOBILENUMBER_SIGN = "明文手机号";
    public final static String PAL_IDENTITY_SIGN = "明文身份证号";
    public final static String PAL_IMSI_SIGN = "明文IMSI";
    public final static String PAL_MEID_SIGN = "明文MEID";
    public final static String PAL_IMEI_SIGN = "明文IMEI";
    public final static String PAL_FIXEDLINE_SIGN = "明文固话";
    public final static String PAL_EMAIL_SIGN = "明文邮箱";
    public final static String IC_PROVINCE_SIGN = "明文省份";
    public final static String IC_NATIONAL_SIGN = "明文民族";
    public final static String IC_ADDRESS_SIGN = "明文地址";
    public final static String IC_NAME_SIGN = "明文姓名";
    public final static String IC_ENTERPRISENAME_SIGN = "明文企业名称";
    public final static String IC_QQ_SIGN = "明文QQ号";
    public final static String IC_UNIFIEDCREDITCODE_SIGN = "明文社会统一信用代码";
    public final static String IC_BUSINESSLICENSE_SIGN = "明文营业执照";
    public final static String IC_BANKCARD_SIGN = "明文银行卡号";
    public final static String IC_VALIDENTPCODE_SIGN = "明文组织机构代码";
    public final static String IC_TAXATIONNO_SIGN = "明文税务登记证号码";
    public final static String IC_CARNUMBERNO_SIGN = "明文车牌号";
    public final static String IC_VIN_SIGN = "明文车辆识别号码";
    public final static String IC_HMPASSCODE_SIGN = "明文港澳通行证号码";
    public final static String IC_OffICERCARD_SIGN = "明文军官证号码";
    public final static String IC_PASSPORTCARD_SIGN = "明文护照号";
    public final static String IC_ACCOUNTOPENINGPERMITNO_SIGN = "明文开户许可证";
    public final static String IC_IPV6_SIGN = "明文IPv6";
    public final static String IC_IP_SIGN = "明文IP";
    public final static String IC_ICCID_SIGN = "明文ICCID";
    public final static String IC_MSISDN_SIGN = "明文物联网MSISDN";
    public final static String NORMAL_SIGN = "正常";

    //白名单
    public final static String WHITELIST = "白名单";

    //手机号检测
    public final static String PHONEDETECTION = "手机号检测";

    //FTP流量标识
    public final static int FTP_DATA_TRANSFER_PORT = 20;//主动模式FTP服务器数据传输端口
    public final static String FTP_150_OBMDC = "150 Opening BINARY mode data connection";//打开连接
    public final static String FTP_150_OTSD = "150 Ok to send data";//打开连接
    public final static String FTP_226_TRANSFER_COMPLETE = "226";//传输完成
    public final static String FTP_STOR = "STOR";//文件上传
    public final static String FTP_STOR_SIGN = "文件上传";//文件上传
    public final static String FTP_RETR = "RETR";//文件下载
    public final static String FTP_RETR_SIGN = "文件下载";//文件下载
    public final static String FTP_USER = "USER";//FTP用户名
    public final static String FTP_PASS = "PASS";//FTP密码
    public final static String FTP_PORT = "PORT";//主动模式
    public final static String FTP_CWD = "CWD";//改变服务器上的工作目录
    public final static String FTP_257_PATH = "257";//路径名
    public final static String FTP_227_PASV = "227";//被动模式

    //packetbeat流量检测读取方式标识
    public final static String FROM_ES = "1";//从ES读取
    public final static String FROM_DISK = "2";//从本地磁盘读取


    public final static String NETFLOW = "netflow";//清洗标识
    public final static String NetworkSession = "networksession";//网络协议清洗标识

    //清洗结果输出
    public final static String CLEAN_DATA_OUTTYPE_DISK = "1";//输出到本地
    public final static String CLEAN_DATA_OUTTYPE_ES = "2";//输出到ES
    public final static String CLEAN_DATA_OUTTYPE_KAFKA = "3";//输出到Kafka

    //流量文件输入 0:管网适配  1:服务器 2:nginx代理
    public final static String FLOW_DATA_PCAP4J = "0";
    public final static String FLOWDATA_APISERVER = "1";
    public final static String FLOWDATA_NGINXPROXY = "2";

    //0:通用  1:苏州 2:金华 3:永康 4:海南  5:青海  6:国家管网  7:智网科技  8:海南[通用] 9:数安平台 10:中移在线
    public final static String FLOW_DATA_general = "0";
    public final static String FLOW_DATA_Suzhou = "1"; //苏州
    public final static String FLOW_DATA_Jinhua = "2"; //金华
    public final static String FLOW_DATA_Yongkang = "3"; //永康
    public final static String FLOW_DATA_Hainan = "4"; //海南[CSB,请求头携带关键信息]
    public final static String FLOW_DATA_QinHai = "5"; //青海
    public final static String FLOW_DATA_GuanWang = "6"; //国家管网
    public final static String FLOW_DATA_ZhiWangKeJi = "7"; //智网科技
    public final static String FLOW_DATA_HAINAN_UNIVERSAL = "8"; // 海南[通用,非CSB]
    public final static String FLOW_DATA_SECURITY_PLATFORM = "9"; //数安平台
    public final static String FLOW_DATA_CHINA_MOBILE_ONLINE = "10";  //中移在线
    public final static String FLOW_DATA_UNIVERSALWRITE = "11";  //通用[清洗接口信息写入规范和发现表]
    public final static String FLOW_DATA_ShangFei = "12";  //商飞


    //清洗状态码
    public final static String CLIENT_ERROR_STATUS = "1";  //客户端错误
    public final static String CLIENT_CORRECT_STATUS = "0";  //客户端正确
    public final static String SERVER_ERROR_STATUS = "1";  //服务端错误
    public final static String SERVER_CORRECT_STATUS = "0";  //服务端正确
    public final static String API_STATUSCODE_1PREFIX = "1";    //状态码开头为 1
    public final static String API_STATUSCODE_2PREFIX = "2";    //状态码开头为 2
    public final static String API_STATUSCODE_3PREFIX = "3";    //状态码开头为 3
    public final static String API_STATUSCODE_4PREFIX = "4";    //状态码开头为 4
    public final static String API_STATUSCODE_5PREFIX = "5";    //状态码开头为 5

    public final static String API_STATUSCODE_0000 = "0000";    //状态码

    //四种消息列表(REC REQ RESP RET)
    public final static String API_type_REC = "REC";
    public final static String API_type_REQ = "REQ";
    public final static String API_type_RESP = "RESP";
    public final static String API_type_RET = "RET";

    public final static String API_SYSTEM = "北向能力";


    public final static String P_CHECK_SENSITIVEDATA = "p_checkSensitiveData";//通用敏感数据检测方法

    //流量清洗状态
    public final static String FLOW_CLEANING_ON = "0";
    public final static String FLOW_CLEANING_OFF = "1";


    //接口鉴权
    public final static String AUTHMETHOD_COOKIE = "cookie:";
    public final static String AUTHMETHOD_AUTHORIZATION = "Authorization:";
    public final static String AUTHMETHOD_SIGN = "sign:";
    public final static String AUTHMETHOD_TOKEN = "token:";
    public final static String AUTHMETHOD_USER_AGENT = "User-Agent:";

    public final static String AUTHMETHOD_XML = "xml";
    public final static String AUTHMETHOD_JSON = "json";

    public final static String AUTHMETHOD_HTTP = "HTTP";
    public final static String AUTHMETHOD_HTTPS = "HTTPS";

    public final static String AUTHMETHOD_ON = "0";
    public final static String AUTHMETHOD_OFF = "1";


    //告警级别
    public final static String RISK_NOT = "0";// 风险程度无
    public final static String RISK_LOW = "1";// 风险程度低
    public final static String RISK_MIDDLE = "2";// 风险程度中
    public final static String RISK_HIGH = "3";// 风险程度高


    //场景检测
    public final static String scene_zero = "0";//统建
    public final static String scene_one = "1";//永康
    public final static String scene_two = "2";//通用版

    public final static String KAKFA_SCENE_ZERO = "0";//智网
    public final static String KAKFA_SCENE_ONE = "1";//软研院
    public final static String KAKFA_SCENE_TWO = "2";// 中移在线
    public final static String KAKFA_CLEANING_ON = "0";
    public final static String KAKFA_CLEANING_OFF = "1";

    public final static String FLOW_CLEANING_ACCOUNT = "appKey";//清洗账号信息

    //基础路径
    public final static String FLOW_CLEANING_FOUNDATIONPATH = "/service/api";


    //API异常检测字典常量
    public final static String DICT_API_INTERFACE_AUTHENTICATION = "17";    //接口鉴权(金华该场景在流量清洗中执行)
    public final static String DICT_API_INTERFACE_AUTHENTICATION_CHARS = "接口鉴权";  //接口鉴权

    public final static String DICT_API_FILE_SENSITIVE = "24";    // 接口文件敏感内容检测
    public final static String DICT_API_FILE_SENSITIVE_CHARS = "接口文件敏感内容检测";  //接口鉴权

    //接口告警处置处理状态
    public final static String INTERFACE_ALARM_DISPOSAL_UNHANDLED = "0"; //未处理

    //接口发现
    public final static String API_ACCESSDOMAIN_INTRANET = "内网"; //金华市
    public final static String API_ACCESSDOMAIN_INTRANET_CHARS = "1";


    public final static String INTERFACE_INFORMATION_STATUS_RUN = "运行";
    public final static String INTERFACE_INFORMATION_STATUS_STOP = "停用";

    //来源
    public final static String INTERFACE_INFORMATION_SOURCE_SYNC = "1"; //同步
    public final static String INTERFACE_INFORMATION_SOURCE_DISCOVER = "2";//发现

    //缺陷性检测
    public final static String UNAUTHORIZED_ACCESS_AUTHENTICATIONMODE = "Authorization";
    public final static String UNAUTHORIZED_ACCESS_ES = "ElasticSearch未授权访问";
    public final static String UNAUTHORIZED_ACCESS_HADOOP = "Hadoop未授权访问";


    //ES未授权访问
    public final static String UNAUTHORIZED_ACCESS_ES_CAT = "_cat";
    public final static String UNAUTHORIZED_ACCESS_ES_RIVER = "_river";
    public final static String UNAUTHORIZED_ACCESS_ES_SEARCH = "_search";
    public final static String UNAUTHORIZED_ACCESS_ES_NODES = "_nodes";

    //Hadoop未授权访问
    public final static String UNAUTHORIZED_ACCESS_Hadoop_50070 = "50070";
    public final static String UNAUTHORIZED_ACCESS_Hadoop_8020 = "8020";
    public final static String UNAUTHORIZED_ACCESS_HADOOP_CHARS = "hadoop";


    //推送syslog标识
    public final static String EVENT_TYPE = "共享安全"; //共享安全
    public final static String DEVICE_TYPE = "API"; //API
    public final static String EVENT_CITY = "金华市"; //金华市


    // 流量清洗模式,0:前一天 1:前一小时
    public final static String CLEANING_MODE_DAY = "0";
    public final static String CLEANING_MODE_HOUR = "1";

    // 通用开关常量
    public final static String STATUS_ON = "0"; //开启(启用)
    public final static String STATUS_OFF = "1"; //关闭(禁用)

    //接口发现
    public final static String INTERFACEDISCOVERY_DATAFORMAT_HTML = "HTML";
    public final static String INTERFACEDISCOVERY_DATAFORMAT_XML = "XML";
    public final static String INTERFACEDISCOVERY_DATAFORMAT_JSONP = "JSONP";
    public final static String INTERFACEDISCOVERY_DATAFORMAT_JSON = "JSON";

    public final static String INTERFACEDISCOVERY_CATEGORY_LOGINAPI = "1"; //登录API
    public final static String INTERFACEDISCOVERY_CATEGORY_FILEUPLOADAPI = "2";//文件上传API
    public final static String INTERFACEDISCOVERY_CATEGORY_FILEDOWNLOADAPI = "3";//文件下载API
    public final static String INTERFACEDISCOVERY_CATEGORY_DATAACQUISITIONAPI = "4";//数据采集API
    public final static String INTERFACEDISCOVERY_CATEGORY_DATAUSAGEAPI = "5";//数据使用API

    public final static String INTERFACEDISCOVERY_CATEGORY_LOGIN = "login";
    public final static String INTERFACEDISCOVERY_CATEGORY_USER = "user";
    public final static String INTERFACEDISCOVERY_CATEGORY_USERNAME = "username";
    public final static String INTERFACEDISCOVERY_CATEGORY_PASS = "pass";
    public final static String INTERFACEDISCOVERY_CATEGORY_PASSWORD = "password";

    public final static String INTERFACEDISCOVERY_CATEGORY_DATA = "data";
    public final static String INTERFACEDISCOVERY_CATEGORY_QUERY = "query";
    public final static String INTERFACEDISCOVERY_CATEGORY_SEARCH = "search";

    public final static String INTERFACEDISCOVERY_RISK_NOT = "1";// 风险程度无
    public final static String INTERFACEDISCOVERY_RISK_LOW = "2";// 风险程度低
    public final static String INTERFACEDISCOVERY_RISK_MIDDLE = "3";// 风险程度中
    public final static String INTERFACEDISCOVERY_RISK_HIGH = "4";// 风险程度高

    /**
     * @description TODO 管网接口审计采集的接口流量内网IP、弹性IP关联
     * <AUTHOR>
     * @date 2024-11-07 17:45
     */
    public final static Map<String,String> gwIpRelevanceMap(){
        Map<String,String> gwIpRelevanceMap = new HashMap<>();
        //TODO 根据实际清单调整
        gwIpRelevanceMap.put("127.0.0.1","**************");
        return gwIpRelevanceMap;
    }

    // 日志类型为请求 1:请求 2:响应
    public final static String LOG_TYPE_REQUEST = "1";
    public final static String LOG_TYPE_RESPONSE = "4";


}
