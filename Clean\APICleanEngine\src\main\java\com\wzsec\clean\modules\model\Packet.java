package com.wzsec.clean.modules.model;

/**
 * Title: Packet
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/10/30
 */
public class Packet {
    private int time_s;//时间戳（秒）
    private int time_ms;//时间戳（微秒）
    private int pLength;//抓包长度
    private int length;//实际长度
    private byte[] content_byte;//数据包
    private String content;//数据内容
    private String protocol;//协议类型 1字节
    private String sourceip;//原地址 4字节
    private String sourceport;//源端口
    private String desip;//目的地址  4字节
    private String desport;//目的端口
    private String seq_number;// 序号  大小端原因，高低位4个8bit的存放顺序是反的，intel使用小端模式 4字节
    private String ack_number;//确认号，大小端原因，高低位4个8bit的存放顺序是反的，intel使用小端模式 4字节



    private String url;
    private String alarmContent;
    private String risk;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }



    public int getTime_s() {
        return time_s;
    }

    public void setTime_s(int time_s) {
        this.time_s = time_s;
    }

    public int getTime_ms() {
        return time_ms;
    }

    public void setTime_ms(int time_ms) {
        this.time_ms = time_ms;
    }

    public int getpLength() {
        return pLength;
    }

    public void setpLength(int pLength) {
        this.pLength = pLength;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public byte[] getContent_byte() {
        return content_byte;
    }

    public void setContent_byte(byte[] content_byte) {
        this.content_byte = content_byte;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getSourceip() {
        return sourceip;
    }

    public void setSourceip(String sourceip) {
        this.sourceip = sourceip;
    }

    public String getSourceport() {
        return sourceport;
    }

    public void setSourceport(String sourceport) {
        this.sourceport = sourceport;
    }

    public String getDesip() {
        return desip;
    }

    public void setDesip(String desip) {
        this.desip = desip;
    }

    public String getDesport() {
        return desport;
    }

    public void setDesport(String desport) {
        this.desport = desport;
    }

    public String getSeq_number() {
        return seq_number;
    }

    public void setSeq_number(String seq_number) {
        this.seq_number = seq_number;
    }

    public String getAck_number() {
        return ack_number;
    }

    public void setAck_number(String ack_number) {
        this.ack_number = ack_number;
    }
}
