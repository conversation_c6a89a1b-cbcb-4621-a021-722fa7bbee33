package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiEncryptedtraffic implements Serializable {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 请求IP
     */
    private String reqip;

    /**
     * 请求端口
     */
    private String reqport;

    /**
     * 协议
     */
    private String protocol;

    /**
     * 响应IP
     */
    private String resip;

    /**
     * 响应端口
     */
    private String resport;

    /**
     * 调用时间
     */
    private String calltime;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;

}
