package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @date 2023-02-24
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IcCallschedule{

    /** 主键 */
    private Integer id;

    /** 接口编码 */
    private String apicode;

    /** 调用时间 */
    private String calltime;

    /** 调用时段 */
    private String timeframe;

    /** 调用日期 */
    private String calldate;

    /** 账号 */
    private String appkey;

    /** ip */
    private String ip;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

}