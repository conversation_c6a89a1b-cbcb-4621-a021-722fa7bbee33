#!/bin/sh
source /etc/profile
file_name="/logs/restart.log"  #重启脚本的日志，保证可写入，保险一点执行 chmod 777 restart.log
pid=0
proc_num() 
{
    num=`ps -ef | grep 'tcpdump' | grep -v grep | wc -l`  #此处'sh /home/<USER>/run.sh'替代为实际的，尽量准确，避免误kill
    return $num 
}
proc_id()
{  
    pid=`ps -ef | grep 'tcpdump' | grep -v grep | awk '{print $2}'`  #此处'sh /home/<USER>/run.sh'也替代为实际的
} 
proc_num  #执行proc_num()，获取进程数
number=$?  #获取上一函数返回值
if [ $number -eq 0 ]  #如果没有该进程，则重启
then
    sh /logs/tcpdumpstart.sh  #启动流量采集组件tcpdump命令
    sh /logs/backflowfilestart.sh   #启动sshpass脚本传输命令
    proc_id 
    echo ${pid}, `date` >> $file_name  #把重启的进程号、时间 写入日志
fi
