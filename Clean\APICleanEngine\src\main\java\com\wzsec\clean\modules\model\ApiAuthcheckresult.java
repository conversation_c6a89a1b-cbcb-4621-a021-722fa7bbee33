package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 接口鉴权Entity
 * <AUTHOR>
 * @version 2022-08-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiAuthcheckresult{

	private String id;//id
	private String apicode;			// 接口编码
	private String apiname;			// 接口名称
	private String url;				// 接口路径
	private String serviceip;		// 接口服务端IP
	private String serviceport;		// 接口服务端端口
	private String protocol;		// 采用协议(http、https)
	private String reqmethod;		// 请求方式(get、post）
	private String reqformat;		// 请求格式(xml、json、表单)
	private String authmethod;		// 鉴权方式(sign、cookie、token、authorization等)
	private String authinfo;		// 鉴权信息示例
	private String risk;			// 安全风险(0、1、2、3)
	private String inserttime;		// 检测时间
	private String sparefield1;		// 备用字段1
	private String sparefield2;		// 备用字段2
	private String sparefield3;		// 备用字段3
	private String sparefield4;		// 备用字段4


}