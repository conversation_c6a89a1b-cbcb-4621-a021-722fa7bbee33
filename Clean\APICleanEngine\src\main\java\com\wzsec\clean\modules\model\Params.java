package com.wzsec.clean.modules.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * 请求内容
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Params {

    private String reqheader; //请求标头

    private String params; //请求体

    private Map<String, String> head; //请求头

    private String jwtSignatureAlgorithm; // JWT签名算法

}
