package com.wzsec.clean.filter.clean.GuanWang;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 管网流量解析抽取
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
public class GuanWangFlowClean {

    /**
     * @description TODO 管网接口审计采集的接口流量内网IP、弹性IP关联
     * <AUTHOR>
     * @date 2024-11-26 10:50
     */
    public static String networkSegmentMatching(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            byte[] ipBytes = inetAddress.getAddress();
            int firstOctet = ipBytes[0] & 0xFF;
            int secondOctet = ipBytes[1] & 0xFF;
            if (firstOctet == 192 && secondOctet == 168) {
                return "***********";
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return ip;
        }
        return ip;
    }

}
