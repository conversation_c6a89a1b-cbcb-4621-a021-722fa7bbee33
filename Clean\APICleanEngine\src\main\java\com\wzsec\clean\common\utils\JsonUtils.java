package com.wzsec.clean.common.utils;

import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Slf4j
public class JsonUtils {

    /**
     * 定义jackson对象
     */
    private static final ObjectMapper MAPPER = new ObjectMapper();

    /**
     * JSON转MAP(JSON字符串获取key,value为数组)
     *
     * @param content 内容
     * @return {@link Map}<{@link String}, {@link Object[]}>
     */
    private static Map<String, Object[]> jsonToMap(String content) {
        HashMap<String, Object[]> jsonParsMap = new HashMap<>();
        JSONObject data = JSON.parseObject(content);
        List<JSONObject> datas = Lists.newArrayList();
        datas.add(data);
        List<MultiValuedMap<String, Object>> outputList = Lists.newArrayList();
        saveTaskInputParam(datas, outputList);

        for (MultiValuedMap<String, Object> stringObjectMultiValuedMap : outputList) {
            for (String s : stringObjectMultiValuedMap.keySet()) {
                jsonParsMap.put(s, stringObjectMultiValuedMap.get(s).toArray());
            }
        }
        return jsonParsMap;
    }


    /**
     * 递归解析json对象
     *
     * @param jsonObject   json对象
     * @param taskInputMap 任务输入地图
     */
    private static void recurseParseJsonObject(JSONObject jsonObject, MultiValuedMap<String, Object> taskInputMap) {
        //递归取key和value，jsonObject表示datas中每一个对象
        //taskInputMap存放每一个jsonObject解析出来的key和value
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            //如果value类型为Map，那么说明又是一个jsonObject对象，因此需要继续递归
            if (entry.getValue() instanceof Map) {
                jsonObject = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                recurseParseJsonObject(jsonObject, taskInputMap);
            }
            //如果value类型为List，说明是一个JSONArray，那么使用for循环把里面的每一个jsonObject对象再一次做递归
            else if (entry.getValue() instanceof List) {
                List<JSONObject> jsonList = JsonUtils.jsonToList(JSON.toJSONString(entry.getValue()), JSONObject.class);
                for (JSONObject json : jsonList) {
                    recurseParseJsonObject(json, taskInputMap);
                }
            } else {
                //说明value是一个普通的属性，String，Number
                taskInputMap.put(entry.getKey(), entry.getValue());
            }
        }
    }


    /**
     * 保存任务输入参数
     *
     * @param datas      数据
     * @param outputList 输出列表
     */
    private static void saveTaskInputParam(List<JSONObject> datas, List<MultiValuedMap<String, Object>> outputList) {
        for (int i = 0; i < datas.size(); i++) {
            MultiValuedMap<String, Object> taskInputMap = new HashSetValuedHashMap<>();
            JSONObject jsonObject = datas.get(i);
            if (jsonObject == null || jsonObject.isEmpty()) {
                continue;
            }
            recurseParseJsonObject(jsonObject, taskInputMap);
            outputList.add(taskInputMap);
        }
    }


    /**
     * 对象转JSON
     *
     * @param data 数据
     * @return {@link String}
     */
    public static String objectToJson(Object data) {
        try {
            return MAPPER.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("JSON Exception", e);
        }
        return null;
    }

    /**
     * JSON转对象
     *
     * @param jsonData json数据
     * @param beanType 对象中的object类型
     * @return
     */
    public static <T> T jsonToPojo(String jsonData, Class<T> beanType) throws IOException {
        try {
            return MAPPER.readValue(jsonData, beanType);
        } catch (JsonProcessingException e) {
            log.error("json结果集转化为对象异常:{}", e.getLocalizedMessage());
            throw e;
        }
    }


    /**
     * 将json数据转换成pojo对象list
     *
     * @param jsonData json数据
     * @param beanType bean类型
     * @return {@link List}<{@link T}>
     */
    public static <T> List<T> jsonToList(String jsonData, Class<T> beanType) {
        JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, beanType);
        try {
            return MAPPER.readValue(jsonData, javaType);
        } catch (Exception e) {
            log.error("JSON Exception", e);
        }

        return null;
    }


    /**
     * Object转Map
     *
     * @param object 对象
     * @return {@link HashMap}<{@link String}, {@link Object}>
     */
    public static HashMap<String, Object> toHashMap(Object object) {
        HashMap<String, Object> data = new HashMap<>();
        // 将json字符串转换成jsonObject
        JSONObject jsonObject = (JSONObject) object;
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            if (entry == null) {
                continue;
            }
            data.put(entry.getKey(), entry.getValue());
        }
        return data;
    }


    /**
     * 解析json字符串
     *
     * @param str str
     * @return {@link JSONObject}
     */
    public static JSONObject parseStr2JsonObj(String str) {
        JSONObject result = new JSONObject();
        JSONObject strJson = new JSONObject();
        try {
            strJson = JSONObject.parseObject(str);
        } catch (JSONException e) {
            throw new JSONException("参数格式不正确");
        }

        // 遍历每一组键值对
        for (Map.Entry<String, Object> entry : strJson.entrySet()) {
            if (StringUtils.isEmpty(entry.getValue().toString())) {
                continue;
            }

            if (isJsonArray(entry.getValue().toString())) {
                // 如果是json集合那就取集合下面的每一个分别去递归解析
                List list = new ArrayList();
                for (Object o : JSONObject.parseArray(entry.getValue().toString())) {
                    JSONObject jsonObject = o instanceof JSONObject ? ((JSONObject) o) : null;
                    list.add(parseStr2JsonObj(jsonObject.toString()));
                }
                result.put(entry.getKey(), list);
            } else if (isJsonObj(entry.getValue().toString())) {
                // 如果是json对象那就递归解析
                result.put(entry.getKey(), parseStr2JsonObj(entry.getValue().toString()));
            } else {
                // 不是json对象直接赋值
                result.put(entry.getKey(), entry.getValue());
            }
            System.out.println(result);
        }
        return result;

    }


    /**
     * 判断字符串是否是json类型
     *
     * @param str str
     * @return boolean
     */
    private static boolean isJsonObj(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            JSONObject.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 判断字符串是否是json集合
     *
     * @param str str
     * @return boolean
     */
    private static boolean isJsonArray(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            JSONObject.parseArray(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    public static void main(String[] args) throws IOException {
        File file = new File("E:\\TestCase\\JSON\\JSON_TEST.txt");
        String content = FileUtils.readFileToString(file, "UTF-8");
        Map<String, Object[]> jsonParsMap = jsonToMap(content);
        for (String s : jsonParsMap.keySet()) {
            Console.log("key: {}, value: {} ", s, jsonParsMap.get(s));
        }
    }

}

