package com.wzsec.clean.common.utils;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import javax.sql.DataSource;

public class DatabaseConfig {

    static {
        try {
            Class.forName(ConfigurationManager.getProperty("spring.datasource.driver-class-name").trim());
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("加载JDBC驱动程序失败", e);
        }
    }

    public static DataSource createDataSource() {
        HikariConfig config = new HikariConfig();
        String dbUrl = ConfigurationManager.getProperty("spring.datasource.url").trim();
        String dbUserName = ConfigurationManager.getProperty("spring.datasource.username").trim();
        String dbPassword = ConfigurationManager.getProperty("spring.datasource.password").trim();

        config.setJdbcUrl(dbUrl);
        config.setUsername(dbUserName);
        config.setPassword(dbPassword);
        // 重要的连接池参数
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.setMinimumIdle(10);
        config.setMaximumPoolSize(20);
        // 设置连接最大生命周期，应大于数据库的超时时间
        config.setMaxLifetime(3600000); // 1小时
        // 可以设置验证查询，但OceanBase可能不需要（取决于JDBC驱动）
        config.setConnectionTestQuery("SELECT 1");

        return new HikariDataSource(config);
    }
}
