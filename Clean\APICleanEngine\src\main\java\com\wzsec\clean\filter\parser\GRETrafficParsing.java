package com.wzsec.clean.filter.parser;

import com.wzsec.clean.modules.model.PcapFlowCombination;
import lombok.extern.slf4j.Slf4j;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.namednumber.IpNumber;

import java.util.Map;

/**
 * GRE流量解析
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
@Slf4j
public class GRETrafficParsing {

    /**
     * gre解析
     *
     * @param packet 包
     * @param requestTime 请求时间
     * @param pcapFlowCombinationMap Pcap流量组合图
     */
    public static void greAnalysis(Packet packet, String requestTime, Map<String, PcapFlowCombination> pcapFlowCombinationMap) {
        // 检查是否为GRE封装包
        if (packet.contains(IpV4Packet.class)) {
            IpV4Packet ipV4Packet = packet.get(IpV4Packet.class);
            if (ipV4Packet != null && ipV4Packet.getHeader() != null) {
                IpNumber protocol = ipV4Packet.getHeader().getProtocol();
                // GRE协议号为47
                if (protocol.value() == 47) {
                    String srcIp = ipV4Packet.getHeader().getSrcAddr().getHostAddress();
                    String dstIp = ipV4Packet.getHeader().getDstAddr().getHostAddress();

                    log.info("检测到GRE封装包: 源IP={}, 目标IP={}", srcIp, dstIp);

                    if (ipV4Packet.getPayload() != null) {
                        byte[] payload = ipV4Packet.getPayload().getRawData();
                        log.info("GRE封装包负载大小: {} 字节", payload.length);

                        // 创建PcapFlowCombination对象记录GRE信息
                        PcapFlowCombination pcapFlowCombination = new PcapFlowCombination();
                        pcapFlowCombination.setRequest_time(requestTime);
                        pcapFlowCombination.setClient_ip(srcIp);
                        pcapFlowCombination.setServer_ip(dstIp);

                        // 构建详细的GRE信息
                        StringBuilder greInfo = new StringBuilder();
                        greInfo.append("GRE封装流量详情:\n")
                                .append("源IP: ").append(srcIp).append("\n")
                                .append("目标IP: ").append(dstIp).append("\n")
                                .append("协议号: ").append(protocol.value()).append("\n")
                                .append("负载大小: ").append(payload.length).append(" 字节\n");

                        // 提取GRE协议类型和标志位
                        if (payload.length >= 4) {
                            // 使用辅助方法解析GRE头部
                            int protocolType = ((payload[2] & 0xFF) << 8) | (payload[3] & 0xFF);
                            log.info("GRE协议类型: 0x{}", Integer.toHexString(protocolType));

                            // 添加GRE头部详细信息
                            greInfo.append(parseGreHeader(payload));

                            // 计算GRE头部长度
                            int flags = ((payload[0] & 0xFF) << 8) | (payload[1] & 0xFF);
                            boolean checksumPresent = (flags & 0x8000) != 0;
                            boolean routingPresent = (flags & 0x4000) != 0;
                            boolean keyPresent = (flags & 0x2000) != 0;
                            boolean seqNumPresent = (flags & 0x1000) != 0;

                            // 计算GRE头部长度
                            int headerOffset = 4; // 基本GRE头部长度
                            if (checksumPresent || routingPresent) headerOffset += 4;
                            if (keyPresent) headerOffset += 4;
                            if (seqNumPresent) headerOffset += 4;

                            // 尝试解析内部封装的IP包
                            if (payload.length > headerOffset) {
                                try {
                                    // 使用辅助方法解析内部IP包
                                    if (protocolType == 0x0800) { // IPv4
                                        greInfo.append("内部封装协议: IPv4\n");
                                        // 调用VXLANTrafficParsing中方法, 解析内部封装的IP包
                                        greInfo.append(VXLANTrafficParsing.parseInnerIPPacket(payload, headerOffset));
                                    } else if (protocolType == 0x86DD) { // IPv6
                                        greInfo.append("内部封装协议: IPv6\n");
                                    } else {
                                        greInfo.append("内部封装协议: 0x").append(Integer.toHexString(protocolType)).append("\n");
                                    }
                                } catch (Exception e) {
                                    greInfo.append("解析内部封装数据失败: ").append(e.getMessage()).append("\n");
                                }
                            }
                        }

                        // 将GRE信息写入request_data和response_data
                        pcapFlowCombination.setRequest_data(greInfo.toString());
                        pcapFlowCombination.setResponse_data("GRE封装流量详情:\n源IP: " + srcIp +
                                "\n目标IP: " + dstIp +
                                "\n协议号: " + protocol.value() +
                                "\n封装类型: IP/GRE\n负载大小: " + payload.length + " 字节");

                        // 使用时间戳作为唯一标识符
                        String uniqueKey = "GRE_" + System.currentTimeMillis();
                        pcapFlowCombinationMap.put(uniqueKey, pcapFlowCombination);
                    }
                }
            }
        }
    }

    /**
     * 解析GRE头部信息
     *
     * @param grePayload GRE包的负载
     * @return GRE头部信息描述
     */
    private static String parseGreHeader(byte[] grePayload) {
        if (grePayload.length < 4) {
            return "GRE头部数据不完整";
        }

        StringBuilder info = new StringBuilder();

        // 解析GRE标志位
        int flags = ((grePayload[0] & 0xFF) << 8) | (grePayload[1] & 0xFF);
        int protocolType = ((grePayload[2] & 0xFF) << 8) | (grePayload[3] & 0xFF);

        boolean checksumPresent = (flags & 0x8000) != 0;
        boolean routingPresent = (flags & 0x4000) != 0;
        boolean keyPresent = (flags & 0x2000) != 0;
        boolean seqNumPresent = (flags & 0x1000) != 0;
        boolean strictSourceRoute = (flags & 0x0800) != 0;
        int version = flags & 0x0007;

        info.append("GRE版本: ").append(version).append("\n")
                .append("协议类型: 0x").append(Integer.toHexString(protocolType)).append("\n")
                .append("校验和位: ").append(checksumPresent ? "存在" : "不存在").append("\n")
                .append("路由位: ").append(routingPresent ? "存在" : "不存在").append("\n")
                .append("密钥位: ").append(keyPresent ? "存在" : "不存在").append("\n")
                .append("序列号位: ").append(seqNumPresent ? "存在" : "不存在").append("\n")
                .append("严格源路由: ").append(strictSourceRoute ? "是" : "否").append("\n");

        return info.toString();
    }
}
