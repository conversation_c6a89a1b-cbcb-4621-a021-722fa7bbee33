package com.wzsec.clean.common.utils;

import com.wzsec.clean.modules.model.CheckRule;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @ClassName: DBConnection
 * @Description: TODO
 * @date 2020-4-28
 */
public class DBUtils {

    /**
     * @Description 获取MySQL数据库连接
     * <AUTHOR>
     * @date 2020-4-28
     */
    public static Connection getConn() {
        String dbDriver = ConfigurationManager.getProperty("dbDriver").trim();
        String dbUrl = ConfigurationManager.getProperty("dbUrl").trim();
        String dbUserName = ConfigurationManager.getProperty("dbUserName").trim();
        String dbPassword = ConfigurationManager.getProperty("dbPassword").trim();

        Connection conn = null;
        try {
            Class.forName(dbDriver);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        try {
            conn = DriverManager.getConnection(dbUrl, dbUserName, dbPassword); // 注意是三个参数
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return conn;
    }

    /**
     * @Description 释放数据库连接
     * <AUTHOR>
     * @date 2020-4-28
     */
    public static void close(ResultSet rs, Statement st, Connection conn) {
        try {
            if (rs != null) {
                rs.close(); // 关闭结果集
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null) {
                    st.close(); // 关闭Statement
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close(); // 关闭连接
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }

            }

        }

    }

    /***
     * @Description:获取敏感数据策略配置
     * <AUTHOR>
     * @date 2020-4-28
     */
    public static Map<String, String> getSensitiveDataStrategy(String name) {
        Map<String, String> sensitiveDataStrategyMap = new HashMap<>();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn();
            stmt = conn.createStatement();
            StringBuilder sbSQL = new StringBuilder();
            sbSQL.append("select * from sm_sensitivedata where testscenario like '%" + name + "%'");
            rs = stmt.executeQuery(sbSQL.toString());
            while (rs.next()) {
                sensitiveDataStrategyMap.put(rs.getString("sensitivedata"), rs.getString("sensitivedataename"));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            close(rs, stmt, conn);
        }
        return sensitiveDataStrategyMap;
    }


    /***
     * @Description:获取检测规则CheckRuleList
     * <AUTHOR>
     * @date 2020-4-28
     */
    public static List<CheckRule> getCheckRuleList() {
        List<CheckRule> checkRuleList = new ArrayList<CheckRule>();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn();
            stmt = conn.createStatement();
            String sql = "select * from fa_checkrule where rulestatus='1' ";
            rs = stmt.executeQuery(sql);
            while (rs.next()) {
                CheckRule checkRule = new CheckRule();
                checkRule.setId(rs.getInt("id"));
                checkRule.setErule(rs.getString("erule"));
                checkRule.setCrule(rs.getString("crule"));
                checkRule.setExpressionformat(rs.getString("expressionformat"));
                checkRule.setBackupfield1(rs.getString("backupfield1"));
                checkRule.setNotes(rs.getString("notes"));
                checkRuleList.add(checkRule);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            close(rs, stmt, conn);
        }
        return checkRuleList;

    }

    /***
     * @Description:获取检测黑白名单
     * <AUTHOR>
     * @date 2020-4-28
     */
    public static List<String> getCheckBlackAndWhiteList(String purpose, String type) {
        List<String> blackAndWhiteList = new ArrayList<String>();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn();
            stmt = conn.createStatement();
            String sql = "select * from fa_list where purpose='" + purpose + "' and type='" + type + "' ";
            rs = stmt.executeQuery(sql);
            while (rs.next()) {
                blackAndWhiteList.add(rs.getString("name"));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            close(rs, stmt, conn);
        }
        return blackAndWhiteList;

    }
}
