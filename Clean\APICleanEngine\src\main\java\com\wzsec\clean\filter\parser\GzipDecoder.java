package com.wzsec.clean.filter.parser;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPInputStream;

/**
 * GZIP解码器
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Slf4j
public class GzipDecoder {

    /**
     * GZIP适配处理 - 处理HTTP响应中的GZIP压缩内容
     *
     * @param bytes   完整的HTTP响应字节数组
     * @param payload 可能包含GZIP数据的负载字节数组
     * @param content 原始内容字符串
     * @return 处理后的内容字符串
     * @throws Exception 处理过程中可能发生的异常
     */
    public static String gzipAdaptation(byte[] bytes, byte[] payload, String content) {
        if (bytes == null || bytes.length == 0) {
            return content;
        }

        // 将字节数组转换为字符串，用于提取HTTP头信息
        // 使用ISO-8859-1编码确保字节不会被错误解释
        String responseString;
        try {
            responseString = new String(bytes, StandardCharsets.ISO_8859_1);
        } catch (Exception e) {
            return content;
        }

        // 找到响应头和响应体的分隔点 - 更健壮的分隔符检测
        int indexOfSeparator = responseString.indexOf("\r\n\r\n");
        int bodyOffset = 4; // 默认为CRLF+CRLF (\r\n\r\n)

        if (indexOfSeparator == -1) {
            // 尝试查找其他可能的分隔符
            indexOfSeparator = responseString.indexOf("\n\n");
            if (indexOfSeparator != -1) {
                bodyOffset = 2; // LF+LF (\n\n)
            } else {
                // 尝试查找单个空行作为分隔符
                indexOfSeparator = responseString.indexOf("\r\n");
                if (indexOfSeparator != -1 && responseString.length() > indexOfSeparator + 2 &&
                        responseString.charAt(indexOfSeparator + 2) == '\n') {
                    bodyOffset = 3; // CRLF+LF (\r\n\n)
                } else {

                    // 尝试直接查找GZIP魔术字节
                    int gzipIndex = GzipDecoder.findGZIPStartIndex(bytes);
                    if (gzipIndex != -1) {
                        try {
                            byte[] dataToDecompress = new byte[bytes.length - gzipIndex];
                            System.arraycopy(bytes, gzipIndex, dataToDecompress, 0, dataToDecompress.length);
                            byte[] uncompressedData = GzipDecoder.decompressGZIP(dataToDecompress);
                            return new String(uncompressedData, StandardCharsets.UTF_8);
                        } catch (Exception ex) {
                            return content;
                        }
                    }

                    return content;
                }
            }
        }

        // 提取响应头和响应体
        String responseHeader = responseString.substring(0, indexOfSeparator);
        String responseBody = responseString.substring(indexOfSeparator + bodyOffset);

        // 更健壮的GZIP压缩内容检测 - 使用正则表达式匹配不同格式的Content-Encoding头
        boolean isGzipped = false;
        Pattern gzipPattern = Pattern.compile("(?i)content-encoding:\\s*(?:.*,\\s*)?gzip(?:\\s*,.*)?\\s*");
        Matcher gzipMatcher = gzipPattern.matcher(responseHeader);

        if (gzipMatcher.find()) {
            isGzipped = true;
        } else if (responseHeader.toLowerCase().contains("content-encoding:") &&
                responseHeader.toLowerCase().contains("gzip")) {
            // 备用检测方法
            isGzipped = true;
        }

        if (!isGzipped) {
            // 不是GZIP压缩，直接返回原始内容
            return content;
        }

        //log.info("GZIP处理: 检测到GZIP压缩内容, 开始解压");

        try {
            // 检测分块传输编码 - 更健壮的检测
            boolean isChunked = false;
            Pattern chunkedPattern = Pattern.compile("(?i)transfer-encoding:\\s*(?:.*,\\s*)?chunked(?:\\s*,.*)?\\s*");
            Matcher chunkedMatcher = chunkedPattern.matcher(responseHeader);

            if (chunkedMatcher.find()) {
                isChunked = true;
            }

            // 计算HTTP头部在字节数组中的长度
            int headerLengthInBytes = calculateHeaderLength(bytes, indexOfSeparator, bodyOffset);

            // 找到GZIP数据的起始位置 - 改进的逻辑
            int gzipStartIndex = -1;

            // 首先在payload中查找GZIP魔术字节
            if (payload != null && payload.length > 0) {
                gzipStartIndex = GzipDecoder.findGZIPStartIndex(payload);
            }

            // 如果在payload中找不到，尝试使用计算的头部长度
            if (gzipStartIndex == -1 && payload != null && payload.length == bytes.length) {
                gzipStartIndex = headerLengthInBytes;

                // 验证该位置是否确实是GZIP头部
                if (gzipStartIndex < bytes.length - 1 &&
                        !(bytes[gzipStartIndex] == 31 && bytes[gzipStartIndex + 1] == -117)) {
                    gzipStartIndex = -1;
                }
            }

            // 如果仍然找不到，在完整响应中查找
            if (gzipStartIndex == -1) {
                gzipStartIndex = GzipDecoder.findGZIPStartIndex(bytes);
                if (gzipStartIndex != -1) {
                    payload = bytes; // 使用完整响应
                } else {
                    return content; // 未找到GZIP数据，返回原始内容
                }
            }

            // 从gzipStartIndex开始截取GZIP数据
            byte[] dataToDecompress;
            try {
                dataToDecompress = new byte[payload.length - gzipStartIndex];
                System.arraycopy(payload, gzipStartIndex, dataToDecompress, 0, dataToDecompress.length);
            } catch (Exception e) {
                return content;
            }

            // 如果是分块传输，尝试处理分块
            if (isChunked) {
                try {
                    byte[] chunkedProcessed = handleChunkedEncoding(dataToDecompress);
                    // 只有当处理后的数据与原始数据不同时才使用处理后的数据
                    if (chunkedProcessed != null && chunkedProcessed.length > 0 &&
                            (chunkedProcessed.length != dataToDecompress.length)) {
                        dataToDecompress = chunkedProcessed;
                    }
                } catch (Exception e) {
                    log.debug("GZIP处理: 处理分块传输编码失败: {}, 继续尝试直接解压", e.getMessage());
                }
            }

            // 解压缩GZIP数据
            byte[] uncompressedData;
            try {
                uncompressedData = GzipDecoder.decompressGZIP(dataToDecompress);
            } catch (Exception e) {

                // 尝试跳过一些字节后再解压
                if (dataToDecompress.length > 10) {
                    try {
                        // 查找下一个可能的GZIP头部
                        int nextGzipIndex = -1;
                        for (int i = 2; i < dataToDecompress.length - 1; i++) {
                            if (dataToDecompress[i] == 31 && dataToDecompress[i + 1] == -117) {
                                nextGzipIndex = i;
                                break;
                            }
                        }

                        if (nextGzipIndex != -1) {
                            byte[] retryData = new byte[dataToDecompress.length - nextGzipIndex];
                            System.arraycopy(dataToDecompress, nextGzipIndex, retryData, 0, retryData.length);
                            uncompressedData = GzipDecoder.decompressGZIP(retryData);
                        } else {
                            return content;
                        }
                    } catch (Exception e2) {
                        return content;
                    }
                } else {
                    return content;
                }
            }

            // 将解压后的数据转换为字符串，尝试多种编码
            String decompressedContent;
            try {
                // 首先尝试UTF-8编码
                decompressedContent = new String(uncompressedData, StandardCharsets.UTF_8);
                // 简单验证UTF-8解码是否成功
                if (decompressedContent.length() < uncompressedData.length / 2) {
                    log.debug("UTF-8解码可能不正确");
                }
            } catch (Exception e) {
                try {
                    // 如果UTF-8可能不正确，尝试ISO-8859-1
                    decompressedContent = new String(uncompressedData, StandardCharsets.ISO_8859_1);
                } catch (Exception e2) {
                    return content;
                }
            }

            // 返回原始响应头和解压缩后的内容
            return responseHeader + "\r\n\r\n" + decompressedContent;
        } catch (Exception e) {
            // 发生异常时返回原始内容
            return content;
        }
    }

    /**
     * 计算HTTP头部在字节数组中的实际长度
     *
     * @param bytes 完整的HTTP响应字节数组
     * @param indexOfSeparator 分隔符在字符串中的索引位置
     * @param bodyOffset 正文偏移量（分隔符的长度）
     * @return HTTP头部的字节长度
     */
    private static int calculateHeaderLength(byte[] bytes, int indexOfSeparator, int bodyOffset) {
        // 尝试在字节数组中查找分隔符的位置
        for (int i = 0; i < bytes.length - (bodyOffset - 1); i++) {
            boolean found = true;
            if (bodyOffset == 4) { // \r\n\r\n
                if (bytes[i] == '\r' && bytes[i + 1] == '\n' &&
                        bytes[i + 2] == '\r' && bytes[i + 3] == '\n') {
                    return i + 4; // 返回包括分隔符在内的头部长度
                }
            } else { // \n\n
                if (bytes[i] == '\n' && bytes[i + 1] == '\n') {
                    return i + 2; // 返回包括分隔符在内的头部长度
                }
            }
        }
        // 如果找不到，返回字符串索引位置加上偏移量作为估计值
        return indexOfSeparator + bodyOffset;
    }

    /**
     * 处理分块传输编码的数据
     * 将分块传输编码的数据转换为连续的数据流
     *
     * @param chunkedData 分块编码的数据
     * @return 处理后的连续数据流
     */
    private static byte[] handleChunkedEncoding(byte[] chunkedData) {
        if (chunkedData == null || chunkedData.length == 0) {
            return chunkedData;
        }

        try {
            // 尝试查找GZIP头部，如果存在，可能不需要处理分块
            if (GzipDecoder.findGZIPStartIndex(chunkedData) == 0) {
                return chunkedData; // 如果数据以GZIP头部开始，可能已经是处理过的数据
            }

            // 将字节数组转换为字符串以便处理分块
            String chunkedStr = new String(chunkedData, StandardCharsets.ISO_8859_1);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            int index = 0;
            int chunkCount = 0;

            while (index < chunkedStr.length()) {
                // 查找块大小行的结束位置
                int lineEnd = chunkedStr.indexOf("\r\n", index);
                if (lineEnd == -1) {
                    // 尝试查找其他可能的行结束符
                    lineEnd = chunkedStr.indexOf("\n", index);
                    if (lineEnd == -1) {
                        break;
                    }
                }

                // 解析块大小（十六进制）
                String chunkSizeHex = chunkedStr.substring(index, lineEnd).trim();

                // 移除可能存在的分块扩展和注释
                if (chunkSizeHex.contains(";")) {
                    chunkSizeHex = chunkSizeHex.substring(0, chunkSizeHex.indexOf(";"));
                }
                if (chunkSizeHex.contains(" ")) {
                    chunkSizeHex = chunkSizeHex.substring(0, chunkSizeHex.indexOf(" "));
                }

                // 确保chunkSizeHex不为空并且只包含十六进制字符
                if (chunkSizeHex.isEmpty() || !chunkSizeHex.matches("^[0-9A-Fa-f]+$")) {
                    return chunkedData; // 返回原始数据
                }

                // 将十六进制转换为整数
                int chunkSize;
                try {
                    chunkSize = Integer.parseInt(chunkSizeHex, 16);
                } catch (NumberFormatException e) {
                    return chunkedData; // 无法解析块大小，返回原始数据
                }

                // 如果块大小为0，表示分块结束
                if (chunkSize == 0) {
                    break;
                }

                // 计算块内容的起始和结束位置
                int contentStart;
                if (chunkedStr.indexOf("\r\n", index) != -1) {
                    contentStart = lineEnd + 2; // 跳过CRLF
                } else {
                    contentStart = lineEnd + 1; // 跳过LF
                }

                int contentEnd = contentStart + chunkSize;

                // 确保不超出字符串范围
                if (contentEnd > chunkedStr.length()) {
                    // 只处理可用的数据
                    contentEnd = chunkedStr.length();
                }

                // 提取块内容并添加到结果中
                byte[] chunkBytes = chunkedStr.substring(contentStart, contentEnd).getBytes(StandardCharsets.ISO_8859_1);
                outputStream.write(chunkBytes);

                // 移动到下一个块
                if (contentEnd + 2 <= chunkedStr.length() &&
                        chunkedStr.charAt(contentEnd) == '\r' &&
                        chunkedStr.charAt(contentEnd + 1) == '\n') {
                    index = contentEnd + 2; // 跳过CRLF
                } else if (contentEnd + 1 <= chunkedStr.length() &&
                        chunkedStr.charAt(contentEnd) == '\n') {
                    index = contentEnd + 1; // 跳过LF
                } else {
                    // 没有找到预期的行结束符，可能是不完整的数据
                    index = contentEnd;
                }
            }

            // 返回处理后的数据
            return outputStream.toByteArray();
        } catch (Exception e) {
            // 处理分块失败，返回原始数据
            return chunkedData;
        }
    }

    /**
     * 找到GZIP数据的起始位置
     *
     * @param data 数据
     * @return int
     */
    private static int findGZIPStartIndex(byte[] data) {
        for (int i = 0; i < data.length - 1; i++) {
            if (data[i] == 31 && data[i + 1] == -117) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 解压缩GZIP数据
     *
     * @param gzipData gzip数据
     * @return {@link byte[]}
     */
    private static byte[] decompressGZIP(byte[] gzipData) throws Exception {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(gzipData);
             GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        }
    }

}
