package com.wzsec.clean.common.quzrtz.config;

import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.modules.model.PcapTask;
import com.wzsec.clean.modules.service.PcapTaskService;
import org.quartz.*;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuartzManager {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private PcapTaskService taskService;

    @Autowired
    private Scheduler scheduler;

    // @Value("${scheduled.status}")
    // private String status;

    //定时刷新任务corn
    final String corn = "59 0/30 * * * ? ";

    public void init() {
        logger.info("流量审计任务初始化start……");
        try {
            // 获取全部的任务
            List<PcapTask> beans = taskService.getTaskByType(Const.INTERFACE_FLOW_SIGN, Const.FTP_FLOW_SIGN);
            // 创建任务调度
            for (PcapTask bean : beans) {
                // 任务状态为on时，启动定时任务。
                if ("1".equals(bean.getIsvaild())) {
                    addJob(bean);
                }
            }
            scheduler.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("流量审计任务初始化end……");
    }

    /**
     * 添加任务
     *
     * @param bean
     */
    public void addJob(PcapTask bean) {
        try {
            if ("1".equals(bean.getIsvaild())) {
                //流量清洗定时任务从页面调整至后台配置
//                JobDetail job = JobBuilder
//                        .newJob((Class<? extends Job>) Class.forName("cn.ctyun.nfd.filter.PcapFlowCheck"))
//                        .withIdentity(bean.getId()).build();
                JobDetail job = JobBuilder
                        .newJob((Class<? extends Job>) Class.forName(""))
                        .withIdentity(bean.getId()).build();
                // 向任务中传递任务id
                job.getJobDataMap().put("jobId", bean.getId());
                job.getJobDataMap().put("jobName", bean.getTaskname());
                /**
                 * 简单的trigger触发时间：通过Quartz提供一个方法来完成简单的重复调用 cron
                 * Trigger：按照Cron的表达式来给定触发的时间
                 */
                // 2.创建Trigger对象：在什么时间做？
                Trigger trigger = TriggerBuilder.newTrigger().withIdentity(bean.getId())
                        .withSchedule(CronScheduleBuilder.cronSchedule(bean.getChecktime())).build();
                // 3.创建Scheduler对象：在什么时间做什么事？
                scheduler.scheduleJob(job, trigger);
                logger.info(
                        "流量审计任务【" + bean.getTaskname() + "】添加成功，任务id:" + bean.getId() + "，cron:" + bean.getChecktime());
            }
        } catch (SchedulerException | ClassNotFoundException e) {
            // e.printStackTrace();
//            logger.info("流量审计任务【" + bean.getTaskname() + "】添加失败，失败信息：" + e.getMessage());
        }
    }

    /**
     * 修改任务
     *
     * @param bean
     */
    public void updateJob(PcapTask bean) {
        try {
            TriggerKey triggerKey = new TriggerKey(bean.getId());
            CronTriggerImpl trigger = (CronTriggerImpl) scheduler.getTrigger(triggerKey);
            trigger.setCronExpression(bean.getChecktime());
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(bean.getChecktime()).withMisfireHandlingInstructionDoNothing();
            trigger = (CronTriggerImpl) trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
            scheduler.rescheduleJob(triggerKey, trigger);
            logger.info("流量审计任务【" + bean.getTaskname() + "】更新成功，任务id:" + bean.getId() + "，cron:" + bean.getChecktime());
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("流量审计任务【" + bean.getTaskname() + "】更新失败，失败信息：" + e.getMessage());
        }
    }

    /**
     * 删除任务
     *
     * @param bean
     */
    public void deleteJob(PcapTask bean) {
        try {
            TriggerKey triggerKey = new TriggerKey(bean.getId());
            scheduler.unscheduleJob(triggerKey);
            scheduler.deleteJob(JobKey.jobKey(bean.getId()));
            logger.info("流量审计任务【" + bean.getTaskname() + "】删除成功，任务id:" + bean.getId());
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("流量审计任务【" + bean.getTaskname() + "】删除失败，失败信息：" + e.getMessage());
        }
    }

    /**
     * 定时判断ftp任务的job列中的数据是否有更新[30分钟执行一次]
     */
    //@Scheduled(cron = corn)
    public void jobUpdate() {
        // if (!Const.FLOW_CLEANING_ON.equals(status)) {
        //     return;
        // }
        logger.info("流量审计任务更新start……");
        try {
            // 获取全部的任务
            List<PcapTask> beans = taskService.getTaskByType(Const.INTERFACE_FLOW_SIGN, Const.FTP_FLOW_SIGN);
            for (PcapTask bean : beans) {
                if ("1".equals(bean.getIsvaild())) {
                    // 获取容器中一定存在的任务
                    TriggerKey triggerKey = new TriggerKey(bean.getId());
                    CronTriggerImpl trigger = (CronTriggerImpl) scheduler.getTrigger(triggerKey);
                    // 判断任务是否存在，不存在则添加，若存在则对比时间表达式，若与容器中的任务时间表达式不同，则更新
                    if (trigger == null) {
                        addJob(bean);
                    } else {
                        updateJob(bean);
                    }
                } else {
                    deleteJob(bean);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("流量审计任务更新失败，失败信息：" + e.getMessage());
        }
        logger.info("流量审计任务更新end……");
    }
}
