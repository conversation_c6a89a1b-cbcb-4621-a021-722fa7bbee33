package com.wzsec.clean.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzsec.clean.modules.model.PcapFlowCombination;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * API协议检测器，用于根据请求头和响应头数据识别协议和组件API类型。
 * 支持的协议包括：RESTful、GraphQL、WebSocket、MQTT、gRPC、JSON-RPC、XML-RPC、SOAP。
 * 支持的组件包括：ClickHouse、Hadoop、Jenkins、Elasticsearch、Spark、Kafka、InfluxDB、Docker。
 * 所有头字段匹配均忽略大小写。
 *
 * 检测原则：
 * 1. 每种协议和组件都有明确的判定依据，基于协议规范和实际应用场景
 * 2. 检测逻辑从特殊到一般，先检测特定协议，最后检测通用RESTful API
 * 3. 组件检测基于请求路径、响应特征和特定头字段的组合判断
 * 4. 所有字符串匹配均转换为小写以实现大小写不敏感的检测
 *
 */
@Slf4j
public class ApiProtocolDetector {

    // 常量定义
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final String CONTENT_TYPE_XML = "text/xml";
    private static final String CONTENT_TYPE_GRPC = "application/grpc";
    private static final Pattern JSON_RPC_VERSION_PATTERN = Pattern.compile("\"jsonrpc\"\\s*:\\s*\"2.0\"", Pattern.CASE_INSENSITIVE);
    private static final Pattern JSON_RPC_METHOD_PATTERN = Pattern.compile("\"method\"\\s*:", Pattern.CASE_INSENSITIVE);
    private static final Pattern MQTT_PATTERN = Pattern.compile("\\bMQTT\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern HTTP_METHOD_PATTERN = Pattern.compile("^(post|get|put|delete|patch|options|head) .*http/", Pattern.CASE_INSENSITIVE);
    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();

    /**
     * 检测协议和组件
     *
     * @param pcapFlowCombination
     * @return {@code List<String> } 返回协议和组件的组合列表
     */
    public static List<String> detectProtocol(PcapFlowCombination pcapFlowCombination) {
        // 空值检查
        if (pcapFlowCombination == null) {
            log.error("流量解析对象为空");
            return new ArrayList<>();
        }

        // 获取请求和响应数据，转换为小写以支持大小写无关匹配
        String request = pcapFlowCombination.getRequest_data() != null ? pcapFlowCombination.getRequest_data().toLowerCase() : "";
        String response = pcapFlowCombination.getResponse_data() != null ? pcapFlowCombination.getResponse_data().toLowerCase() : "";

        // 创建结果列表
        List<String> result = new ArrayList<>();

        // 检测协议类型
        String protocolType = detectProtocolType(request, response);
        if (!protocolType.isEmpty()) {
            result.add(protocolType);
        }

        // 检测组件类型
        String componentType = detectComponentType(request, response);
        if (!componentType.isEmpty()) {
            result.add(componentType);
        }
        return result;
    }

    /**
     * 检测协议类型。
     * 检测顺序为：
     * 1.JSON-RPC（请求或响应中包含jsonrpc字段和method字段）、
     * 2.GraphQL（请求体包含graphql特征字段）、
     * 3.WebSocket（请求头包含websocket升级字段）、
     * 4.MQTT（请求或响应包含mqtt关键字）、
     * 5.gRPC（内容类型为application/grpc）、
     * 6.XML-RPC（xml格式且包含methodCall字段）、
     * 7.SOAP（xml格式且包含soap特征字段）、
     * 8.RESTful（标准HTTP方法和路径，且内容类型为json或xml）。
     * 检测方式：通过正则表达式和字符串包含判断协议特征字段，优先级依次递减，遇到第一个匹配即返回。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code String } 检测到的协议类型
     */
    private static String detectProtocolType(String request, String response) {
        // JSON-RPC 检测
        if (isJsonRpc(request, response)) {
            return "JSON-RPC";
        }

        // GraphQL 检测
        if (isGraphQl(request)) {
            return "GraphQL";
        }

        // WebSocket 检测
        if (isWebSocket(request)) {
            return "WebSocket";
        }

        // MQTT 检测
        if (isMqtt(request, response)) {
            return "MQTT";
        }

        // gRPC 检测
        if (isGrpc(request, response)) {
            return "gRPC";
        }

        // XML-RPC 检测
        if (isXmlRpc(request, response)) {
            return "XML-RPC";
        }

        // SOAP 检测
        if (isSoap(request, response)) {
            return "SOAP";
        }

        // RESTful 检测（放在最后）
        if (isRestful(request, response)) {
            return "RESTful";
        }
        return "";
    }

    /**
     * 检测组件类型。
     * 检测顺序为：
     * 1.ClickHouse（请求或响应包含clickhouse特征头或关键字）、
     * 2.Hadoop（响应包含hadoop及其组件关键字或请求为webhdfs等特征路径）、
     * 3.Jenkins（响应包含jenkins特征头或请求为jenkins api路径）、
     * 4.Elasticsearch（响应包含elasticsearch集群信息或请求为es特征路径）、
     * 5.Spark（响应包含spark特征关键字或请求为spark web ui路径）、
     * 6.Kafka（响应包含kafka-clients或请求为kafka特征路径）、
     * 7.InfluxDB（响应包含influxdb特征头或请求为influxdb api路径）、
     * 8.Docker（请求或响应包含docker特征关键字）。
     * 检测方式：通过字符串包含和正则表达式判断组件特征，优先级依次递减，遇到第一个匹配即返回。
     * 判定依据：如isClickHouse方法通过请求头和响应内容判断，isHadoop方法通过路径和响应内容判断等，详见各方法注释。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code String } 检测到的组件类型
     */
    private static String detectComponentType(String request, String response) {
        // ClickHouse 检测
        if (isClickHouse(request, response)) {
            return "ClickHouse";
        }

        // Hadoop 检测
        if (isHadoop(request, response)) {
            return "Hadoop";
        }

        // Jenkins 检测
        if (isJenkins(request, response)) {
            return "Jenkins";
        }

        // Elasticsearch 检测
        if (isElasticsearch(request, response)) {
            return "Elasticsearch";
        }

        // Spark 检测
        if (isSpark(request, response)) {
            return "Spark";
        }

        // Kafka 检测
        if (isKafka(request, response)) {
            return "Kafka";
        }

        // InfluxDB 检测
        if (isInfluxDB(request, response)) {
            return "InfluxDB";
        }

        // Docker 检测
        if (isDocker(request, response)) {
            return "Docker";
        }

        return "";  // 未检测到组件
    }

    /**
     * ClickHouse组件检测。
     * 检测依据：
     * 1. 请求头包含x-clickhouse-user或响应头包含x-clickhouse-server-version，且响应中包含clickhouse关键字。
     * 2. 请求参数包含format=json和query=，且响应中包含clickhouse关键字。
     * 3. 请求路径包含/?query=，且包含SQL语句（select/insert/create），且响应中包含clickhouse关键字。
     * 任一条件满足即判定为ClickHouse。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为ClickHouse组件
     */
    private static boolean isClickHouse(String request, String response) {
        return ((request.contains("x-clickhouse-user") || response.contains("x-clickhouse-server-version")) && response.contains("clickhouse")) ||
                (request.contains("format=json") && request.contains("query=") && response.contains("clickhouse")) ||
                (request.contains("/?query=") && (request.contains("select") || request.contains("insert") || request.contains("create")) && response.contains("clickhouse"));
    }

    /**
     * 检测是否为Hadoop组件。
     * 检测方式：判断响应中是否包含“hadoop”及“dfs.namenode”或“dfs.datanode”关键字，
     * 或请求路径/参数是否包含WebHDFS、Knox Gateway、JMX、conf、user.name、op等特征。
     * 判定依据：满足上述任一条件即判定为Hadoop组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为Hadoop组件
     */
    private static boolean isHadoop(String request, String response) {
        return (response.contains("hadoop") && (response.contains("dfs.namenode") || response.contains("dfs.datanode"))) ||
                (request.contains("/webhdfs/v1") || request.contains("/webhdfs/v2")) ||
                (request.contains("/gateway/default/webhdfs/v1")) ||
                (request.contains("/jmx") && response.contains("hadoop")) ||
                (request.contains("/conf") && response.contains("hadoop.security")) ||
                (request.contains("user.name=") && request.contains("op=") && response.contains("hadoop"));
    }

    /**
     * 检测是否为Jenkins组件。
     * 检测方式：判断响应中是否包含Jenkins相关头（如x-jenkins、jenkins-crumb、x-hudson、jenkins-version、jenkins-session）及“jenkins”关键字，
     * 或请求路径/参数是否包含Jenkins API、作业、CSRF保护等特征。
     * 判定依据：满足上述任一条件即判定为Jenkins组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为Jenkins组件
     */
    private static boolean isJenkins(String request, String response) {
        return ((response.contains("x-jenkins") || response.contains("jenkins-crumb") || response.contains("x-hudson")) && response.contains("jenkins")) ||
                (request.contains("/api/json") && response.contains("jenkins")) ||
                (request.contains("/job/") && (request.contains("/api/") || request.contains("/build"))) ||
                (request.contains("/crumbIssuer/") || response.contains("_crumb=")) ||
                (response.contains("jenkins-version") || response.contains("jenkins-session"));
    }

    /**
     * 检测是否为Elasticsearch组件。
     * 检测方式：判断响应中是否包含“cluster_name”、“elasticsearch”、“number_of_nodes”关键字，
     * 或请求路径/参数是否包含Elasticsearch特有端点（如/_search、/_bulk、/_mapping、/_analyze）及查询语法。
     * 判定依据：满足上述任一条件即判定为Elasticsearch组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为Elasticsearch组件
     */
    private static boolean isElasticsearch(String request, String response) {
        return (response.contains("cluster_name") && response.contains("elasticsearch") && response.contains("number_of_nodes")) ||
                (request.contains("/_search") || request.contains("/_bulk") || request.contains("/_mapping") || request.contains("/_analyze")) ||
                (request.contains("content-type: application/json") &&
                        (request.contains("\"query\":") || request.contains("\"match\":") || request.contains("\"bool\":") ||
                                request.contains("\"term\":") || request.contains("\"terms\":") || request.contains("\"range\":")) &&
                        response.contains("elasticsearch"));
    }

    /**
     * 检测是否为Spark组件。
     * 检测方式：判断响应中是否包含“spark application”及“sparkcontext”或“sparkmaster”关键字，
     * 或请求路径/参数是否包含Spark Web UI端点（如/jobs/、/stages/、/applications/、/executors）及“spark”关键字，
     * 或响应包含spark-version、spark-ui-url。
     * 判定依据：满足上述任一条件即判定为Spark组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为Spark组件
     */
    private static boolean isSpark(String request, String response) {
        return (response.contains("spark application") && (response.contains("sparkcontext") || response.contains("sparkmaster"))) ||
                (request.contains("/jobs/") && response.contains("spark")) ||
                (request.contains("/stages/") && response.contains("spark")) ||
                (request.contains("/applications/") && response.contains("spark")) ||
                (request.contains("/executors") && response.contains("spark")) ||
                (response.contains("spark-version") || response.contains("spark-ui-url"));
    }

    /**
     * 检测是否为Kafka组件。
     * 检测方式：判断响应中是否包含“kafka-clients”及请求或响应包含“kafka”关键字，
     * 或请求路径/参数是否包含Kafka特有端点（如/topics、/brokers、/consumer）、内容类型、配置参数等。
     * 判定依据：满足上述任一条件即判定为Kafka组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为Kafka组件
     */
    private static boolean isKafka(String request, String response) {
        return (response.contains("kafka-clients") && (request.contains("kafka") || response.contains("kafka"))) ||
                (request.contains("/topics") && response.contains("kafka")) ||
                (request.contains("/brokers") && response.contains("kafka")) ||
                (request.contains("/consumer") && response.contains("kafka")) ||
                (request.contains("content-type: application/vnd.kafka")) ||
                (request.contains("bootstrap.servers") && request.contains("group.id") && (request.contains("kafka") || response.contains("kafka")));
    }

    /**
     * 检测是否为InfluxDB组件。
     * 检测方式：判断响应中是否包含“x-influxdb-version”及“influxdb”关键字，
     * 或请求路径/参数是否包含InfluxDB API端点（如/query、/write、/ping）、内容类型、参数等。
     * 判定依据：满足上述任一条件即判定为InfluxDB组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为InfluxDB组件
     */
    private static boolean isInfluxDB(String request, String response) {
        return (response.contains("x-influxdb-version") && response.contains("influxdb")) ||
                (request.contains("/query") && request.contains("q=") && (request.contains("select") || request.contains("show") || request.contains("from"))) ||
                (request.contains("/write") && request.contains("precision=")) ||
                (request.contains("/ping") && response.contains("influxdb")) ||
                (request.contains("application/x-www-form-urlencoded") && request.contains("db=") && response.contains("influxdb"));
    }

    /**
     * 检测是否为Docker组件。
     * 检测方式：判断响应中是否包含“docker-distribution-api-version”及“docker”关键字，
     * 或请求路径/参数是否包含Docker API端点（如/v1/containers、/v2/images、/containers/json、/images/json）、认证头、内容类型等。
     * 判定依据：满足上述任一条件即判定为Docker组件。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为Docker组件
     */
    private static boolean isDocker(String request, String response) {
        return (response.contains("docker-distribution-api-version") && response.contains("docker")) ||
                (request.contains("/v1/containers") || request.contains("/v1/images") || request.contains("/v2/containers") || request.contains("/v2/images")) ||
                (request.contains("/containers/json") || request.contains("/images/json")) ||
                (request.contains("x-registry-auth") && (request.contains("docker") || response.contains("docker"))) ||
                (request.contains("application/vnd.docker") || response.contains("application/vnd.docker"));
    }

    /**
     * 检测是否为JSON-RPC协议
     * 检测方式：
     * 1. 首先检查请求和响应的内容类型是否均为application/json
     * 2. 使用正则表达式检查请求中是否包含jsonrpc:"2.0"和method字段
     * 3. 尝试解析请求体为JSON，并验证是否包含jsonrpc和method字段
     *
     * 判定依据：
     * - 请求和响应的Content-Type必须为application/json
     * - 请求必须包含jsonrpc字段，且值为"2.0"
     * - 请求必须包含method字段
     * - 请求可能包含params字段（可选）
     * - 响应通常包含result或error字段
     *
     * JSON-RPC 2.0规范要求这些特定字段，缺少任何必需字段都不符合规范。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为JSON-RPC协议
     */
    private static boolean isJsonRpc(String request, String response) {
        // 检查内容类型
        if (!request.contains(CONTENT_TYPE_JSON) || !response.contains(CONTENT_TYPE_JSON)) {
            return false;
        }

        // 使用正则表达式检查关键字段
        if (!JSON_RPC_VERSION_PATTERN.matcher(request).find() || !JSON_RPC_METHOD_PATTERN.matcher(request).find()) {
            return false;
        }

        // 尝试解析JSON并验证字段
        try {
            JsonNode json = JSON_MAPPER.readTree(request);
            // 验证必需字段
            return json.has("jsonrpc") &&
                    json.get("jsonrpc").asText().equals("2.0") &&
                    json.has("method");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检测是否为GraphQL协议
     * 检测方式：
     * 1. 判断请求是否以POST方法开头（GraphQL通常使用POST方法）
     * 2. 检查请求URL或路径中是否包含graphql关键字
     * 3. 检查请求体或参数中是否包含GraphQL特有的操作类型（query、mutation或subscription）
     * 4. 检查请求头中是否包含application/graphql或application/json内容类型
     *
     * 判定依据：
     * - GraphQL请求通常使用POST方法
     * - 请求URL通常包含/graphql路径
     * - 请求体中必须包含query、mutation或subscription关键字之一
     * - 请求可能包含variables字段（可选）
     * - 内容类型通常为application/graphql或application/json
     *
     * GraphQL是一种查询语言和运行时，用于API中获取数据，其特征允许客户端精确指定所需数据。
     * 请求格式非常特定，包含操作类型和字段选择器，这使其与其他API协议明显区分。
     *
     * @param request 请求数据
     * @return {@code boolean} 是否为GraphQL协议
     */
    private static boolean isGraphQl(String request) {
        // 检查HTTP方法（GraphQL通常使用POST）
        boolean isPostMethod = request.startsWith("post ");

        // 检查URL或路径中是否包含graphql关键字
        boolean hasGraphQLPath = request.contains("/graphql") || request.contains("graphql");

        // 检查请求体中是否包含GraphQL特有的操作类型
        boolean hasOperation = request.contains("query=") ||
                request.contains("mutation=") ||
                request.contains("subscription=") ||
                request.contains("\"query\":") ||
                request.contains("\"mutation\":") ||
                request.contains("\"subscription\":");

        // 检查内容类型
        boolean hasGraphQLContentType = request.contains("application/graphql") ||
                (request.contains(CONTENT_TYPE_JSON) && hasOperation);

        // GraphQL请求必须满足：POST方法 + GraphQL路径 + 操作类型
        return isPostMethod && hasGraphQLPath && hasOperation;
    }

    /**
     * 检测是否为WebSocket协议
     * 检测方式：
     * 1. 检查请求头中是否包含WebSocket升级相关字段
     * 2. 验证'upgrade: websocket'头是否存在
     * 3. 验证'connection: upgrade'头是否存在
     * 4. 检查是否包含'sec-websocket-key'和'sec-websocket-version'头
     *
     * 判定依据：
     * - WebSocket握手必须包含'upgrade: websocket'头
     * - WebSocket握手必须包含'connection: upgrade'头
     * - WebSocket握手通常包含'sec-websocket-key'和'sec-websocket-version'头
     * - WebSocket是基于HTTP的协议升级机制，初始握手使用HTTP，然后升级为WebSocket
     *
     * WebSocket协议(RFC 6455)规定了特定的握手过程，包括这些必需的头字段。
     * 这些头字段的存在是识别WebSocket连接尝试的可靠指标。
     *
     * @param request 请求数据
     * @return {@code boolean} 是否为WebSocket协议
     */
    private static boolean isWebSocket(String request) {
        // 基本WebSocket升级头检查
        boolean hasUpgradeHeader = request.contains("upgrade: websocket");
        boolean hasConnectionHeader = request.contains("connection: upgrade");

        // 额外的WebSocket特定头检查（增强可靠性）
        boolean hasWebSocketKey = request.contains("sec-websocket-key:");
        boolean hasWebSocketVersion = request.contains("sec-websocket-version:");

        // WebSocket握手必须包含升级和连接头
        boolean isWebSocketHandshake = hasUpgradeHeader && hasConnectionHeader;

        // 如果同时具有基本头和至少一个特定WebSocket头，则更有可能是WebSocket
        return isWebSocketHandshake && (hasWebSocketKey || hasWebSocketVersion);
    }

    /**
     * 检测是否为MQTT协议
     * 检测方式：
     * 1. 使用正则表达式判断请求和响应中是否均包含MQTT关键字
     * 2. 检查是否包含MQTT特有的控制包类型（CONNECT、PUBLISH、SUBSCRIBE等）
     * 3. 检查是否包含MQTT协议级别和客户端标识符
     *
     * 判定依据：
     * - 请求和响应中均匹配MQTT_PATTERN模式（包含MQTT关键字）
     * - 可能包含MQTT特有的控制包类型标识，如CONNECT、PUBLISH、SUBSCRIBE、PINGREQ等
     * - 可能包含协议名称和级别标识，如MQIsdp（MQTT 3.1）或MQTT（MQTT 3.1.1及以上）
     * - 可能包含client_id、keep_alive、clean_session等MQTT特有参数
     *
     * MQTT是一种轻量级的发布/订阅消息传输协议，广泛应用于IoT设备通信。
     * 它有明确的协议标识和控制包类型，使其在网络流量中易于识别。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为MQTT协议
     */
    private static boolean isMqtt(String request, String response) {
        // 基本MQTT关键字检查
        boolean hasMqttKeyword = MQTT_PATTERN.matcher(request).find() && MQTT_PATTERN.matcher(response).find();

        if (!hasMqttKeyword) {
            return false;
        }

        // 检查MQTT特有的控制包类型
        boolean hasMqttPacketType = request.contains("connect") ||
                request.contains("publish") ||
                request.contains("subscribe") ||
                request.contains("unsubscribe") ||
                request.contains("pingreq") ||
                request.contains("disconnect");

        // 检查MQTT协议级别和客户端标识符
        boolean hasMqttProtocolIdentifiers = request.contains("mqisdp") ||
                request.contains("protocol_level") ||
                request.contains("client_id=") ||
                request.contains("keep_alive");

        // MQTT流量必须包含MQTT关键字，且通常包含控制包类型或协议标识符
        return hasMqttKeyword && (hasMqttPacketType || hasMqttProtocolIdentifiers);
    }

    /**
     * 检测是否为gRPC协议
     * 检测方式：
     * 1. 检查请求和响应的内容类型是否为'application/grpc'
     * 2. 检查是否包含gRPC特有的HTTP/2头字段
     * 3. 检查是否包含gRPC特有的状态码和元数据
     *
     * 判定依据：
     * - 请求和响应的Content-Type必须为'application/grpc'
     * - 请求通常使用HTTP/2协议（可能包含'http/2'标识）
     * - 请求路径通常遵循'/[package].[service]/[method]'格式
     * - 响应可能包含'grpc-status'和'grpc-message'头
     * - 可能包含'grpc-encoding'（如'gzip'、'identity'）和'grpc-accept-encoding'头
     *
     * gRPC是一种基于HTTP/2和Protocol Buffers的高性能RPC框架，
     * 它使用特定的内容类型和头字段，使其在网络流量中易于识别。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为gRPC协议
     */
    private static boolean isGrpc(String request, String response) {
        // 检查内容类型
        boolean hasGrpcContentType = request.contains(CONTENT_TYPE_GRPC) && response.contains(CONTENT_TYPE_GRPC);

        if (!hasGrpcContentType) {
            return false;
        }

        // 检查gRPC特有的HTTP/2头字段
        boolean hasGrpcHeaders = response.contains("grpc-status:") ||
                response.contains("grpc-message:") ||
                request.contains("grpc-encoding:") ||
                request.contains("grpc-accept-encoding:");

        // 检查HTTP/2协议标识
        boolean isHttp2 = request.contains("http/2") || request.contains("h2");

        // 检查gRPC路径格式（通常为/package.service/method）
        boolean hasGrpcPath = request.matches(".*/[a-zA-Z][\\w\\.]+\\/[a-zA-Z][\\w\\.]+.*");

        // gRPC必须有正确的内容类型，且通常具有特定的头字段或HTTP/2标识
        return hasGrpcContentType && (hasGrpcHeaders || isHttp2 || hasGrpcPath);
    }

    /**
     * 检测是否为XML-RPC协议
     * 检测方式：判断请求和响应的内容类型是否均为'text/xml'，且请求中包含<methodCall>标签和<params>标签。
     * 判定依据：满足上述所有条件即判定为XML-RPC协议。XML-RPC使用标准的XML格式，
     * 请求中必须包含methodCall元素（注意大小写），以及可能的params元素。
     * XML-RPC规范要求标签名称区分大小写，正确的标签名为methodCall而非methodcall。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为XML-RPC协议
     */
    private static boolean isXmlRpc(String request, String response) {
        // XML-RPC规范中标签名称区分大小写，正确的标签名为methodCall而非methodcall
        // 但为了兼容可能的错误实现，同时检查两种写法
        return request.contains(CONTENT_TYPE_XML) &&
                (request.contains("<methodcall>") || request.contains("<methodCall>")) &&
                (request.contains("<params>") || request.contains("<param>")) &&
                response.contains(CONTENT_TYPE_XML);
    }

    /**
     * 检测是否为SOAP协议
     * 检测方式：
     * 1. 检查请求和响应的内容类型是否为'text/xml'或'application/soap+xml'
     * 2. 检查请求中是否包含SOAP特征命名空间和标签
     * 3. 验证是否包含SOAP Envelope、Body等核心元素
     *
     * 判定依据：
     * - SOAP请求和响应的Content-Type通常为'text/xml'或'application/soap+xml'
     * - SOAP消息必须包含Envelope根元素（可能带有命名空间前缀，如soapenv:Envelope）
     * - SOAP消息通常包含Body元素，可能包含Header元素
     * - SOAP消息可能包含xmlns:soap或xmlns:soapenv命名空间声明
     *
     * SOAP是一种基于XML的协议，用于在Web服务中交换结构化信息。
     * 它有严格的消息格式要求，包括必需的元素和命名空间，这使其易于识别。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为SOAP协议
     */
    private static boolean isSoap(String request, String response) {
        // 检查内容类型
        boolean hasXmlContentType = (request.contains(CONTENT_TYPE_XML) || request.contains("application/soap+xml")) &&
                (response.contains(CONTENT_TYPE_XML) || response.contains("application/soap+xml"));

        if (!hasXmlContentType) {
            return false;
        }

        // 检查SOAP特征标签和命名空间
        boolean hasSoapEnvelope = request.contains("<soapenv:envelope") ||
                request.contains("<soap:envelope") ||
                request.contains("<envelope>");

        boolean hasSoapBody = request.contains("<soapenv:body") ||
                request.contains("<soap:body") ||
                request.contains("<body>");

        boolean hasSoapNamespace = request.contains("xmlns:soap=") ||
                request.contains("xmlns:soapenv=") ||
                request.contains("xmlns=\"http://schemas.xmlsoap.org\"");

        // SOAP消息必须包含Envelope元素，通常还包含Body元素或SOAP命名空间
        return hasSoapEnvelope && (hasSoapBody || hasSoapNamespace);
    }

    /**
     * 检测是否为RESTful协议
     * 检测方式：
     * 1. 首先判断请求是否符合标准HTTP方法格式（GET/POST/PUT/DELETE/PATCH等）
     * 2. 检查请求路径是否符合RESTful风格（如包含资源路径、ID等）
     * 3. 检查内容类型是否为常见RESTful格式（如application/json、application/xml等）
     * 4. 检查响应状态码是否为标准HTTP状态码
     * 5. 排除其他特定协议的特征
     *
     * 判定依据：
     * - 请求必须使用标准HTTP方法（GET/POST/PUT/DELETE/PATCH等）
     * - 请求路径通常包含资源名称，可能包含ID（如/users/123）
     * - 响应通常包含状态码和内容类型头
     * - 不符合其他特定协议（如JSON-RPC、GraphQL等）的特征
     *
     * 该检测作为兜底检测，优先级最低。RESTful API是一种架构风格而非严格协议，
     * 因此判定标准相对宽松，主要基于HTTP方法和资源路径特征。
     *
     * @param request 请求数据
     * @param response 响应数据
     * @return {@code boolean} 是否为RESTful协议
     */
    private static boolean isRestful(String request, String response) {
        // 检查 HTTP 方法和协议
        if (!HTTP_METHOD_PATTERN.matcher(request).find()) {
            return false;
        }

        // 检查是否包含常见RESTful内容类型
        boolean hasRestfulContentType = request.contains(CONTENT_TYPE_JSON) ||
                response.contains(CONTENT_TYPE_JSON) ||
                request.contains(CONTENT_TYPE_XML) ||
                response.contains(CONTENT_TYPE_XML) ||
                request.contains("application/hal+json") ||
                response.contains("application/hal+json");

        // 检查是否包含常见RESTful响应状态码
        boolean hasStatusCode = response.contains("200 ok") ||
                response.contains("201 created") ||
                response.contains("204 no content") ||
                response.contains("400 bad request") ||
                response.contains("401 unauthorized") ||
                response.contains("403 forbidden") ||
                response.contains("404 not found") ||
                response.contains("500 internal");

        // 如果同时具有RESTful内容类型和状态码特征，则很可能是RESTful API
        if (hasRestfulContentType && hasStatusCode) {
            return true;
        }

        // 排除其他特定协议
        return !isJsonRpc(request, response) &&
                !isGraphQl(request) &&
                !isGrpc(request, response) &&
                !isXmlRpc(request, response) &&
                !isSoap(request, response) &&
                !isWebSocket(request) &&
                !isMqtt(request, response);
    }
}
