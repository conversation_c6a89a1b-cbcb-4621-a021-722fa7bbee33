package com.wzsec.clean.modules.service.impl;

import com.wzsec.clean.modules.dao.PcapDao;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.PcapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: PcapServiceImpl
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/9/3
 */
@Service
public class PcapServiceImpl implements PcapService {

    @Autowired
    private PcapDao pcapDao;

    /**
     * @Description: 敏感数据检测管理查询
     * <AUTHOR>
     * @date 2020年09月03日
     */
    @Override
    public HashMap<String, String> selectSensitiveData(String signSensitive) {
        HashMap<String, String> hashMap = new HashMap<>();
        List<SMSensitiveData> SMSensitiveDataList = pcapDao.selectSensitiveData(signSensitive);
        for (SMSensitiveData sensitiveData : SMSensitiveDataList) {
            hashMap.put(sensitiveData.getSensitivedata(), sensitiveData.getSensitivedataename());
        }
        return hashMap;
    }

    /**
     * 保存接口流量检测结果
     *
     * @param pcapData
     */
    @Override
    public void saveResult(PcapData pcapData) {
        pcapDao.saveResult(pcapData);
    }

    /**
     * 保存接口流量任务
     *
     * @param map
     */
    @Override
    public void saveTask(HashMap<String, String> map) {
        pcapDao.saveTask(map);
    }

    /**
     * 结果类型
     *
     * @return
     */
    @Override
    public Map<String, Integer> selectResultType() {
        Map<String, Integer> resMap = new HashMap<>();
        List<Map<String, Object>> list = pcapDao.selectResultType();
        for (Map<String, Object> map : list) {
            resMap.put(map.get("resulttype").toString(), Integer.parseInt(map.get("id").toString()));
        }
        return resMap;
    }

    /**
     * 手机号检测白名单查询
     *
     * @param phonedetection
     * @param whitelist
     * @return
     */
    @Override
    public List<String> getFileListInfo(String phonedetection, String whitelist) {
        return pcapDao.getFileListInfo(phonedetection, whitelist);
    }

    /**
     * 根据用户id查询结果类型表
     *
     * @param resyltTypeNum
     * @return
     */
    @Override
    public Map<String, Integer> getResultTypeById(Integer resyltTypeNum) {
        Map<String, Integer> resMap = new HashMap<>();
        Map<String, Object> map = pcapDao.getResultTypeById(resyltTypeNum);
        resMap.put(map.get("resulttype").toString(), Integer.parseInt(map.get("resultweight").toString()));
        return resMap;
    }

    /**
     * 保存详情信息
     *
     * @param pcapData
     */
    @Override
    public void saveDetailResult(PcapData pcapData) {
        pcapDao.saveDetailResult(pcapData);
    }

    /**
     * 保存鉴权信息
     *
     * @param apiAuthcheckresult
     */
    @Override
    public void saveAuthcheckresult(ApiAuthcheckresult apiAuthcheckresult) {
        pcapDao.saveAuthcheckresult(apiAuthcheckresult);
    }

    /**
     * 保存异常调用时段
     *
     * @param icCallschedule
     */
    @Override
    public void saveIcCallschedule(IcCallschedule icCallschedule) {
        pcapDao.saveIcCallschedule(icCallschedule);
    }

    /**
     * 保存接口信息表
     *
     * @param icInterfaceInfo
     */
    @Override
    public void saveIcInterfaceinfo(IcInterfaceInfo icInterfaceInfo) {
        pcapDao.saveIcInterfaceinfo(icInterfaceInfo);
    }


    /**
     * 保存接口信息表(青海)
     *
     * @param icInterfaceInfo
     */
    @Override
    public void saveIcInterfaceinfoByQinHai(IcInterfaceInfo icInterfaceInfo) {
        pcapDao.saveIcInterfaceinfoByQinHai(icInterfaceInfo);
    }

    /**
     * 保存接口发现
     *
     * @param apiApidiscovery
     */
    @Override
    public void saveApidiscovery(Apidiscovery apiApidiscovery) {
        pcapDao.saveApidiscovery(apiApidiscovery);
    }


    /**
     * 保存接口发现(数安平台)
     *
     * @param apiDiscoverySecurityPlatform
     */
    @Override
    public void saveApiDiscovery_SecurityPlatform(ApiDiscoverySecurityPlatform apiDiscoverySecurityPlatform) {
        pcapDao.saveApiDiscovery_SecurityPlatform(apiDiscoverySecurityPlatform);
    }

    /**
     * 保存模型配置
     *
     * @param icModelconfig
     */
    @Override
    public void saveIcModelconfig(IcModelconfig icModelconfig) {
        pcapDao.saveIcModelconfig(icModelconfig);
    }

    /**
     * 更新模型配置
     *
     * @param icModelconfig
     */
    @Override
    public void update(IcModelconfig icModelconfig) {
        pcapDao.update(icModelconfig);
    }

    /**
     * 保存其他事件检测信息
     *
     * @param apiOtherRiskDetect
     */
    @Override
    public void saveOtherRiskDetect(ApiOtherRiskDetect apiOtherRiskDetect) {
        pcapDao.saveOtherRiskDetect(apiOtherRiskDetect);
    }

    /**
     * 保存FTP结果信息
     *
     * @param ftpData
     */
    @Override
    public void saveFtpResult(FTPData ftpData) {
        pcapDao.saveFtpResult(ftpData);
    }


    /**
     * 保存HTTP结果概要信息
     *
     * @param httpResult
     */
    @Override
    public void saveHttpResult(HTTPResult httpResult) {
        pcapDao.saveHttpResult(httpResult);
    }


    /**
     * 保存HTTP结果详情信息
     *
     * @param httpResultDetail
     */
    @Override
    public void saveHttpResultDetail(HTTPResultDetail httpResultDetail) {
        pcapDao.saveHttpResultDetail(httpResultDetail);
    }

    /**
     * 保存FTP结果详情信息
     *
     * @param ftpData
     */
    @Override
    public void saveFtpResultDetail(FTPResultDetail ftpData) {
        pcapDao.saveFtpResultDetail(ftpData);
    }


    /**
     * 保存FTP结果概要信息
     *
     * @param ftpResult
     */
    @Override
    public void saveFTPResult(FTPResult ftpResult) {
        pcapDao.saveFTPResult(ftpResult);
    }


    /**
     * 手机号检测白名单查询
     *
     * @return
     */
    @Override
    public List<String> getAuthcheckresultApicode() {
        return pcapDao.selectAuthcheckresultApicode();
    }

    /**
     * 通过接口标识查询接口编码
     *
     * @param interfaceIdentification
     */
    @Override
    public String queryInterfaceCoding(String interfaceIdentification) {
        return pcapDao.queryInterfaceCoding(interfaceIdentification);
    }

    /**
     * 通过接口编码查询是否入库
     *
     * @param apicode
     */
    @Override
    public int queryNumberInterfaces(String apicode) {
        return pcapDao.queryNumberInterfaces(apicode);
    }

    /**
     * 通过接口标识查询模型配置接口编码
     *
     * @param interfaceIdentification
     */
    @Override
    public Map<String, String> queryIcModelconfig(String interfaceIdentification) {
        return pcapDao.queryIcModelconfig(interfaceIdentification);
    }

    /**
     * 通过接口编码查询接口名称
     *
     * @param apicode
     */
    @Override
    public String getInterfaceName(String apicode) {
        return pcapDao.getInterfaceName(apicode);
    }

    /**
     * 接口编码查询
     *
     * @return
     */
    @Override
    public List<String> selectApicode() {
        return pcapDao.selectApicode();
    }

    /**
     * 获取接口发现表
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getInterfaceDiscoveryTable() {
        return pcapDao.getInterfaceDiscoveryTable();
    }

    /**
     * 获取接口发现表(数安平台)
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getInterfaceDiscoverySecurityPlatform() {
        return pcapDao.getInterfaceDiscoverySecurityPlatform();
    }


}
