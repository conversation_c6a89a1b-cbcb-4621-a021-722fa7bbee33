package com.wzsec.clean.kafka.service;


import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.DateUtils;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.wzsec.clean.modules.service.PcapService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class KafkaMessageConsumer {

    private static final Logger LOG = LoggerFactory.getLogger(KafkaMessageConsumer.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private PcapService pcapService;

    @Autowired
    private RestHighLevelClient client;

    private static KafkaMessageConsumer kafkaMessageConsumer;

    @PostConstruct
    public void init() {
        kafkaMessageConsumer = this;
        kafkaMessageConsumer.client = this.client;
        kafkaMessageConsumer.jdbcTemplate = this.jdbcTemplate;
        kafkaMessageConsumer.pcapService = this.pcapService;
    }

    //TODO 监听器 实时消费
//    @KafkaListener(topics = {"MONITOR_RISK_ALARM_DATA"})
    public static void receiveThree(ConsumerRecord<?, ?> record) {
        String message = String.valueOf(record.value());
        try {
            LOG.info(" 开始从kafka中获取数据, 写入ES.");

            ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
            Map<String, String> fieldDataMap = JSON.parseObject(message, HashMap.class);
            String apiCode = fieldDataMap.get("apiCode") == null ? "" : fieldDataMap.get("apiCode");//接口编码
            String apiName = fieldDataMap.get("apiName") == null ? "" : fieldDataMap.get("apiName");//接口名称
            String componentHost = fieldDataMap.get("componentHost") == null ? "" : fieldDataMap.get("componentHost");//服务IP
            String componentPort = fieldDataMap.get("componentPort") == null ? "" : fieldDataMap.get("componentPort");//服务端口
            String consumerAppName = fieldDataMap.get("consumerAppName") == null ? "" : fieldDataMap.get("consumerAppName");//app系统名称
            String fromIp = fieldDataMap.get("fromIp") == null ? "" : fieldDataMap.get("fromIp");//请求ip
            String logId = fieldDataMap.get("logId") == null ? "" : fieldDataMap.get("logId");//日志ID
            String transId = fieldDataMap.get("transId") == null ? "" : fieldDataMap.get("transId");//账号信息
            String statusCode = fieldDataMap.get("statusCode") == null ? "" : fieldDataMap.get("statusCode");//状态
            String httpMethod = fieldDataMap.get("httpMethod") == null ? "" : fieldDataMap.get("httpMethod");//请求方法
            JSONObject json = JSONObject.parseObject(message);//四种消息列表(REC REQ RESP RET)
            JSONArray messageList = json.getJSONArray("messageList");
            String calltime = "";
            if (messageList.size() > 0) {
                for (Object infoObj : messageList) {
                    JSONObject infoObjList = JSONObject.parseObject(String.valueOf(infoObj));
                    String body = infoObjList.getString("body");
                    Integer time = infoObjList.getInteger("time");
                    String type = infoObjList.getString("type");
                    String url = infoObjList.getString("url");
                    if (Const.API_type_REC.equals(type)) {
                        Params params = new Params();
                        params.setParams(body);
                        apiCallNetFlow.setReqcontent(params);//请求内容
                        apiCallNetFlow.setApiuri(url);//url
                        calltime = TimeUtils.TimestampToDateStr(time);
                        apiCallNetFlow.setCalltime(calltime);//请求时间
                    }
                    if (Const.API_type_RET.equals(type)) {
                        DataRescontent dataRescontent = new DataRescontent();
                        dataRescontent.setData(body);
                        apiCallNetFlow.setRescontent(dataRescontent);//响应内容
                    }
                }
            }
            if (Const.API_STATUSCODE_0000.equals(statusCode)) {
                apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS);//客户端
                apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
            } else {
                apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);//客户端
                apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
            }
            apiCallNetFlow.setApicode(apiCode);//接口编码
            apiCallNetFlow.setLogid(logId);//日志ID
            apiCallNetFlow.setReqmethod(httpMethod);//请求方法
            apiCallNetFlow.setApiip(componentHost);//服务ip
            apiCallNetFlow.setApiport(componentPort);//服务端口
            apiCallNetFlow.setClientip(fromIp);//请求ip
            apiCallNetFlow.setCleantime(TimeUtils.getReqTime());//清洗时间
            apiCallNetFlow.setSystem(Const.API_SYSTEM);
            apiCallNetFlow.setAccount(transId);
            String data = JSON.toJSONString(apiCallNetFlow);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
            Date date = sdf.parse(calltime);
            //推送ES索引日期设置为前一天
            IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(date, 0)).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
            //写入es中
            kafkaMessageConsumer.client.index(request, RequestOptions.DEFAULT);

//            List<String> list = kafkaMessageConsumer.pcapService.selectApicode();
//            IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//            icInterfaceInfo.setApicode(apiCallNetFlow.getApicode());//接口编码
//            icInterfaceInfo.setApiname(apiName);//接口名称
//            icInterfaceInfo.setApp(consumerAppName);//接口名称
//            icInterfaceInfo.setUrl(apiCallNetFlow.getApiuri());//URL
//            icInterfaceInfo.setSparefield2(apiCallNetFlow.getReqmethod());//请求类似 get post
//            icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//            kafkaMessageConsumer.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
        } catch (Exception e) {
            LOG.info("ES清洗出现异常,异常信息为: {}", e.getMessage());
        }
    }


}