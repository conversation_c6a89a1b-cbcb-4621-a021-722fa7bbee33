package com.wzsec.clean.common.utils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> by xiongpf
 * @Description:MD5 16位
 * @date 2018-03-01
 */
public class MD5Util {

    private static final String ALGORITHM_MD5 = "MD5";

    /**
     * 取MD5值中间16位
     *
     * @param paramString
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    public static String encrypt(String paramString)
            throws NoSuchAlgorithmException, UnsupportedEncodingException {
        if (paramString != null) {
            return MD5Util.MD5_32bit(paramString).substring(8, 24);
        } else {
            return null;
        }
    }

    /**
     * 32位MD5算法
     *
     * @param readyEncryptStr
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static final String MD5_32bit(String readyEncryptStr) throws NoSuchAlgorithmException {
        if (readyEncryptStr != null) {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM_MD5);
            md.update(readyEncryptStr.getBytes());
            byte[] b = md.digest();
            StringBuilder su = new StringBuilder();
            for (int offset = 0, bLen = b.length; offset < bLen; offset++) {
                String haxHex = Integer.toHexString(b[offset] & 0xFF);
                if (haxHex.length() < 2) {
                    su.append("0");
                }
                su.append(haxHex);
            }
            return su.toString();
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        try {
            String str = "/sys/api/getuserinfo";
            String encStr = MD5Util.encrypt(str);
            System.out.println(encStr);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

}
