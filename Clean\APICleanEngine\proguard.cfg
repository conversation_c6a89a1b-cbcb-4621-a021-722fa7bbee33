# 指定不警告尚未解决的引用和其他问题
-dontwarn
# JDK目标版本1.8
-target 1.8
# 不做收缩（删除注释、未被引用代码）
-dontshrink
# 不做优化（变更代码实现逻辑）
-dontoptimize
# 混淆时不使用大小写混合，混淆后的类名为小写
-dontusemixedcaseclassnames
# 不去忽略非公共的库类
-dontskipnonpubliclibraryclasses
# 指定不跳过包可见的库类成员（字段和方法）。
# 默认情况下，proguard在解析库类时会跳过包可见的库类成员。当我们确实引用了包可见的类成员时，需要设置此项
-dontskipnonpubliclibraryclassmembers
# 确定统一的混淆类的成员名称来增加混淆
-useuniqueclassmembernames
# 优化时允许访问并修改有修饰符的类和类的成员
-allowaccessmodification
# 不混淆所有包名
#-keeppackagenames
# 需要保持的属性：异常，注解等
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,LocalVariable*Table,*Annotation*,Synthetic,EnclosingMethod

# spring 相关的注解，不要混淆
-keepclassmembers class * {
@org.springframework.** *;
@javax.annotation.PostConstruct *;
@javax.annotation.PreDestroy *;
@org.springframework.beans.factory.annotation.Autowired <fields>;
@org.springframework.beans.factory.annotation.Autowired <methods>;
}

#混淆时是否记录日志
-verbose

# 不混淆所有包含Component等注解的类
-keep @org.springframework.stereotype.Component class * {*;}
-keep @org.springframework.stereotype.Service class * {*;}
-keep @org.springframework.web.bind.annotation.RestController class * {*;}
-keep @org.springframework.context.annotation.Configuration class * {*;}

# 不混淆所有的set/get方法
-keepclassmembers public class * {void set*(***);*** get*();}


# 不混淆本工程中的部分特殊类
#所有类(包括接口)的方法参数不混淆(包括没被keep的) 如果参数混淆了 mybatis mapper 参数绑定会出错(如#{id}）
-keepattributes MethodParameters


#入口程序类不能混淆，混淆会导致springboot启动不了
-keep class com.wzsec.clean.CleanEngineApplication {*;}
#由于spring会根据参数名称绑定参数,如果参数名称被混淆了,参数绑定是会报错,所以不混淆controller的所有的public方法(也可以不混淆类名)
#因为配置了'keepparameternames'所以不混淆方法时参数也不会混淆
-keepclassmembers class com.wzsec.clean.* {public *** *(...);}
#但是因为swagger配置了根据包路径进行扫描,所以如果混淆了会导致swagger扫描不到混淆后的包,所以不混淆controller的包路径
-keeppackagenames com.wzsec.clean.*.controller

#实体类不混淆不然会导致mybatis xml 配置的实体类找不到
#如果是spring jpa 用到@Query 也会导致找不到相关类
#-keep class com.wzsec.clean.filter.clean.** {*;}
-keep class com.wzsec.clean.*.model.** {*;}
-keep class com.wzsec.clean.*.dao.** {*;}
#-keep class com.wzsec.clean.filter.service.impl.** {*;}
#-keep class com.wzsec.clean.filter.service.** {*;}



#保留service的所有公共方法名,由于使用AOP控制事务,根据拦截get,update等方法进行事务控制,所以需要不混淆service下的public方法名
# -keepclassmembers class com.wzsec.modules.*.service.** {public *** *(...);}

#不混淆service包名,由于使用AOP(POINTCUT=serivce包名包名)控制事务,所以需要保留service的包名防止找不到service
# -keeppackagenames com.wzsec.api.*.service.**

#frannework下都是一些配置类比如datasource,aopconfig,swaggerconfig如果混淆会导致各种启动报错,
#比如用@Around(value = "apiLog()")指定apiLog方法对应的@Pointcut作为切入点,但是因为apiLog方法被混淆成a导致找不到对应@Pointcut
#所以全部不混淆 省心点
-keep class com.wzsec.framework.** {*;}

#注解了Aspect的都不混淆,由于把framework下的所有类都不混淆,所以此配置就可有可无了
#-keep @org.aspectj.lang.annotation.Aspect class * {
#    *;
#}

#保留Serializable序列化的类不被混淆
-keepclassmembers class * implements java.io.Serializable{
static final long serialVersionUID;
private static final java.io.ObjectStreamField[]
serialPersistentFields;
private void writeObject(java.io.ObjectOutputStream);
private void readObject(java.io.ObjectInputStream);
java.lang.Object writeReplace();
java.lang.Object readResolve();
}