package com.wzsec.clean.common.analysis;//package com.wzsec.flowdetect.filter.parser;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import cn.ctyun.nfd.filter.clean.PacketParser;
//import cn.ctyun.nfd.filter.clean.PcapClean;
//import cn.ctyun.nfd.filter.model.*;
//import PcapService;
//import ProCommonRule;
//import ConfigurationManager;
//import Const;
//import TimeUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.*;
//import java.math.BigDecimal;
//import java.util.*;
//
///**
// * HTTP、FTP流量检测，清洗后进行检测
// */
//@Component
//@Transactional
//public class PcapParser_v2 {
//
//
//	private static Logger logger = LoggerFactory.getLogger(PcapParser_v2.class);
//
//	private static Map<String, Integer> countMap = new HashMap<>();
//	private static Map<String, HTTPResultDetail> resultMap = new HashMap<>();
//	// 敏感数据检测管理查询(key=敏感数据，value=敏感数据英文名)
//	public static HashMap<String, String> sensitivedataMap = new HashMap<>();
//	//统计每行包含结果类型
//	public static HashSet<String> resultTypeSet = new HashSet<>();
//	// 明文统计次数结果Map
//	public static Map<String, Map<String, Integer>> sensitiveDataCountResultMap = new HashMap<>();
//	//结果类型
//	public static Map<String, Integer> resTypeMap =new HashMap<>();
//	//检测总次数
//	public static HashMap<String, Integer> totalCountByMethodMap = new HashMap<>();
//	//FTP 文件list
//	public static List<String> ftpList = new ArrayList<>();
//	//FTP 结果Map保存文件信息
//	public static Map<String,List<String>> ftpListMap=new HashMap<>();
//	//FTP 结果Map对象信息
//	public static Map<String,FTPResultDetail> ftpEntityMap=new HashMap<>();
//	//结果正常id
//	private static int normalTypeId = 10;
//
//	@Autowired
//	private PcapService pcapService;
//
//
////	@Scheduled(cron = "${pcap.cron}")
//	public void getRes() {
//		try {
//			// 手机号白名单初始化
//			Const.whitePhoneList = pcapService.getFileListInfo(Const.PHONEDETECTION, Const.WHITELIST);
//			// 敏感数据检测管理查询
//			sensitivedataMap = pcapService.selectSensitiveData(Const.INTERFACE_FLOW_SIGN);
//			//结果类型
//			resTypeMap=pcapService.selectResultType();
//
//			//开始时间
//			String startTime = TimeUtils.getNowTime();
//
//			String pcapCleanBeforePath = ConfigurationManager.getProperty("PcapCleanBeforePath").trim();
//			String pcapCleanAfterPath = ConfigurationManager.getProperty("PcapCleanAfterPath").trim();
//			String pcapDate = ConfigurationManager.getProperty("PcapDate").trim();
//			String IntervalDay = ConfigurationManager.getProperty("Interval.day").trim();
//
//			//间隔天数
//			int INTERVAL_DAY = Integer.parseInt(IntervalDay);
//			//pcap检测日期
//			if ("".equals(pcapDate) || pcapDate == null) {
//				//默认检测前一天
//				pcapDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", INTERVAL_DAY);
//			}
//			//清洗前路径
//			String parserBeforeDir = "";
//			if (pcapCleanBeforePath.endsWith(File.separator)) {
//				parserBeforeDir = pcapCleanBeforePath + pcapDate;
//			} else {
//				parserBeforeDir = pcapCleanBeforePath + File.separator + pcapDate;
//			}
//			//清洗后路径
//			String parserAfterDir = "";
//			if (pcapCleanAfterPath.endsWith(File.separator)) {
//				parserAfterDir = pcapCleanAfterPath + pcapDate;
//			} else {
//				parserAfterDir = pcapCleanAfterPath + File.separator + pcapDate;
//			}
//			logger.info("清洗前路径："+parserBeforeDir);
//			logger.info("清洗后路径："+parserAfterDir);
//
//			//清洗前文件路径Map
//			Map<String, String> fileNameMap = new HashMap<>();
//			getAllFileName(parserBeforeDir, fileNameMap);
//
//			//pcap文件清洗
//			PcapClean.pcapClean(fileNameMap,parserAfterDir);
//
//			//检测任务号  61_代表pcap
//			String taskNum = "61" + TimeUtils.DateToStr1(new Date());
//
//			//处理清洗后HTTP流量文件
//			dealDirData(parserAfterDir+File.separator+Const.HTTP_SIGN);
//
//			//处理清洗后FTP流量文件
//			dealDirData(parserAfterDir+File.separator+Const.FTP_SIGN);
//
//			//HTTP流量结果统计保存信息
//			recordHTTPCheckResultInfo(taskNum, startTime);
//
//			//FTP流量结果统计保存信息
//			recordFTPCheckResultInfo(taskNum, startTime);
//
//			//结束时间
//			String endTime = TimeUtils.getNowTime();
//			logger.info("开始时间："+startTime);
//			logger.info("结束时间："+endTime);
//			int time = TimeUtils.getTimeSecondsByBothDate(startTime,endTime);
//			logger.info("任务号"+taskNum+",检测pcap文件共耗时："+time+"秒");
//			//变量销毁
//			destruction();
//		}catch (Exception e){
//			logger.info("检测流量结果统计出现异常：" + e.getMessage());
//			e.printStackTrace();
//		}
//
//
//
//	}
//
//	/**
//	 * FTP流量结果统计保存信息
//	 * @param taskNum
//	 * @param startTime
//	 * <AUTHOR>
//	 * @date 2020-11-02
//	 */
//	private void recordFTPCheckResultInfo(String taskNum, String startTime) {
//		if (ftpListMap.size()>0){
//			Map<String,FTPResultDetail> detailMap = new HashMap<>();
//			Map<String,FTPResult> outlineMap = new HashMap<>();
//				for (String key : ftpListMap.keySet()) {
//					if (ftpListMap.containsKey(key)) {
//						FTPResultDetail ftpEntity = ftpEntityMap.get(key);
//						List<String> list = ftpListMap.get(key);
//						if (list != null) {
//							List<Integer> resultTypeList = checkSensitiveDataByUseRule(key, list);
//
//							if (resultTypeList.size()==0){
//								FTPResultDetail ftpDataNew = new FTPResultDetail();
//								String resKey = key + Const.AUDIT_SPLIT_JOIN + normalTypeId;
//								//根据id查询结果类型
//								Map<String, Integer> resutlMap = pcapService.getResultTypeById(normalTypeId);
//								ftpDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
//								if (!ftpDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
//									ftpDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype())));
//								}
//								ftpDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
//								ftpDataNew.setChecktime(startTime);
//								ftpDataNew.setDesip(ftpEntity.getDesip());
//								ftpDataNew.setDesport(ftpEntity.getDesport());
//								ftpDataNew.setSourceip(ftpEntity.getSourceip());
//								ftpDataNew.setSourceport(ftpEntity.getSourceport());
//								ftpDataNew.setOperationtime(ftpEntity.getOperationtime());
//
//								int checkCount = 1;
//								Integer totalCount = 1;
//								ftpDataNew.setResulttypecount(String.valueOf(checkCount));
//								ftpDataNew.setTotalcount(String.valueOf(totalCount));
//								double rate = 100 * ((double) checkCount / totalCount);
//								BigDecimal bDec = new BigDecimal(String.valueOf(rate));
//								double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//								ftpDataNew.setRatio(String.valueOf(ratio));
//								ftpDataNew.setSign(ftpEntity.getSign());
//								ftpDataNew.setUsername(ftpEntity.getUsername());
//								ftpDataNew.setPassword(ftpEntity.getPassword());
//								ftpDataNew.setFilename(ftpEntity.getFilename());
//								ftpDataNew.setFilepath(ftpEntity.getFilepath());
//								ftpDataNew.setFilesize(ftpEntity.getFilesize());
//								detailMap.put(resKey, ftpDataNew);
//
//
//								//FTP详情统计
//								FTPResult ftpResult = new FTPResult();
//								ftpResult.setTaskname(taskNum);
//								ftpResult.setChecktime(startTime);
//								ftpResult.setProtocol(Const.FTP_SIGN);
//								ftpResult.setSourceip(ftpDataNew.getSourceip());
//								ftpResult.setSourceport(ftpDataNew.getSourceport());
//								ftpResult.setDesip(ftpDataNew.getDesip());
//								ftpResult.setDesport(ftpDataNew.getDesport());
//
//								String[] keys = key.split(Const.AUDIT_SPLIT_JOIN);
//
//								String riskKey="";
//								if (keys.length > 4){
//									riskKey = keys[0] + Const.AUDIT_SPLIT_JOIN + keys[1] + Const.AUDIT_SPLIT_JOIN +
//											keys[2] + Const.AUDIT_SPLIT_JOIN + keys[3] + Const.AUDIT_SPLIT_JOIN +
//											ftpDataNew.getRisk();
//								}
//
//								if (outlineMap.containsKey(riskKey)) {
//									FTPResult result = outlineMap.get(riskKey);
//									result.setRisk(ftpDataNew.getRisk());
//									result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
//									outlineMap.put(riskKey, result);
//								} else {
//									ftpResult.setRisk(ftpDataNew.getRisk());
//									ftpResult.setRiskcount("1");
//									outlineMap.put(riskKey, ftpResult);
//								}
//							}else {
//								for (Integer resyltTypeNum : resultTypeList) {
//									FTPResultDetail ftpDataNew = new FTPResultDetail();
//									String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
//									//根据id查询结果类型
//									Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);
//									ftpDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
//									if (!ftpDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
//										ftpDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype())));
//									}
//									ftpDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
//									ftpDataNew.setChecktime(startTime);
//									ftpDataNew.setDesip(ftpEntity.getDesip());
//									ftpDataNew.setDesport(ftpEntity.getDesport());
//									ftpDataNew.setSourceip(ftpEntity.getSourceip());
//									ftpDataNew.setSourceport(ftpEntity.getSourceport());
//									ftpDataNew.setOperationtime(ftpEntity.getOperationtime());
//
//									Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype());
//									Collection<Integer> values = checkCountMap.values();
//									int checkCount = 0;
//									for (Integer value : values) {
//										checkCount += value;
//									}
//									Integer totalCount = totalCountByMethodMap.get(key);
//									ftpDataNew.setResulttypecount(String.valueOf(checkCount));
//									ftpDataNew.setTotalcount(String.valueOf(totalCount));
//									double rate = 100 * ((double) checkCount / totalCount);
//									BigDecimal bDec = new BigDecimal(String.valueOf(rate));
//									double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//									ftpDataNew.setRatio(String.valueOf(ratio));
//									ftpDataNew.setSign(ftpEntity.getSign());
//									ftpDataNew.setUsername(ftpEntity.getUsername());
//									ftpDataNew.setPassword(ftpEntity.getPassword());
//									ftpDataNew.setFilename(ftpEntity.getFilename());
//									ftpDataNew.setFilepath(ftpEntity.getFilepath());
//									ftpDataNew.setFilesize(ftpEntity.getFilesize());
//									detailMap.put(resKey, ftpDataNew);
//
//
//									//FTP详情统计
//									FTPResult ftpResult = new FTPResult();
//									ftpResult.setTaskname(taskNum);
//									ftpResult.setChecktime(startTime);
//									ftpResult.setProtocol(Const.FTP_SIGN);
//									ftpResult.setSourceip(ftpDataNew.getSourceip());
//									ftpResult.setSourceport(ftpDataNew.getSourceport());
//									ftpResult.setDesip(ftpDataNew.getDesip());
//									ftpResult.setDesport(ftpDataNew.getDesport());
//
//									String[] keys = key.split(Const.AUDIT_SPLIT_JOIN);
//
//									String riskKey="";
//									if (keys.length > 4){
//										riskKey = keys[0] + Const.AUDIT_SPLIT_JOIN + keys[1] + Const.AUDIT_SPLIT_JOIN +
//												keys[2] + Const.AUDIT_SPLIT_JOIN + keys[3] + Const.AUDIT_SPLIT_JOIN +
//												ftpDataNew.getRisk();
//									}
//
//									if (outlineMap.containsKey(riskKey)) {
//										FTPResult result = outlineMap.get(riskKey);
//										result.setRisk(ftpDataNew.getRisk());
//										result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
//										outlineMap.put(riskKey, result);
//									} else {
//										ftpResult.setRisk(ftpDataNew.getRisk());
//										ftpResult.setRiskcount("1");
//										outlineMap.put(riskKey, ftpResult);
//									}
//								}
//							}
//						}
//					}
//				}
//
//			if (detailMap!=null){
//				Collection<FTPResultDetail> pcapDatas = detailMap.values();
//				for (FTPResultDetail ftpData : pcapDatas) {
//					pcapService.saveFtpResultDetail(ftpData);
//				}
//			}
//			logger.info("保存FTP流量检测详情统计完成！");
//			if (outlineMap!=null){
//				Collection<FTPResult> ftpResults = outlineMap.values();
//				for (FTPResult ftpResult : ftpResults) {
//					pcapService.saveFTPResult(ftpResult);
//				}
//			}
//			logger.info("保存FTP流量检测概要统计完成！");
//		}else {
//			logger.info("FTP流量检测统计结果为空！");
//		}
//
//	}
//
//
//
//	/**
//	 * HTTP流量结果统计保存信息
//	 *
//	 * @param taskNum
//	 * @param startTime
//	 * <AUTHOR>
//	 * @date 2020-11-02
//	 */
//	private void recordHTTPCheckResultInfo(String taskNum, String startTime) throws IOException {
//
//		Map<String,HTTPResultDetail> detailMap = new HashMap<>();
//		Map<String,HTTPResult> outlineMap = new HashMap<>();
//
//		//保存pcap文件信息
//		if (resultMap != null && resultMap.size() != 0) {
//
//			Set<String> keySet = resultMap.keySet();
//			for (String key : keySet) {
//				HTTPResultDetail httpResultDetail = resultMap.get(key);
//
//				ArrayList<String> list=null;
//				if (httpResultDetail.getReqcontent()!=null){
//					String [] datas = httpResultDetail.getReqcontent().split(",|:|\"|\\{|}|\\[|]|\r\n");
//					list = convertArrToList(datas);
//				}else {
//					String [] datas = httpResultDetail.getRescontent().split(",|:|\"|\\{|}|\\[|]|\r\n");
//					list = convertArrToList(datas);
//				}
//				if (list!=null){
//
//					//概要统计接口调用总数
//					String totalcount="";
//					String countKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN));
//					if (countMap.containsKey(countKey)) {
//						totalcount = String.valueOf(countMap.get(countKey));
//					}
//
//					//详情统计 统计敏感数据
//					List<Integer> resultTypeList = checkSensitiveDataByUseRule(key,list);
//
//					for (Integer resyltTypeNum : resultTypeList) {
//
//						HTTPResultDetail httpResultDetailNew = new HTTPResultDetail();
//						httpResultDetailNew.setSeqnumber(httpResultDetail.getSeqnumber());
//						httpResultDetailNew.setInterfaceuri(httpResultDetail.getInterfaceuri());
//						httpResultDetailNew.setChecktime(startTime);
//						httpResultDetailNew.setStarttime(httpResultDetail.getStarttime());
//						httpResultDetailNew.setEndtime(httpResultDetail.getEndtime());
//						httpResultDetailNew.setSourceip(httpResultDetail.getSourceip());
//						httpResultDetailNew.setSourceport(httpResultDetail.getSourceport());
//						httpResultDetailNew.setDesip(httpResultDetail.getDesip());
//						httpResultDetailNew.setDesport(httpResultDetail.getDesport());
//						httpResultDetailNew.setRescontent(httpResultDetail.getRescontent());
//						httpResultDetailNew.setReqcontent(httpResultDetail.getReqcontent());
//						httpResultDetailNew.setResstatus(httpResultDetail.getResstatus());
//						httpResultDetailNew.setResstatuscode(httpResultDetail.getResstatuscode());
//						httpResultDetailNew.setReqmethod(httpResultDetail.getReqmethod());
//						httpResultDetailNew.setUserid(httpResultDetail.getUserid());
//						httpResultDetailNew.setUsername(httpResultDetail.getUsername());
//						httpResultDetailNew.setCheckrule(httpResultDetail.getCheckrule());
//
//						String resKey=key+Const.AUDIT_SPLIT_JOIN+resyltTypeNum;
//						//根据id查询结果类型
//						Map<String,Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);
//
//						httpResultDetailNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
//						if (!httpResultDetailNew.getResulttype().equals(Const.NORMAL_SIGN)){
//							httpResultDetailNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key+Const.AUDIT_SPLIT_JOIN+httpResultDetailNew.getResulttype())));
//						}
//						httpResultDetailNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString())!=0?"1":"0");
//
//						Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key+Const.AUDIT_SPLIT_JOIN+httpResultDetailNew.getResulttype());
//						Collection<Integer> values = checkCountMap.values();
//						int checkCount=0;
//						for (Integer value : values) {
//							checkCount+=value;
//						}
//						Integer totalCount = totalCountByMethodMap.get(key);
//						httpResultDetailNew.setResulttypecount(String.valueOf(checkCount));
//						httpResultDetailNew.setChecktotalcount(String.valueOf(totalCount));
//						double rate = 100 * ((double) checkCount / totalCount);
//						BigDecimal bDec = new BigDecimal(String.valueOf(rate));
//						double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//						httpResultDetailNew.setRatio(String.valueOf(ratio));
//						detailMap.put(resKey,httpResultDetailNew);
//
//
//
//						String riskKey = key.substring(0,key.lastIndexOf(Const.AUDIT_SPLIT_JOIN)) + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getRisk();
//						if (outlineMap.containsKey(riskKey)){
//							HTTPResult result = outlineMap.get(riskKey);
//							result.setRisk(httpResultDetailNew.getRisk());
//							result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
//							outlineMap.put(riskKey,result);
//						}else {
//							HTTPResult httpResult = new HTTPResult();
//							httpResult.setTaskname(taskNum);
//							httpResult.setChecktime(startTime);
//							httpResult.setInterfaceuri(httpResultDetail.getInterfaceuri());
//							httpResult.setSourceip(httpResultDetail.getSourceip());
//							httpResult.setSourceport(httpResultDetail.getSourceport());
//							httpResult.setDesip(httpResultDetail.getDesip());
//							httpResult.setDesport(httpResultDetail.getDesport());
//							httpResult.setProtocol(Const.HTTP_SIGN);
//							httpResult.setRisk(httpResultDetailNew.getRisk());
//							httpResult.setRiskcount("1");
//							httpResult.setTotalcount(totalcount);
//							outlineMap.put(riskKey,httpResult);
//						}
//					}
//				}
//			}
//			logger.info("保存接口流量检测概要统计完成！");
//			//保存详情结果
//			if (detailMap!=null){
//				Collection<HTTPResultDetail> httpResultDetails = detailMap.values();
//
//				for (HTTPResultDetail httpResultDetail : httpResultDetails) {
//					pcapService.saveHttpResultDetail(httpResultDetail);
//				}
//				logger.info("保存接口流量检测详情统计完成！");
//			}
//
//			if (outlineMap!=null){
//				Collection<HTTPResult> httpResults = outlineMap.values();
//				for (HTTPResult httpResult : httpResults) {
//					pcapService.saveHttpResult(httpResult);
//				}
//			}
//
//		} else {
//			logger.info("接口流量检测统计结果为空！");
//		}
//	}
//
//	/**
//	 * 解析清洗后的HTTP JSON文件
//	 * <AUTHOR>
//	 * @date 2020-11-05
//	 * @param strLine
//	 */
//	public static void cleanHttpJsonParse(String strLine){
//
//		JSONObject httpObj = JSONObject.parseObject(strLine);
//		JSONObject reqObj = JSONObject.parseObject(httpObj.getString("req"));
//		JSONObject resObj = JSONObject.parseObject(httpObj.getString("res"));
//
//		HTTPResultDetail httpReqResult = JSON.toJavaObject(reqObj,HTTPResultDetail.class);
//		HTTPResultDetail httpResResult = JSON.toJavaObject(resObj,HTTPResultDetail.class);
//
//		httpReqResult.setResstatus(httpResResult.getResstatus());
//		httpReqResult.setResstatuscode(httpResResult.getResstatuscode());
//		httpReqResult.setRescontent(httpResResult.getRescontent());
//		httpReqResult.setEndtime(httpResResult.getEndtime());
//
//		// reqKey = 客户端IP-客户端端口-服务端IP-服务端端口-接口URI
//		String reqKey = httpReqResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpReqResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
//				httpReqResult.getDesip() + Const.AUDIT_SPLIT_JOIN + httpReqResult.getDesport() + Const.AUDIT_SPLIT_JOIN +
//				httpReqResult.getInterfaceuri();
////		String resKey = httpResResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpResResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
////				httpResResult.getDesip() + Const.AUDIT_SPLIT_JOIN + httpResResult.getDesport() + Const.AUDIT_SPLIT_JOIN +
////				httpResResult.getInterfaceuri();
//
//		String resultReqKey = reqKey+Const.AUDIT_SPLIT_JOIN+httpReqResult.getStarttime();
////		String resultResKey = resKey+Const.AUDIT_SPLIT_JOIN+httpResResult.getEndtime();
//
//		String reqtime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpReqResult.getStarttime()));
//		httpReqResult.setStarttime(reqtime);
//		String restime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResResult.getEndtime()));
//		httpResResult.setEndtime(restime);
//		//统计接口调用次数
//		interfaceInfoCount(reqKey);
////		interfaceInfoCount(resKey);
//
//		//保存http结果
//		resultMap.put(resultReqKey,httpReqResult);
////		resultMap.put(resultResKey,httpResResult);
//	}
//
//	/**
//	 * 解析清洗后的FTP JSON文件
//	 * <AUTHOR>
//	 * @date 2020-11-05
//	 * @param strLine
//	 */
//	private static void cleanFTPJsonParse(String strLine) {
//
//		JSONObject ftpObj = JSONObject.parseObject(strLine);
//		FTPResultDetail ftpResult = JSON.toJavaObject(ftpObj, FTPResultDetail.class);
//
//		String key = ftpResult.getSourceip()+Const.AUDIT_SPLIT_JOIN+ftpResult.getSourceport()+Const.AUDIT_SPLIT_JOIN+
//				ftpResult.getDesip()+Const.AUDIT_SPLIT_JOIN+ftpResult.getDesport();
//
//		String ftptime = TimeUtils.TimestampToDateStrNew(Long.parseLong(ftpResult.getOperationtime()));
//		ftpResult.setOperationtime(ftptime);
//		String reskey = "";
//		if (Const.FTP_STOR_SIGN.equals(ftpResult.getSign())){
//			reskey = key+Const.AUDIT_SPLIT_JOIN+Const.FTP_STOR+Const.AUDIT_SPLIT_JOIN+ftptime+Const.AUDIT_SPLIT_JOIN+ftpResult.getFilename();
//		}
//		if (Const.FTP_RETR_SIGN.equals(ftpResult.getSign())){
//			reskey = key+Const.AUDIT_SPLIT_JOIN+Const.FTP_RETR+Const.AUDIT_SPLIT_JOIN+ftptime+Const.AUDIT_SPLIT_JOIN+ftpResult.getFilename();
//		}
//
//		//从还原的文件夹下获取文件
//		String ftpFileDir = ConfigurationManager.getProperty("ftpfileoutput.dir").trim();
//		HashMap<String, String> filenameMap = new HashMap<>();
//		getAllFileName(ftpFileDir,filenameMap);
//		for (String filePath : filenameMap.keySet()) {
////			String fileKey = key + Const.AUDIT_SPLIT_JOIN +ftpResult.getOperationtime() +Const.AUDIT_SPLIT_JOIN+ ftpResult.getFilename();
//			if (filenameMap.get(filePath).equals(ftpResult.getFilename())){
//				dealSingleFileReadLine(filePath,ftpList);
//			}
//		}
//		ftpEntityMap.put(reskey, ftpResult);
//		ftpListMap.put(reskey,ftpList);
//		ftpList=new ArrayList<>();
//	}
//
//
//	/**
//	 *@Description:处理目录下数据
//	 *<AUTHOR> by xiongpf
//	 *@date 2018-01-18
//	 */
//	public static void dealDirData(String strInput) {
//		try {
//			File inDir = new File(strInput);
//			File[] tempList = inDir.listFiles();
//			long totalLines = 0;
//			long startTime = System.currentTimeMillis();
//
//			for(int i = 0; i < tempList.length; i++) {
//				dealSingleFile(tempList[i].toString());
//			}
//
//			long endTime = System.currentTimeMillis();
//			long useTime = endTime - startTime;
//			//System.out.println(useTime);
//			//System.out.println(totalLines);
//		}
//		catch(Exception ex){
//			ex.printStackTrace();
//		}
//	}
//
//
//	/**
//	 *@Description: 读取文件内容清理，存到List
//	 *<AUTHOR>
//	 *@date 2020-11-02
//	 */
//	public static void dealSingleFileReadLine(String strInput,List<String> ftpList) {
//		BufferedReader br = null;
//		try {
//			File f_Source = new File(strInput);
//			br = new BufferedReader(new FileReader(f_Source));
//
//			String sline = null;
//			while((sline=br.readLine()) != null) {
//				String[] dataStr = sline.split(",|:|\"|\\{|}|\\[|]|\t|\r|\n|\r\n");
//				ArrayList<String> list = convertArrToList(dataStr);
//				ftpList.addAll(list);
//			}
//		}
//		catch(Exception ex) {
//			ex.printStackTrace();
//		}
//		finally {
//			try {
//				if (br != null) {
//					br.close();
//				}
//			}
//			catch(Exception ex) {
//				ex.printStackTrace();
//			}
//		}
//	}
//
//
//
//	/**
//	 *@Description:处理单个文件
//	 *<AUTHOR> by xiongpf
//	 *@date 2018-01-18
//	 */
//	public static void dealSingleFile(String strInput) {
//		BufferedReader br = null;
//		try {
//			String f_name = "";
//			File f_Source = new File(strInput);
//			f_name = f_Source.getName();
//			br = new BufferedReader(new FileReader(f_Source));
//
//			long startTime = System.currentTimeMillis();
//
//			//判断HTTP文件还是FTP文件
//			if (strInput.contains(Const.HTTP_SIGN + "_clean")){
//				String sline = null;
//				while((sline=br.readLine()) != null) {
////				System.out.println("处理行："+sline);
//					cleanHttpJsonParse(sline);
//				}
//			}else if (strInput.contains(Const.FTP_SIGN + "_clean")){
//				String sline = null;
//				while((sline=br.readLine()) != null) {
////				System.out.println("处理行："+sline);
//					cleanFTPJsonParse(sline);
//				}
//			}
//
//
//			long endTime = System.currentTimeMillis();
//			long useTime = endTime - startTime;
//			System.out.println("处理文件'"+f_name+"'用时:" + useTime +"ms");
//		}
//		catch(Exception ex) {
//			ex.printStackTrace();
//		}
//		finally {
//			try {
//				if (br != null){
//					br.close();
//				}
//			}
//			catch(Exception ex) {
//				ex.printStackTrace();
//			}
//		}
//	}
//
//
//	/**
//	 * 清洗数组中空字符串
//	 * @param datas
//	 * <AUTHOR>
//	 * @date 2020-11-02
//	 */
//	private static ArrayList<String> convertArrToList(String[] datas) {
//		ArrayList<String> list = new ArrayList<>();
//		for (String data : datas) {
//			if (!data.equals("")){
//				list.add(data);
//			}
//		}
//		return list;
//	}
//
//
//	/**
//	 * 获取一个文件夹下的所有文件全路径和文件名
//	 *
//	 * @param path
//	 * @param fileNameMap
//	 * <AUTHOR> by wangqi
//	 * @date 2020-11-02
//	 */
//	public static void getAllFileName(String path, Map<String, String> fileNameMap) {
//		File file = new File(path);
//		File[] files = null;
//		if (file.isDirectory()) {
//			files = file.listFiles();
//		}
//		if (files != null) {
//			for (File f : files) {
//				if (f.isFile()) {
////					if (f.getAbsolutePath().endsWith("pcap")) {
//						fileNameMap.put(f.getAbsolutePath(), f.getName());
////					}
//				}
//			}
//		}
//	}
//
//
//	/**
//	 * 统计接口检测总数
//	 * @param key
//	 * <AUTHOR> by wangqi
//	 * @date 2020-11-02
//	 */
//	private static void interfaceInfoCount(String key) {
//		if (countMap.containsKey(key)) {
//			countMap.put(key, countMap.get(key) + 1);
//		} else {
//			countMap.put(key, 1);
//		}
//	}
//
//
//
//	/*
//	 *@Decription 变量销毁
//	 *<AUTHOR>
//	 *@date 2020/7/10
//	 */
//	public static void destruction() {
//		countMap = new HashMap<>();
//		resultMap = new HashMap<>();
//		sensitivedataMap = new HashMap<>();
//		resultTypeSet = new HashSet<>();
//		sensitiveDataCountResultMap = new HashMap<>();
//		resTypeMap = new HashMap<>();
//		totalCountByMethodMap = new HashMap<>();
//		ftpList = new ArrayList<>();
//		ftpListMap=new HashMap<>();
//		ftpEntityMap=new HashMap<>();
//		PacketParser.dataMap=new HashMap<>();
//		PacketParser.ftpUserMap=new HashMap<>();
//		PacketParser.ftpPortMap=new HashMap<>();
//		PacketParser.ftpDataTransferMap=new HashMap<>();
//		PacketParser.ftpTransferMap=new HashMap<>();
//		PacketParser.ftpResultMap=new HashMap<>();
//	}
//
//
//	/**
//	 * @Description:检测参数数据通过通用检测规则
//	 * <AUTHOR> by wangqi
//	 * @date 2020-09-03
//	 */
//	private List<Integer> checkSensitiveDataByUseRule(String strKey, List<String> list) {
//		// 校验参数是否包含敏感数据
//		resultTypeSet = new HashSet<>();
//		List<Integer> resultTypeList = ProCommonRule.checkIsClearType(sensitivedataMap, sensitiveDataCountResultMap,
//				strKey, list.toArray(new String[list.size()]), resultTypeSet,resTypeMap);
//
//		// 统计检测次数
//		if (list.size() > 0) {
//			if (null == totalCountByMethodMap) {
//				totalCountByMethodMap = new HashMap<>();
//				totalCountByMethodMap.put(strKey, list.size());
//			} else {
//				if (totalCountByMethodMap.containsKey(strKey)) {
//					totalCountByMethodMap.put(strKey, totalCountByMethodMap.get(strKey).intValue() + list.size());
//				} else {
//					totalCountByMethodMap.put(strKey, list.size());
//				}
//			}
//		}
//
//		return resultTypeList;
//	}
//
//	/**
//	 * @Description：按照行为时间进行排序，方式采集时时间顺序错误导致审计出错
//	 * <AUTHOR>
//	 * @date 2019年12月9日 下午3:13:02
//	 * @param map:需要根据value排序的map
//	 */
//	private static String mapSort(Map<String, Integer> map) {
//		if (map == null || map.size() == 0) {
//			return null;
//		}
//		ArrayList<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(map.entrySet());
//		Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
//			@Override
//			// 定义一个比较器
//			public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
//				Integer num1 = o1.getValue();
//				Integer num2 = o2.getValue();
//				return num2.compareTo(num1);
//			}
//		});
//		JSONObject jsonObject = new JSONObject();
//		for (Map.Entry<String, Integer> l : list) {
//			jsonObject.put(l.getKey(), l.getValue());
//		}
//		return jsonObject.toString();
//	}
//}
