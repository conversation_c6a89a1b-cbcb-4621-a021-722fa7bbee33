package com.wzsec.clean.filter.clean.ZhiWangKeJi;

import com.wzsec.clean.modules.model.PcapFlowCombination;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 智网科技流量清洗抽取
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
public class ZhiWangKeJiFlowClean {


    /**
     * 截取客户端及服务端IP(智网科技)
     *
     * @param pcapFlowCombination pcap流量组合
     */
    public static void interceptClientServeIPByZhiWangKeJi(PcapFlowCombination pcapFlowCombination) {
        String requestData = pcapFlowCombination.getRequest_data().toLowerCase();
        pcapFlowCombination.setServer_ip(extractHeaderValue(requestData, "x-forwarded-for").trim()); //客户端IP
        pcapFlowCombination.setServer_port(""); //TODO 头部信息中无客户端端口

        // 服务端IP及端口
        String[] client = extractHeaderValue(requestData, "x-forwarded-host").trim().split(":");
        if (client.length > 1) {
            pcapFlowCombination.setClient_ip(client[0]); //服务端IP
            pcapFlowCombination.setClient_port(client[1]); //服务端端口
        } else {
            pcapFlowCombination.setServer_ip(extractHeaderValue(requestData, "x-forwarded-host").trim()); //服务端IP
        }
    }


    /**
     * 提取标头值
     *
     * @param header     头球
     * @param headerName 标头名称
     * @return {@link String}
     */
    private static String extractHeaderValue(String header, String headerName) {
        String pattern = headerName + ": (.+)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(header);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }

}
