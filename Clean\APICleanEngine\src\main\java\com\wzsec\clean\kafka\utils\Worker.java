package com.wzsec.clean.kafka.utils;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.DateUtils;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;

import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class Worker implements Runnable {

    private ConsumerRecord<String, String> consumerRecord;

    private RestHighLevelClient restHighLevelClient;

    private String scene = ConfigurationManager.getProperty("KafkaMessageConsume.scene").trim();

    public Worker(ConsumerRecord record, RestHighLevelClient client) {
        this.consumerRecord = record;
        this.restHighLevelClient = client;
    }

    @Override
    public void run() {
        //TODO  消息处理逻辑 目前topic存储48小时数据
        String message = String.valueOf(consumerRecord.value());
        try {
            if (Const.KAKFA_SCENE_ZERO.equals(scene)){
                intelligentNetworkVersion(message); //智网
            } else if (Const.KAKFA_SCENE_ONE.equals(scene)){
                softResearchInstituteVersion(message);//软研院
            } else if (Const.KAKFA_SCENE_TWO.equals(scene)) { // TODO 中移在线
                chinaMobileOnlineVersion(message);
            }
        } catch (Exception e) {
            log.info("ES清洗出现异常,异常信息为: {}", e.getMessage());
        }
    }

    /**
     * 软研院消费kafka写入es
     * @param message
     * @throws IOException
     */
    private void softResearchInstituteVersion(String message) throws IOException {
        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
        Map<String, String> fieldDataMap = JSON.parseObject(message, HashMap.class);
        String apiCode = fieldDataMap.get("TRANS_IDC") == null ? "" : fieldDataMap.get("TRANS_IDC");//接口编码
        String apiName = fieldDataMap.get("SVC_CH_NAME") == null ? "" : fieldDataMap.get("SVC_CH_NAME");//接口名称
        String fromIp = fieldDataMap.get("OPARTY_IP") == null ? "" : fieldDataMap.get("OPARTY_IP");//请求ip
        String logId = fieldDataMap.get("COLONY_CODE") == null ? "" : fieldDataMap.get("COLONY_CODE");//日志ID
        String httpMethod = fieldDataMap.get("SVC_NAME") == null ? "" : fieldDataMap.get("SVC_NAME");//请求方法
        String requestMessage = fieldDataMap.get("REQ_CONTENT") == null ? "" : fieldDataMap.get("REQ_CONTENT");//请求报文
        String responseMessage = fieldDataMap.get("HRSP_CONTENT") == null ? "" : fieldDataMap.get("HRSP_CONTENT");//响应报文
        String calltime = fieldDataMap.get("OSN_REQ_RECV_TIME") == null ? "" : fieldDataMap.get("OSN_REQ_RECV_TIME");//请求时间


        apiCallNetFlow.setApicode(apiCode);//接口编码
        apiCallNetFlow.setLogid(logId);//日志ID
        apiCallNetFlow.setReqmethod(httpMethod);//请求方法
        apiCallNetFlow.setApiip("");//TODO 服务ip
        apiCallNetFlow.setApiport("");//TODO 服务端口
        apiCallNetFlow.setApiuri("");//TODO url
        apiCallNetFlow.setRepstatus("");//TODO 客户端状态
        apiCallNetFlow.setResstatus("");//TODO 服务端状态
        apiCallNetFlow.setClientip(fromIp);//请求ip
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime());//清洗时间
        apiCallNetFlow.setSystem("");//TODO 系统名
        apiCallNetFlow.setAccount(""); // TODO 应用
        apiCallNetFlow.setClientmac("");
        DataRescontent dataRescontent = new DataRescontent();
        dataRescontent.setData(responseMessage);
        apiCallNetFlow.setRescontent(dataRescontent);//响应内容
        Params params = new Params();
        params.setParams(requestMessage);
        apiCallNetFlow.setReqcontent(params);//请求内容
        apiCallNetFlow.setCalltime(calltime);//请求时间
        String data = JSON.toJSONString(apiCallNetFlow);
        //推送ES索引日期设置为前一天
        IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1)).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
        //写入es中
        restHighLevelClient.index(request, RequestOptions.DEFAULT);

//            List<String> list = kafkaMessageConsumer.pcapService.selectApicode();
//            IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//            icInterfaceInfo.setApicode(apiCallNetFlow.getApicode());//接口编码
//            icInterfaceInfo.setApiname(apiName);//接口名称
//            icInterfaceInfo.setApp(consumerAppName);//接口名称
//            icInterfaceInfo.setUrl(apiCallNetFlow.getApiuri());//URL
//            icInterfaceInfo.setSparefield2(apiCallNetFlow.getReqmethod());//请求类似 get post
//            icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//            kafkaMessageConsumer.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
    }

    /**
     * 智网消费kafka写入es
     * @param message
     * @throws IOException
     */
    private void intelligentNetworkVersion(String message) throws IOException {
        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
        Map<String, String> fieldDataMap = JSON.parseObject(message, HashMap.class);
        String apiCode = fieldDataMap.get("apiCode") == null ? "" : fieldDataMap.get("apiCode");//接口编码
        String apiName = fieldDataMap.get("apiName") == null ? "" : fieldDataMap.get("apiName");//接口名称
        String componentHost = fieldDataMap.get("componentHost") == null ? "" : fieldDataMap.get("componentHost");//服务IP
        String componentPort = fieldDataMap.get("componentPort") == null ? "" : fieldDataMap.get("componentPort");//服务端口
        String consumerAppName = fieldDataMap.get("consumerAppName") == null ? "" : fieldDataMap.get("consumerAppName");//app系统名称
        String fromIp = fieldDataMap.get("fromIp") == null ? "" : fieldDataMap.get("fromIp");//请求ip
        String logId = fieldDataMap.get("logId") == null ? "" : fieldDataMap.get("logId");//日志ID
        String transId = fieldDataMap.get("transId") == null ? "" : fieldDataMap.get("transId");//账号信息
        String statusCode = fieldDataMap.get("statusCode") == null ? "" : fieldDataMap.get("statusCode");//状态
        String httpMethod = fieldDataMap.get("httpMethod") == null ? "" : fieldDataMap.get("httpMethod");//请求方法
        JSONObject json = JSONObject.parseObject(message);//四种消息列表(REC REQ RESP RET)
        JSONArray messageList = json.getJSONArray("messageList");
        String calltime = "";
        if (messageList.size() > 0) {
            for (Object infoObj : messageList) {
                JSONObject infoObjList = JSONObject.parseObject(String.valueOf(infoObj));
                String body = infoObjList.getString("body");
                String time = infoObjList.getString("time");
                String type = infoObjList.getString("type");
                String url = infoObjList.getString("url");
                if (Const.API_type_REC.equals(type)) {
                    Params params = new Params();
                    params.setParams(body);
                    apiCallNetFlow.setReqcontent(params);//请求内容
                    apiCallNetFlow.setApiuri(new URL(url).getPath());//url
                    calltime = TimeUtils.TimestampToDateStr(Long.parseLong(time));
                    apiCallNetFlow.setCalltime(calltime);//请求时间
                }
                if (Const.API_type_RET.equals(type)) {
                    DataRescontent dataRescontent = new DataRescontent();
                    dataRescontent.setData(body);
                    apiCallNetFlow.setRescontent(dataRescontent);//响应内容
                }
            }
        }
        if (Const.API_STATUSCODE_0000.equals(statusCode)) {
            apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS);//客户端
            apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
        } else {
            apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);//客户端
            apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
        }
        apiCallNetFlow.setApicode(apiCode);//接口编码
        apiCallNetFlow.setLogid(logId);//日志ID
        apiCallNetFlow.setReqmethod(httpMethod);//请求方法
        apiCallNetFlow.setApiip(componentHost);//服务ip
        apiCallNetFlow.setApiport(componentPort);//服务端口
        apiCallNetFlow.setClientip(fromIp);//请求ip
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime());//清洗时间
        apiCallNetFlow.setSystem(Const.API_SYSTEM);
        apiCallNetFlow.setAccount(transId);
        apiCallNetFlow.setClientmac("");
        String data = JSON.toJSONString(apiCallNetFlow);
        //推送ES索引日期设置为前一天
        IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1)).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
        //写入es中
        restHighLevelClient.index(request, RequestOptions.DEFAULT);

//            List<String> list = kafkaMessageConsumer.pcapService.selectApicode();
//            IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//            icInterfaceInfo.setApicode(apiCallNetFlow.getApicode());//接口编码
//            icInterfaceInfo.setApiname(apiName);//接口名称
//            icInterfaceInfo.setApp(consumerAppName);//接口名称
//            icInterfaceInfo.setUrl(apiCallNetFlow.getApiuri());//URL
//            icInterfaceInfo.setSparefield2(apiCallNetFlow.getReqmethod());//请求类似 get post
//            icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//            kafkaMessageConsumer.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
    }


    /**
     * 中移在线消费kafka写入es
     *
     * @param message 消息
     * @throws IOException IOException
     */
    private void chinaMobileOnlineVersion(String message) {
        try {
            //推送ES索引日期设置为前一天
            IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1))
                    .id(UUID.randomUUID().toString())
                    .source(message, XContentType.JSON);
            //写入es中
            restHighLevelClient.index(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * filebeat采集消费kafka写入es
     * @param message
     * @throws IOException
     */
    private void consumingKafkaWritesToEs(String message) throws IOException {
        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
        Map<String, String> fieldDataMap = JSON.parseObject(message, HashMap.class);
        String apiCode = fieldDataMap.get("apiCode") == null ? "" : fieldDataMap.get("apiCode");//接口编码
        String apiName = fieldDataMap.get("apiName") == null ? "" : fieldDataMap.get("apiName");//接口名称
        String componentHost = fieldDataMap.get("componentHost") == null ? "" : fieldDataMap.get("componentHost");//服务IP
        String componentPort = fieldDataMap.get("componentPort") == null ? "" : fieldDataMap.get("componentPort");//服务端口
        String consumerAppName = fieldDataMap.get("consumerAppName") == null ? "" : fieldDataMap.get("consumerAppName");//app系统名称
        String fromIp = fieldDataMap.get("fromIp") == null ? "" : fieldDataMap.get("fromIp");//请求ip
        String logId = fieldDataMap.get("logId") == null ? "" : fieldDataMap.get("logId");//日志ID
        String transId = fieldDataMap.get("transId") == null ? "" : fieldDataMap.get("transId");//账号信息
        String statusCode = fieldDataMap.get("statusCode") == null ? "" : fieldDataMap.get("statusCode");//状态
        String httpMethod = fieldDataMap.get("httpMethod") == null ? "" : fieldDataMap.get("httpMethod");//请求方法
        JSONObject json = JSONObject.parseObject(message);//四种消息列表(REC REQ RESP RET)
        JSONArray messageList = json.getJSONArray("messageList");
        String calltime = "";
        if (messageList.size() > 0) {
            for (Object infoObj : messageList) {
                JSONObject infoObjList = JSONObject.parseObject(String.valueOf(infoObj));
                String body = infoObjList.getString("body");
                String time = infoObjList.getString("time");
                String type = infoObjList.getString("type");
                String url = infoObjList.getString("url");
                if (Const.API_type_REC.equals(type)) {
                    Params params = new Params();
                    params.setParams(body);
                    apiCallNetFlow.setReqcontent(params);//请求内容
                    apiCallNetFlow.setApiuri(new URL(url).getPath());//url
                    calltime = TimeUtils.TimestampToDateStr(Long.parseLong(time));
                    apiCallNetFlow.setCalltime(calltime);//请求时间
                }
                if (Const.API_type_RET.equals(type)) {
                    DataRescontent dataRescontent = new DataRescontent();
                    dataRescontent.setData(body);
                    apiCallNetFlow.setRescontent(dataRescontent);//响应内容
                }
            }
        }
        if (Const.API_STATUSCODE_0000.equals(statusCode)) {
            apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS);//客户端
            apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
        } else {
            apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);//客户端
            apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS);//服务端
        }
        apiCallNetFlow.setApicode(apiCode);//接口编码
        apiCallNetFlow.setLogid(logId);//日志ID
        apiCallNetFlow.setReqmethod(httpMethod);//请求方法
        apiCallNetFlow.setApiip(componentHost);//服务ip
        apiCallNetFlow.setApiport(componentPort);//服务端口
        apiCallNetFlow.setClientip(fromIp);//请求ip
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime());//清洗时间
        apiCallNetFlow.setSystem(Const.API_SYSTEM);
        apiCallNetFlow.setAccount(transId);
        apiCallNetFlow.setClientmac("");
        String data = JSON.toJSONString(apiCallNetFlow);
        //推送ES索引日期设置为前一天
        IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1)).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
        //写入es中
        restHighLevelClient.index(request, RequestOptions.DEFAULT);

//            List<String> list = kafkaMessageConsumer.pcapService.selectApicode();
//            IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//            icInterfaceInfo.setApicode(apiCallNetFlow.getApicode());//接口编码
//            icInterfaceInfo.setApiname(apiName);//接口名称
//            icInterfaceInfo.setApp(consumerAppName);//接口名称
//            icInterfaceInfo.setUrl(apiCallNetFlow.getApiuri());//URL
//            icInterfaceInfo.setSparefield2(apiCallNetFlow.getReqmethod());//请求类似 get post
//            icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//            kafkaMessageConsumer.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
    }
}
