package com.wzsec.clean.filter.clean.networksession;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description 网络会话 实体类
 * <AUTHOR>
 * @date 2025/3/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NetworkSessionAudit {
    private String requestIp;     // 客户端 IP
    private int requestPort;      // 客户端端口
    private String responseIp;    // 服务器 IP
    private int responsePort;     // 服务器端口
    private String calltime;      // 请求时间
    private String cleantime;      // 清洗时间
    private long requestBodySize;  // 客户端到服务器 (bytes)
    private long responseBodySize; // 服务器到客户端 (bytes)
    private double sessionDurationSec; // 毫秒
    private String protocolVersion; //协议
    private String url; //url
}
