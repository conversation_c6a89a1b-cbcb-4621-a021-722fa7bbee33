package com.wzsec.clean.filter.clean.chinaMobileOnline;

import cn.hutool.core.date.DateUtil;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.DatabaseConfig;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.ApiDiscoverySecurityPlatform;
import com.wzsec.clean.modules.model.IcInterfaceInfo;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 中国移动在线数据库交互(防止与原有逻辑冲突)
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public class ChinaMobileOnlineDatabaseInteraction {

    private static final DataSource dataSource = DatabaseConfig.createDataSource();

    /**
     * 获取接口规范信息(接口编码,接口名称,能力模块ID,能力模块名称,CSF编码,组织机构ID,组织机构名称)
     *
     * @return {@link Map}<{@link String}, {@link IcInterfaceInfo}>
     */
    public static Map<String, IcInterfaceInfo> queryInterfaceSpecificationInfo() {
        String sql = "SELECT apicode, apiname, app, data_source, register_unit_mozi_org_id, area, register_unit FROM sdd_ic_interfaceinfo";
        Map<String, IcInterfaceInfo> icInterfaceInfoMap = new HashMap<>();
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
                icInterfaceInfo.setApicode(resultSet.getString("apicode")); //接口编码
                icInterfaceInfo.setApiname(resultSet.getString("apiname")); //接口名称
                icInterfaceInfo.setApp(resultSet.getString("app")); //能力模块ID
                icInterfaceInfo.setData_source(resultSet.getString("data_source")); //能力模块名称
                icInterfaceInfo.setRegister_unit_mozi_org_id(resultSet.getString("register_unit_mozi_org_id"));//CSF编码
                icInterfaceInfo.setArea(resultSet.getString("area"));//组织机构ID
                icInterfaceInfo.setRegister_unit(resultSet.getString("register_unit"));//组织机构名称
                icInterfaceInfoMap.put(icInterfaceInfo.getApicode(), icInterfaceInfo);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return icInterfaceInfoMap;
    }


    /**
     * 获取接口发现接口编码
     *
     * @return {@link List }<{@link String }>
     */
    public static List<String> queryIcDiscoverApiCode() {
        String sql = "SELECT DISTINCT apicode FROM sdd_ic_apidiscovery";
        List<String> apiCodes = new ArrayList<>();
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet resultSet = statement.executeQuery()) {
            // 处理结果集
            while (resultSet.next()) {
                apiCodes.add(resultSet.getString("apicode"));
            }
        } catch (SQLException e) {
            // 打印错误堆栈信息
            e.printStackTrace();
        }
        return apiCodes;
    }

    /**
     * 插入接口发现
     *
     * @param apiCallNetFlow         转换数据
     * @param interfaceDiscoveryList 接口发现列表
     */
    public static void interfaceDiscovery(ApiCallNetFlow apiCallNetFlow,
                                          List<String> interfaceDiscoveryList) {
        // TODO 新增接口写入接口发现表
        if (!interfaceDiscoveryList.contains(apiCallNetFlow.getApicode())) {
            ApiDiscoverySecurityPlatform apiDiscoverySecurityPlatform = new ApiDiscoverySecurityPlatform();
            apiDiscoverySecurityPlatform.setApicode(apiCallNetFlow.getApicode());
            apiDiscoverySecurityPlatform.setApiname(apiCallNetFlow.getApiname());
            apiDiscoverySecurityPlatform.setApiip("");
            apiDiscoverySecurityPlatform.setApiport("");
            apiDiscoverySecurityPlatform.setUrl("");
            apiDiscoverySecurityPlatform.setReq_example(apiCallNetFlow.getReqcontent().getParams());
            apiDiscoverySecurityPlatform.setRes_example(apiCallNetFlow.getRescontent().getData());
            apiDiscoverySecurityPlatform.setRequestmode("");
            apiDiscoverySecurityPlatform.setApistatus(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);
            apiDiscoverySecurityPlatform.setInserttime(DateUtil.now());
            apiDiscoverySecurityPlatform.setSparefield1(apiCallNetFlow.getAbilitymoduleid()); // 能力模块ID
            apiDiscoverySecurityPlatform.setSparefield2(apiCallNetFlow.getAbilitymodulename()); // 能力模块名称
            apiDiscoverySecurityPlatform.setSparefield3(apiCallNetFlow.getClientip());  //客户ID
            apiDiscoverySecurityPlatform.setSparefield4("");  //客户端名称
            interfaceDiscoveryList.add(apiCallNetFlow.getApicode());
            ChinaMobileOnlineDatabaseInteraction.saveApiDiscoveryInfoByChinaMobileOnline(apiDiscoverySecurityPlatform);
        }
    }

    /**
     * [中移在线]新增接口写入发现信息表
     *
     * @param apiDiscovery api发现
     */
    public static void saveApiDiscoveryInfoByChinaMobileOnline(ApiDiscoverySecurityPlatform apiDiscovery) {
        String sql = "INSERT INTO sdd_ic_apidiscovery (" +
                "apicode, apiname, apiip, apiport, url, req_example, res_example, " +
                "requestmode, apistatus, inserttime, updateuser, updatetime, " +
                "sparefield1, sparefield2, sparefield3, sparefield4) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {
            // 设置各字段值
            statement.setString(1, apiDiscovery.getApicode());
            statement.setString(2, apiDiscovery.getApiname());
            statement.setString(3, apiDiscovery.getApiip());
            statement.setString(4, apiDiscovery.getApiport());
            statement.setString(5, apiDiscovery.getUrl());
            statement.setString(6, apiDiscovery.getReq_example());
            statement.setString(7, apiDiscovery.getRes_example());
            statement.setString(8, apiDiscovery.getRequestmode());
            statement.setString(9, apiDiscovery.getApistatus());
            statement.setString(10, apiDiscovery.getInserttime());
            statement.setString(11, apiDiscovery.getUpdateuser());
            statement.setString(12, apiDiscovery.getUpdatetime());
            statement.setString(13, apiDiscovery.getSparefield1());
            statement.setString(14, apiDiscovery.getSparefield2());
            statement.setString(15, apiDiscovery.getSparefield3());
            statement.setString(16, apiDiscovery.getSparefield4());
            // 执行插入操作
            statement.executeUpdate();
        } catch (SQLException e) {
            // 打印插入失败的对象信息和错误堆栈
            System.err.println("插入API发现表异常: " + apiDiscovery);
            e.printStackTrace();
        }
    }
}
