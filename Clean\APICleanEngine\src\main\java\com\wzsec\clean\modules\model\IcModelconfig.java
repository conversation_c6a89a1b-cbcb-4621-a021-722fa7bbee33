package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
* <AUTHOR>
* @date 2022-05-13
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IcModelconfig {

    /** id */
    private Integer id;

    /** 接口编码 */
    private String apicode;

    /** 预期方法 */
    private String forecastmethod;

    /** 预期字段名 */
    private String forecastfieldname;

    /** 预期字段值 */
    private String forecastfieldvalues;

    /** 单次最大输出量 */
    private String maximumoutput;

    /** 接口请求开始使用时间 */
    private String begindate;

    /** 接口请求结束使用时间 */
    private String enddate;

    /** 建议开始使用时间 */
    private String suggestbegindate;

    /** 建议结束使用时间 */
    private String suggestenddate;

    /** 状态 */
    private String status;

    /** 创建人 */
    private String createuser;

    /** 创建时间 */
    private String createtime;

    /** 更新人 */
    private String updateuser;

    /** 更新时间 */
    private String updatetime;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;

    /** 备用字段6 */
    private String sparefield6;

    /** 备用字段7 */
    private String sparefield7;

    /** 备用字段8 */
    private String sparefield8;

}