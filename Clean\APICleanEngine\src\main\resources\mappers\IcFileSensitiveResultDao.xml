<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wzsec.clean.modules.dao.IcFileSensitiveResultDao">

    <insert id="saveResult" parameterType="com.wzsec.clean.modules.model.IcFileSensitiveResult">
        insert into t_api_filesensitiveresult(id, apicode, apiname, url, clientip, serverip, serverport,
                                              filename, filesize, behaviortype, risk, checktime,taskname,
                                              sparefield1, sparefield2, sparefield3, sparefield4, sparefield5)
        values (#{id}, #{apicode}, #{apiname}, #{url}, #{clientip}, #{serverip}, #{serverport},
                #{filename}, #{filesize}, #{behaviortype}, #{risk}, #{checktime}, #{taskname}, #{sparefield1}, #{sparefield2},
                #{sparefield3}, #{sparefield4}, #{sparefield5})
    </insert>

</mapper>
