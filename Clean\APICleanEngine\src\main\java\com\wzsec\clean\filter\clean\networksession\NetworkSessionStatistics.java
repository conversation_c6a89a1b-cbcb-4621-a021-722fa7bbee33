package com.wzsec.clean.filter.clean.networksession;
import com.wzsec.clean.common.utils.TimeUtils;
import org.pcap4j.core.*;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.TcpPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 网络会话统计
 * @date 2025/3/11
 */
public class NetworkSessionStatistics {

    private static final Logger logger = LoggerFactory.getLogger(NetworkSessionStatistics.class);

    // 用于跟踪会话
    private static class Session {
        long startTime;
        long endTime;
        long clientToServerSize; // 客户端到服务器数据大小
        long serverToClientSize; // 服务器到客户端数据大小
        String protocolVersion;  // 协议版本
        boolean isTlsDetected;   // 是否检测到 TLS
        String clientIpPort;     // 客户端 IP:端口
        String serverIpPort;     // 服务器 IP:端口
        long lastClientSeq;      // 最近客户端序列号
        long lastServerSeq;      // 最近服务器序列号
        boolean directionSet;    // 是否已确定方向

        Session(long startTime, String clientIpPort, String serverIpPort) {
            this.startTime = startTime;
            this.endTime = startTime;
            this.clientToServerSize = 0;
            this.serverToClientSize = 0;
            this.protocolVersion = "";
            this.isTlsDetected = false;
            this.clientIpPort = clientIpPort;
            this.serverIpPort = serverIpPort;
            this.lastClientSeq = -1;
            this.lastServerSeq = -1;
            this.directionSet = false;
        }

        void updateDirection(String srcIpPort, String dstIpPort, long seqNum, boolean isSyn, boolean isAck, long payloadSize) {
            if (!directionSet) {
                if (isSyn && !isAck) {
                    this.clientIpPort = srcIpPort;
                    this.serverIpPort = dstIpPort;
                    this.directionSet = true;
                } else if (isSyn && isAck) {
                    this.clientIpPort = dstIpPort;
                    this.serverIpPort = srcIpPort;
                    this.directionSet = true;
                } else if (payloadSize > 0) {
                    this.clientIpPort = srcIpPort;
                    this.serverIpPort = dstIpPort;
                    this.lastClientSeq = seqNum;
                    this.directionSet = true;
                }
            }
        }
    }


    /**
     * 解析 pcap 文件并返回会话统计列表
     *
     * @param pcapFilePath pcap 文件路径
     * @return List<NetworkSessionAudit> 会话统计列表
     * @throws IllegalArgumentException 如果文件路径为空或文件无法打开
     */
    public static List<NetworkSessionAudit> parsePcap(String pcapFilePath) {
        if (pcapFilePath == null || pcapFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("pcapFilePath cannot be null or empty");
        }

        List<NetworkSessionAudit> networkSessionAuditList = new ArrayList<>();
        Map<String, Session> sessionMap = new HashMap<>();

        try (PcapHandle handle = Pcaps.openOffline(pcapFilePath)) {
            Packet packet;
            while ((packet = handle.getNextPacket()) != null) {
                TcpPacket tcpPacket = packet.get(TcpPacket.class);
                if (tcpPacket == null) continue;

                IpV4Packet ipPacket = packet.get(IpV4Packet.class);
                if (ipPacket == null) continue;

                // 获取 IP 和端口
                String srcIp = ipPacket.getHeader().getSrcAddr().getHostAddress();
                int srcPort = tcpPacket.getHeader().getSrcPort().value() & 0xFFFF; // 修正负端口
                String dstIp = ipPacket.getHeader().getDstAddr().getHostAddress();
                int dstPort = tcpPacket.getHeader().getDstPort().value() & 0xFFFF; // 修正负端口
                String srcIpPort = srcIp + ":" + srcPort;
                String dstIpPort = dstIp + ":" + dstPort;

                // 生成会话 ID（双向一致）
                String sessionId = generateSessionId(srcIpPort, dstIpPort);

                // 初始化或获取会话
                Session session = sessionMap.computeIfAbsent(sessionId, k ->
                        new Session(handle.getTimestamp().getTime(), srcIpPort, dstIpPort)
                );

                // 更新会话时间
                session.endTime = handle.getTimestamp().getTime();

                // 获取 TCP 头信息
                TcpPacket.TcpHeader tcpHeader = tcpPacket.getHeader();
                long seqNum = tcpHeader.getSequenceNumber();
                long ackNum = tcpHeader.getAcknowledgmentNumber();
                long payloadSize = tcpPacket.getPayload() != null ? tcpPacket.getPayload().getRawData().length : 0;
                boolean isSyn = tcpHeader.getSyn();
                boolean isAck = tcpHeader.getAck();

                // 动态确定方向
                session.updateDirection(srcIpPort, dstIpPort, seqNum, isSyn, isAck, payloadSize);

                // 累加数据大小
                if (session.directionSet) {
                    if (srcIpPort.equals(session.clientIpPort) && (seqNum >= session.lastClientSeq || payloadSize > 0)) {
                        session.clientToServerSize += payloadSize;
                        session.lastClientSeq = seqNum + payloadSize; // 更新到序列号末尾
                    } else if (srcIpPort.equals(session.serverIpPort) && (ackNum > session.lastServerSeq || payloadSize > 0)) {
                        session.serverToClientSize += payloadSize;
                        session.lastServerSeq = ackNum; // 更新确认号
                    }
                }

                // 检测协议版本和 URL
                detectProtocolAndUrl(tcpPacket, session);

                // 结束会话（仅在最后一次 FIN/RST 汇总）
                if (tcpPacket.getHeader().getFin() || tcpPacket.getHeader().getRst()) {
                    long durationMs = ChronoUnit.MILLIS.between(
                            Instant.ofEpochMilli(session.startTime),
                            Instant.ofEpochMilli(session.endTime)
                    );

                    if (session.clientToServerSize != 0 || session.serverToClientSize != 0) {
                        // 拆分 IP 和端口
                        String[] clientParts = session.clientIpPort.split(":");
                        String requestIp = clientParts[0];
                        int requestPort = Integer.parseInt(clientParts[1]);
                        String[] serverParts = session.serverIpPort.split(":");
                        String responseIp = serverParts[0];
                        int responsePort = Integer.parseInt(serverParts[1]);

                        NetworkSessionAudit audit = new NetworkSessionAudit(
                                requestIp,
                                requestPort,
                                responseIp,
                                responsePort,
                                Instant.ofEpochMilli(session.startTime).toString(),
                                TimeUtils.getReqTime(),
                                session.clientToServerSize,
                                session.serverToClientSize,
                                durationMs,
                                session.protocolVersion,
                                session.isTlsDetected ? "" : extractUrl(tcpPacket)
                        );
                        networkSessionAuditList.add(audit);
                        sessionMap.remove(sessionId);
                        logger.info("Parsed session: {}", audit);
                    }
                }
            }
        } catch (PcapNativeException | NotOpenException e) {
            logger.error("Error processing pcap file: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to process pcap file: " + e.getMessage(), e);
        }

        return networkSessionAuditList;
    }

    // 绑定双向会话 ID 进行统计
    private static String generateSessionId(String srcIpPort, String dstIpPort) {
        // 确保双向会话使用同一 sessionId
        String[] srcParts = srcIpPort.split(":");
        String[] dstParts = dstIpPort.split(":");
        String srcIp = srcParts[0];
        String srcPort = srcParts[1];
        String dstIp = dstParts[0];
        String dstPort = dstParts[1];

        // 按字典序排序，确保双向会话 ID 一致
        if (srcIp.compareTo(dstIp) < 0) {
            return srcIp + ":" + srcPort + ":" + dstIp + ":" + dstPort;
        } else if (srcIp.compareTo(dstIp) > 0) {
            return dstIp + ":" + dstPort + ":" + srcIp + ":" + srcPort;
        } else {
            return srcIp + ":" + (Integer.parseInt(srcPort) < Integer.parseInt(dstPort) ? srcPort + ":" + dstPort : dstPort + ":" + srcPort);
        }
    }

    // 检测协议版本和 URL
    private static void detectProtocolAndUrl(TcpPacket tcpPacket, Session session) {
        Packet payload = tcpPacket.getPayload();
        if (payload != null && payload.getRawData().length > 5) {
            byte[] data = payload.getRawData();
            String payloadStr = new String(data, java.nio.charset.StandardCharsets.UTF_8).trim();

            // 检测 TLS（包括 TLSv1.2）
            if (data[0] == 0x16 && data[1] == 0x03 && (data[2] == 0x03 || data[2] == 0x02 || data[2] == 0x01)) {
                session.isTlsDetected = true;
                session.protocolVersion = "TLSv1.2";
            }
            // 检测 HTTP
            else if (payloadStr.contains("GET") || payloadStr.contains("POST") || payloadStr.contains("HTTP")) {
                session.protocolVersion = "HTTP";
            }
            // 检测 FTP
            else if (payloadStr.contains("USER") || payloadStr.contains("PASS") || payloadStr.contains("FTP")) {
                session.protocolVersion = "FTP";
            }
            // 检测 SMTP
            else if (payloadStr.contains("HELO") || payloadStr.contains("MAIL FROM") || payloadStr.contains("SMTP")) {
                session.protocolVersion = "SMTP";
            }
        }
    }

    // 获取 URL 信息
    private static String extractUrl(TcpPacket tcpPacket) {
        Packet payload = tcpPacket.getPayload();
        if (payload != null && payload.getRawData().length > 0) {
            String data = new String(payload.getRawData(), java.nio.charset.StandardCharsets.UTF_8).trim();
            // 优化正则表达式，适配更多 HTTP 请求格式
            Pattern pattern = Pattern.compile("(?i)(GET|POST|HEAD)\\s+([^\\s?]+\\??[^\\s]*)\\s+HTTP/\\d\\.\\d");
            Matcher matcher = pattern.matcher(data);
            if (matcher.find()) {
                String url = matcher.group(2).trim();
                // 确保 URL 有效，去除多余空白
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    return url;
                } else {
                    return "http://" + url; // 默认添加协议头
                }
            }
        }
        return "";
    }

    // 测试方法
    public static void main(String[] args) {
        String pcapFilePath = "E:\\pcap\\pcapcleanbeforepath\\20241030\\2024_09_11_08_44_38.pcap";
        List<NetworkSessionAudit> networkSessionAudits = parsePcap(pcapFilePath);
        networkSessionAudits.forEach(System.out::println);
    }
}
