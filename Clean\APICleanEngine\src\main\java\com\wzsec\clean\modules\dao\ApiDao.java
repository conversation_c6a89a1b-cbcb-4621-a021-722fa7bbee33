package com.wzsec.clean.modules.dao;

import com.wzsec.clean.common.utils.DatabaseConfig;
import com.wzsec.clean.modules.model.AccountLoginRecord;
import com.wzsec.clean.modules.model.ApiEncryptedtraffic;
import com.wzsec.clean.modules.model.Apidiscovery;
import com.wzsec.clean.modules.model.IcInterfaceInfo;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ApiDao {

    private static final DataSource dataSource = DatabaseConfig.createDataSource();


    public static List<IcInterfaceInfo> selectInterfaceInfo() {
        List<IcInterfaceInfo> list = new ArrayList<>();
        String sql = "select * from sdd_ic_interfaceinfo";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
                icInterfaceInfo.setId(Long.parseLong(String.valueOf(rs.getString("id"))));
                icInterfaceInfo.setApicode((String) rs.getString("apicode"));  //接口编码
                icInterfaceInfo.setApiname((String) rs.getString("apiname"));  //接口名称
                icInterfaceInfo.setInterface_status((String) rs.getString("interface_status"));  //接口状态
                icInterfaceInfo.setArea((String) rs.getString("area"));  //区县
                icInterfaceInfo.setData_source((String) rs.getString("data_source"));  //部门
                list.add(icInterfaceInfo);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list;
    }


    public static List<String> getInterfaceDiscoveryTable() {
        List<String> list = new ArrayList<>();
        String sql = "select distinct apicode from das_api_apidiscovery";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                list.add(rs.getString("apicode"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list;
    }

    public static List<String> cleanAPIServiceList(String flag) {
        List<String> list = new ArrayList<>();
        String sql = "SELECT value FROM dict_detail WHERE dict_id ='" + flag + "'";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                list.add(rs.getString("value"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list;
    }


    public static String cleanAPIServiceFlag(String flag) {
        String sql = "SELECT value FROM dict_detail JOIN dict ON dict_detail.dict_id = dict.id WHERE value = 'true' and name = '" + flag + "'";
        try (Connection conn = dataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                return rs.getString("value");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 入库接口发现表
     *
     * @param apidiscovery 接口发现表
     */
    public static void saveApidiscovery(Apidiscovery apidiscovery) {
        String sql = "insert into das_api_apidiscovery(apicode, apiname, apiip, apiport, url, req_example, res_example, category, risk, labels, accessdomain, apistatus, inserttime, updateuser, updatetime, sparefield1, sparefield2, sparefield3, sparefield4, reqdatatag, resdatatag, dataformat) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            statement.setString(1, apidiscovery.getApicode());
            statement.setString(2, apidiscovery.getApiname());
            statement.setString(3, apidiscovery.getApiip());
            statement.setString(4, apidiscovery.getApiport());
            statement.setString(5, apidiscovery.getUrl());
            statement.setString(6, apidiscovery.getReq_example());
            statement.setString(7, apidiscovery.getRes_example());
            statement.setString(8, apidiscovery.getCategory());
            statement.setString(9, apidiscovery.getRisk());
            statement.setString(10, apidiscovery.getLabels());
            statement.setString(11, apidiscovery.getAccessdomain());
            statement.setString(12, apidiscovery.getApistatus());
            statement.setString(13, apidiscovery.getInserttime());
            statement.setString(14, apidiscovery.getUpdateuser());
            statement.setString(15, apidiscovery.getUpdatetime());
            statement.setString(16, apidiscovery.getSparefield1());
            statement.setString(17, apidiscovery.getSparefield2());
            statement.setString(18, apidiscovery.getSparefield3());
            statement.setString(19, apidiscovery.getSparefield4());
            statement.setString(20, apidiscovery.getReqdatatag());
            statement.setString(21, apidiscovery.getResdatatag());
            statement.setString(22, apidiscovery.getDataformat());

            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    /**
     *TODO  入库通用版接口发现表
     *
     * @param apidiscovery 接口发现表
     */
    public static void saveApiDiscoveryBeCommon(Apidiscovery apidiscovery) {
        String sql = "insert into das_api_apidiscovery(apicode, apiname, apiip, apiport, url, req_example, res_example, category, risk, labels, accessdomain, apistatus, inserttime, updateuser, updatetime, sparefield1, sparefield2, sparefield3, sparefield4) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            statement.setString(1, apidiscovery.getApicode());
            statement.setString(2, apidiscovery.getApiname());
            statement.setString(3, apidiscovery.getApiip());
            statement.setString(4, apidiscovery.getApiport());
            statement.setString(5, apidiscovery.getUrl());
            statement.setString(6, apidiscovery.getReq_example());
            statement.setString(7, apidiscovery.getRes_example());
            statement.setString(8, apidiscovery.getCategory());
            statement.setString(9, apidiscovery.getRisk());
            statement.setString(10, apidiscovery.getLabels());
            statement.setString(11, apidiscovery.getAccessdomain());
            statement.setString(12, apidiscovery.getApistatus());
            statement.setString(13, apidiscovery.getInserttime());
            statement.setString(14, apidiscovery.getUpdateuser());
            statement.setString(15, apidiscovery.getUpdatetime());
            statement.setString(16, apidiscovery.getSparefield1());
            statement.setString(17, apidiscovery.getSparefield2());
            statement.setString(18, apidiscovery.getSparefield3());
            statement.setString(19, apidiscovery.getSparefield4());

            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * [国家管网]入库接口发现表
     *
     * @param icInterfaceInfo 接口发现表
     */
    public static void saveInterfaceInfoByPipeline(IcInterfaceInfo icInterfaceInfo) {
        String sql = "insert into sdd_ic_interfaceinfo (" +
                "apiip, apiport, apicode, interface_status, sparefield2, sparefield4, data_format, url, syncstate, createtime) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            statement.setString(1, icInterfaceInfo.getApiip());
            statement.setString(2, icInterfaceInfo.getApiport());
            statement.setString(3, icInterfaceInfo.getApicode());
            statement.setString(4, icInterfaceInfo.getInterface_status());
            statement.setString(5, icInterfaceInfo.getSparefield2());
            statement.setString(6, icInterfaceInfo.getSparefield4());
            statement.setString(7, icInterfaceInfo.getData_format());
            statement.setString(8, icInterfaceInfo.getUrl());
            statement.setString(9, icInterfaceInfo.getSyncstate());
            statement.setString(10, icInterfaceInfo.getCreatetime());

            statement.executeUpdate();
        } catch (SQLException e) {
            // 插入报错对象打印
            System.err.println(icInterfaceInfo);
            e.printStackTrace();
        }
    }


    /**
     * 入库账号登录记录表
     *
     * @param accountLoginRecord 账号登录记录表
     */
    public static void saveAccountLoginRecord(AccountLoginRecord accountLoginRecord) {
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            PreparedStatement statement = connection.prepareStatement(
                    "insert into das_api_accountloginrecord(account,clientip,clientport,serverip,serverport,token,requestterminal,requesttime,creationtime,sparefield1,sparefield2,sparefield3,sparefield4) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            );
            statement.setString(1, accountLoginRecord.getAccount());
            statement.setString(2, accountLoginRecord.getClientip());
            statement.setString(3, accountLoginRecord.getClientport());
            statement.setString(4, accountLoginRecord.getServerip());
            statement.setString(5, accountLoginRecord.getServerport());
            statement.setString(6, accountLoginRecord.getToken());
            statement.setString(7, accountLoginRecord.getRequestterminal());
            statement.setString(8, accountLoginRecord.getRequesttime());
            statement.setString(9, accountLoginRecord.getCreationtime());
            statement.setString(10, accountLoginRecord.getSparefield1());
            statement.setString(11, accountLoginRecord.getSparefield2());
            statement.setString(12, accountLoginRecord.getSparefield3());
            statement.setString(13, accountLoginRecord.getSparefield4());
            statement.executeUpdate();
            statement.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    /**
     * 入库接口加密流量临时表
     *
     * @param apiEncryptedtraffic 入库接口加密流量临时表
     */
    public static void saveApiEncrypteDtraffic(ApiEncryptedtraffic apiEncryptedtraffic) {
        String sql = "insert into sdd_api_encryptedtraffic(reqip, reqport, protocol, resip, resport, calltime, sparefield1, sparefield2, sparefield3, sparefield4) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, apiEncryptedtraffic.getReqip());
            statement.setString(2, apiEncryptedtraffic.getReqport());
            statement.setString(3, apiEncryptedtraffic.getProtocol());
            statement.setString(4, apiEncryptedtraffic.getResip());
            statement.setString(5, apiEncryptedtraffic.getResport());
            statement.setString(6, apiEncryptedtraffic.getCalltime());
            statement.setString(7, apiEncryptedtraffic.getSparefield1());
            statement.setString(8, apiEncryptedtraffic.getSparefield2());
            statement.setString(9, apiEncryptedtraffic.getSparefield3());
            statement.setString(10, apiEncryptedtraffic.getSparefield4());
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * [标签 API] 根据标签名称或标签值查询标签 ID（忽略大小写）
     *
     * @param labelName 标签名称
     * @return 标签 ID，如果找不到返回 null
     */
    public static String findLabelId(String labelName) {
        String querySql = "SELECT id FROM t_labelinfo WHERE LOWER(labelname) = LOWER(?) OR LOWER(labelvalue) = LOWER(?)";
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(querySql)) {
            statement.setString(1, labelName);
            statement.setString(2, labelName);
            try (ResultSet rs = statement.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("id");
                }
            }
        } catch (SQLException e) {
            System.err.println("查询标签 ID 失败 - labelName: " + labelName);
            e.printStackTrace();
        }
        return null;
    }


    /**
     * [标签 API] 插入标签与 API 映射关系
     *
     * @param labelId 标签 ID
     * @param apiCode API 代码
     */
    public static void saveLabelApi(String labelId, String apiCode) {
        String checkSql = "SELECT COUNT(*) FROM labels_apis WHERE labelid = ? AND apicode = ?";
        String insertSql = "INSERT INTO labels_apis (labelid, apicode) VALUES (?, ?)";

        try (Connection connection = dataSource.getConnection()) {
            // 先检查是否已存在
            try (PreparedStatement checkStatement = connection.prepareStatement(checkSql)) {
                checkStatement.setString(1, labelId);
                checkStatement.setString(2, apiCode);
                try (ResultSet rs = checkStatement.executeQuery()) {
                    if (rs.next() && rs.getInt(1) > 0) {
                        // 已存在则不插入
                        //System.out.println("标签与 API 映射已存在，跳过插入: " + labelId + " - " + apiCode);
                        return;
                    }
                }
            }

            // 执行插入
            try (PreparedStatement insertStatement = connection.prepareStatement(insertSql)) {
                insertStatement.setString(1, labelId);
                insertStatement.setString(2, apiCode);
                int rowsAffected = insertStatement.executeUpdate();
                //if (rowsAffected > 0) {
                    //System.out.println("标签与 API 映射插入成功: " + labelId + " - " + apiCode);
                //}
            }
        } catch (SQLException e) {
            // 插入报错对象打印
            System.err.println("插入失败 - labelId: " + labelId + ", apiCode: " + apiCode);
            e.printStackTrace();
        }
    }

}
