package com.wzsec.clean.common.analysis;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.ParamUtil;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.PcapService;
import com.wzsec.clean.common.rule.ProCommonRule;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;

/**
 * HTTP、FTP流量检测，清洗完直接检测
 */
@Component
@Transactional
public class PcapParser_v1 {


    private static Logger logger = LoggerFactory.getLogger(PcapParser_v1.class);

    private static Map<String, Integer> countMap = new HashMap<>();
    private static Map<String, HTTPResultDetail> resultMap = new HashMap<>();
    private static Map<String, HTTPResultDetail> ackMap = new HashMap<>();
    // 敏感数据检测管理查询(key=敏感数据，value=敏感数据英文名)
    public static HashMap<String, String> sensitivedataMap = new HashMap<>();
    //统计每行包含结果类型
    public static HashSet<String> resultTypeSet = new HashSet<>();
    // 明文统计次数结果Map
    public static Map<String, Map<String, Integer>> sensitiveDataCountResultMap = new HashMap<>();
    //结果类型
    public static Map<String, Integer> resTypeMap = new HashMap<>();
    //检测总次数
    public static HashMap<String, Integer> totalCountByMethodMap = new HashMap<>();
    //FTP用户名密码
    //FTP信息Map
    public static Map<String, String> ftpUserMap = new HashMap<>();
    public static Map<String, Integer> ftpPortMap = new HashMap<>();
    public static Map<String, String> ftpDataTransferMap = new HashMap<>();
    public static Map<String, FTPResultDetail> ftpTransferMap = new HashMap<>();
    public static Map<String, FTPResultDetail> ftpResultMap = new HashMap<>();
    //FTP打开关闭连接
    public static boolean connect = false;
    //FTP 文件list
    public static List<String> ftpList = new ArrayList<>();
    //FTP 结果Map保存文件信息
    public static Map<String, List<String>> ftpListMap = new HashMap<>();
    //FTP 结果Map对象信息
    public static Map<String, FTPResultDetail> ftpEntityMap = new HashMap<>();

    private boolean flag;

    @Autowired
    private PcapService pcapService;


    //	@Scheduled(cron = "${pcap.cron}")
    public void getRes() {
        try {
            // 手机号白名单初始化
            Const.whitePhoneList = pcapService.getFileListInfo(Const.PHONEDETECTION, Const.WHITELIST);
            // 敏感数据检测管理查询
            sensitivedataMap = pcapService.selectSensitiveData(Const.INTERFACE_FLOW_SIGN);
            //结果类型
            resTypeMap = pcapService.selectResultType();

            String pcapDir = ConfigurationManager.getProperty("PcapCleanBeforePath").trim();
            String pcapDate = ConfigurationManager.getProperty("PcapDate").trim();
            String IntervalDay = ConfigurationManager.getProperty("Interval.day").trim();

            //间隔天数
            int INTERVAL_DAY = Integer.parseInt(IntervalDay);
            //pcap检测日期
            if ("".equals(pcapDate) || pcapDate == null) {
                //默认检测前一天
                pcapDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", INTERVAL_DAY);
            }
            //检测前路径
            String parserBeforeDir = "";
            if (pcapDir.endsWith(File.separator)) {
                parserBeforeDir = pcapDir + pcapDate;
            } else {
                parserBeforeDir = pcapDir + File.separator + pcapDate;
            }
            Map<String, String> fileNameMap = new HashMap<>();
            getAllFileName(parserBeforeDir, fileNameMap);

            //检测任务号  61_代表pcap
            String taskNum = "61" + TimeUtils.DateToStr1(new Date());
            //开始时间
            String startTime = TimeUtils.getNowTime();


            //文件解析
            if (fileNameMap != null) {
                Set<String> keys = fileNameMap.keySet();
                if (keys.size() != 0) {
                    logger.info("开始解析pcap文件");
                    logger.info("文件数量：" + fileNameMap.size());

                    for (String key : keys) {
                        parser(key);
                        flag = true;
                    }
                    logger.info("pcap文件解析完毕");
                } else {
                    logger.info("文件数量为0");
                }
            }

            //HTTP流量结果统计保存信息
            recordHTTPCheckResultInfo(taskNum, startTime);
            //FTP流量结果统计保存信息
            recordFTPCheckResultInfo(taskNum, startTime);
            //结束时间
            String endTime = TimeUtils.getNowTime();
            logger.info("开始时间：" + startTime);
            logger.info("结束时间：" + endTime);
            int time = TimeUtils.getTimeSecondsByBothDate(startTime, endTime);
            logger.info("任务号" + taskNum + ",检测pcap文件共耗时：" + time + "秒");
            //变量销毁
            destruction();
        } catch (Exception e) {
            flag = false;
            logger.info("检测流量结果统计出现异常：" + e.getMessage());
            e.printStackTrace();
        }


    }

    /**
     * FTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * <AUTHOR>
     * @date 2020-11-02
     */
    private void recordFTPCheckResultInfo(String taskNum, String startTime) {
        if (ftpListMap.size() > 0) {
            Map<String, FTPResultDetail> detailMap = new HashMap<>();
            Map<String, FTPResult> outlineMap = new HashMap<>();
            for (String key : ftpListMap.keySet()) {
                if (ftpListMap.containsKey(key)) {
                    FTPResultDetail ftpEntity = ftpEntityMap.get(key);
                    List<String> list = ftpListMap.get(key);
                    if (list != null) {
                        List<Integer> resultTypeList = checkSensitiveDataByUseRule(key, list);

                        for (Integer resyltTypeNum : resultTypeList) {
                            FTPResultDetail ftpDataNew = new FTPResultDetail();
                            String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                            //根据id查询结果类型
                            Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);
                            ftpDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                            if (!ftpDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                                ftpDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype())));
                            }
                            ftpDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
                            ftpDataNew.setChecktime(startTime);
                            ftpDataNew.setDesip(ftpEntity.getDesip());
                            ftpDataNew.setDesport(ftpEntity.getDesport());
                            ftpDataNew.setSourceip(ftpEntity.getSourceip());
                            ftpDataNew.setSourceport(ftpEntity.getSourceport());
                            ftpDataNew.setOperationtime(ftpEntity.getOperationtime());

                            Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype());
                            Collection<Integer> values = checkCountMap.values();
                            int checkCount = 0;
                            for (Integer value : values) {
                                checkCount += value;
                            }
                            Integer totalCount = totalCountByMethodMap.get(key);
                            ftpDataNew.setResulttypecount(String.valueOf(checkCount));
                            ftpDataNew.setTotalcount(String.valueOf(totalCount));
                            double rate = 100 * ((double) checkCount / totalCount);
                            BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                            double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            ftpDataNew.setRatio(String.valueOf(ratio));
                            ftpDataNew.setSign(ftpEntity.getSign());
                            ftpDataNew.setUsername(ftpEntity.getUsername());
                            ftpDataNew.setPassword(ftpEntity.getPassword());
                            ftpDataNew.setFilename(ftpEntity.getFilename());
                            ftpDataNew.setFilepath(ftpEntity.getFilepath());
                            ftpDataNew.setFilesize(ftpEntity.getFilesize());
                            detailMap.put(resKey, ftpDataNew);


                            //FTP详情统计
                            FTPResult ftpResult = new FTPResult();
                            ftpResult.setTaskname(taskNum);
                            ftpResult.setChecktime(startTime);
                            ftpResult.setProtocol(Const.FTP_SIGN);
                            ftpResult.setSourceip(ftpDataNew.getSourceip());
                            ftpResult.setSourceport(ftpDataNew.getSourceport());
                            ftpResult.setDesip(ftpDataNew.getDesip());
                            ftpResult.setDesport(ftpDataNew.getDesport());

                            String[] keys = key.split(Const.AUDIT_SPLIT_JOIN);
                            if (keys.length > 4) {
                                String riskKey = keys[0] + Const.AUDIT_SPLIT_JOIN + keys[1] + Const.AUDIT_SPLIT_JOIN +
                                        keys[2] + Const.AUDIT_SPLIT_JOIN + keys[3] + Const.AUDIT_SPLIT_JOIN +
                                        ftpDataNew.getRisk();

                                if (outlineMap.containsKey(riskKey)) {
                                    FTPResult result = outlineMap.get(riskKey);
                                    result.setRisk(ftpDataNew.getRisk());
                                    result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
                                    outlineMap.put(riskKey, result);
                                } else {
                                    ftpResult.setRisk(ftpDataNew.getRisk());
                                    ftpResult.setRiskcount("1");
                                    outlineMap.put(riskKey, ftpResult);
                                }
                            }

                        }
                    }
                }
            }

            if (detailMap != null) {
                Collection<FTPResultDetail> pcapDatas = detailMap.values();
                for (FTPResultDetail ftpData : pcapDatas) {
                    pcapService.saveFtpResultDetail(ftpData);
                }
            }
            logger.info("保存FTP流量检测详情统计完成！");
            if (outlineMap != null) {
                Collection<FTPResult> ftpResults = outlineMap.values();
                for (FTPResult ftpResult : ftpResults) {
                    pcapService.saveFTPResult(ftpResult);
                }
            }
            logger.info("保存FTP流量检测概要统计完成！");
        } else {
            logger.info("FTP流量检测统计结果为空！");
        }

    }

    /**
     * HTTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * <AUTHOR>
     * @date 2020-11-02
     */
    private void recordHTTPCheckResultInfo(String taskNum, String startTime) {

        Map<String, HTTPResultDetail> detailMap = new HashMap<>();
        Map<String, HTTPResult> outlineMap = new HashMap<>();

        //保存pcap文件信息
        if (resultMap != null && resultMap.size() != 0) {

            Set<String> keySet = resultMap.keySet();
            for (String key : keySet) {
                HTTPResultDetail httpResultDetail = resultMap.get(key);

                ArrayList<String> list = null;
                if (httpResultDetail.getReqcontent() != null) {
                    String[] datas = httpResultDetail.getReqcontent().split(",|:|\"|\\{|}|\\[|]|\r\n");
                    list = convertArrToList(datas);
                } else {
                    String[] datas = httpResultDetail.getRescontent().split(",|:|\"|\\{|}|\\[|]|\n");
                    list = convertArrToList(datas);
                }
                if (list != null) {

                    //概要统计接口调用总数
                    String totalcount = "";
                    String countKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN));
                    if (countMap.containsKey(countKey)) {
                        totalcount = String.valueOf(countMap.get(countKey));
                    }

                    //详情统计 统计敏感数据
                    List<Integer> resultTypeList = checkSensitiveDataByUseRule(key, list);

                    for (Integer resyltTypeNum : resultTypeList) {

                        HTTPResultDetail httpResultDetailNew = new HTTPResultDetail();
                        httpResultDetailNew.setSeqnumber(httpResultDetail.getSeqnumber());
                        httpResultDetailNew.setInterfaceuri(httpResultDetail.getInterfaceuri());
                        httpResultDetailNew.setChecktime(startTime);
                        httpResultDetailNew.setStarttime(httpResultDetail.getStarttime());
                        httpResultDetailNew.setEndtime(httpResultDetail.getEndtime());
                        httpResultDetailNew.setSourceip(httpResultDetail.getSourceip());
                        httpResultDetailNew.setSourceport(httpResultDetail.getSourceport());
                        httpResultDetailNew.setDesip(httpResultDetail.getDesip());
                        httpResultDetailNew.setDesport(httpResultDetail.getDesport());
                        httpResultDetailNew.setRescontent(httpResultDetail.getRescontent());
                        httpResultDetailNew.setReqcontent(httpResultDetail.getReqcontent());
                        httpResultDetailNew.setResstatus(httpResultDetail.getResstatus());
                        httpResultDetailNew.setResstatuscode(httpResultDetail.getResstatuscode());
                        httpResultDetailNew.setReqmethod(httpResultDetail.getReqmethod());
                        httpResultDetailNew.setUserid(httpResultDetail.getUserid());
                        httpResultDetailNew.setUsername(httpResultDetail.getUsername());
                        httpResultDetailNew.setCheckrule(httpResultDetail.getCheckrule());

                        String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                        //根据id查询结果类型
                        Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);

                        httpResultDetailNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                        if (!httpResultDetailNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                            httpResultDetailNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getResulttype())));
                        }
                        httpResultDetailNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");

                        Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getResulttype());
                        Collection<Integer> values = checkCountMap.values();
                        int checkCount = 0;
                        for (Integer value : values) {
                            checkCount += value;
                        }
                        Integer totalCount = totalCountByMethodMap.get(key);
                        httpResultDetailNew.setResulttypecount(String.valueOf(checkCount));
                        httpResultDetailNew.setChecktotalcount(String.valueOf(totalCount));
                        double rate = 100 * ((double) checkCount / totalCount);
                        BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                        double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        httpResultDetailNew.setRatio(String.valueOf(ratio));
                        detailMap.put(resKey, httpResultDetailNew);


                        String riskKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN)) + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getRisk();
                        if (outlineMap.containsKey(riskKey)) {
                            HTTPResult result = outlineMap.get(riskKey);
                            result.setRisk(httpResultDetailNew.getRisk());
                            result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
                            outlineMap.put(riskKey, result);
                        } else {
                            HTTPResult httpResult = new HTTPResult();
                            httpResult.setTaskname(taskNum);
                            httpResult.setChecktime(startTime);
                            httpResult.setInterfaceuri(httpResultDetail.getInterfaceuri());
                            httpResult.setSourceip(httpResultDetail.getSourceip());
                            httpResult.setSourceport(httpResultDetail.getSourceport());
                            httpResult.setDesip(httpResultDetail.getDesip());
                            httpResult.setDesport(httpResultDetail.getDesport());
                            httpResult.setProtocol(Const.HTTP_SIGN);
                            httpResult.setRisk(httpResultDetailNew.getRisk());
                            httpResult.setRiskcount("1");
                            httpResult.setTotalcount(totalcount);
                            outlineMap.put(riskKey, httpResult);
                        }
                    }
                }
            }
            logger.info("保存接口流量检测概要统计完成！");
            //保存详情结果
            if (detailMap != null) {
                Collection<HTTPResultDetail> httpResultDetails = detailMap.values();

                for (HTTPResultDetail httpResultDetail : httpResultDetails) {
                    pcapService.saveHttpResultDetail(httpResultDetail);
                }
                logger.info("保存接口流量检测详情统计完成！");
            }

            if (outlineMap != null) {
                Collection<HTTPResult> httpResults = outlineMap.values();
                for (HTTPResult httpResult : httpResults) {
                    pcapService.saveHttpResult(httpResult);
                }
            }

        } else {
            logger.info("接口流量检测统计结果为空！");
        }
    }

    /**
     * 清洗数组中空字符串
     *
     * @param datas
     * <AUTHOR>
     * @date 2020-11-02
     */
    private ArrayList<String> convertArrToList(String[] datas) {
        ArrayList<String> list = new ArrayList<>();
        for (String data : datas) {
            if (!data.equals("")) {
                list.add(data);
            }
        }
        return list;
    }

    /**
     * @description 解析pcap文件
     * <AUTHOR>
     * @date 2020-11-02
     */
    public void parser(String fielDir) throws IOException {

        FileInputStream fis = new FileInputStream(fielDir);
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        int m = fis.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);

        while (m > 0) {
            Packet data = new Packet();

            m = fis.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));


            byte[] content = new byte[data.getpLength()];
            m = fis.read(content);

            //截取有效负载payload 文本内容从66位开始
            if (content.length > 66) {
                byte[] contentbyte = new byte[content.length - 66];
                System.arraycopy(content, 66, contentbyte, 0, content.length - 66);
                data.setContent_byte(contentbyte);
            } else {
                data.setContent_byte(content);
            }


            byte[] ver_ihla = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 14;
                ver_ihla[0] = content[b];
            }

            byte[] pro = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 23;
                pro[i] = content[b];
            }

            StringBuilder sbr = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 26;
                sbr.append(content[b] & 0xff);
                sbr.append(".");
            }
            sbr.deleteCharAt(sbr.length() - 1);
            data.setSourceip(sbr.toString());

            StringBuilder sba = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 30;
                sba.append(content[b] & 0xff);
                sba.append(".");
            }
            sba.deleteCharAt(sba.length() - 1);
            data.setDesip(sba.toString());

            if ((short) ver_ihla[0] == 69) {
                if ((short) pro[0] == 6) {
                    //TCP协议
                    StringBuilder sbd = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 34;
                        sbd.append(Integer.toHexString(content[b] & 0xff));
                    }
                    Integer souport = Integer.valueOf(sbd.toString(), 16);
                    data.setSourceport(String.valueOf(souport));

                    StringBuilder sbe = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 36;
                        sbe.append(Integer.toHexString(content[b] & 0xff));
                    }
                    Integer desport = Integer.valueOf(sbe.toString(), 16);
                    data.setDesport(String.valueOf(desport));

                    StringBuilder sbf = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 38;
                        sbf.append(content[b] & 0xff);
                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    data.setSeq_number(sbf.toString());


                    StringBuilder sbg = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 42;
                        sbg.append(content[b] & 0xff);
                    }
                    sbg.deleteCharAt(sbg.length() - 1);
                    data.setAck_number(sbg.toString());

                    //数据包转文本内容
                    String strContent = content2Str(data.getContent_byte(), "UTF-8");

                    //分析响应内容并保存Map HTTP
                    saveDataToMapForHTTP(data, strContent);

                    //分析响应内容并保存List FTP
                    saveDataToListForFTP(data, strContent);
                }
            }

        }
        fis.close();
    }

    /**
     * 分析解析数据包内容保存List FTP
     *
     * @param strContent 数据
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private void saveDataToListForFTP(Packet data, String strContent) {
        strContent = strContent.replaceAll("\r\n", "");
        FTPResultDetail ftpData = new FTPResultDetail();

        //FTP 协议
        String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();
        //拆分数据传输的内容
        if (ftpDataTransferMap.containsKey(key)) {
            if (data.getContent_byte().length > 66) {//过滤字节小于66位的数据
                String ftpTransferKey = ftpDataTransferMap.get(key);
                int bytes = data.getContent_byte().length;
                String[] dataStr = strContent.split(",|:|\"|\\{|}|\\[|]|\t|\r|\n|\r\n");
                ArrayList<String> list = convertArrToList(dataStr);
                ftpList.addAll(list);

                //计算文件大小
                if (ftpResultMap.containsKey(ftpTransferKey)) {
                    FTPResultDetail ftpResultDetail = ftpResultMap.get(ftpTransferKey);
                    if (StringUtils.isNotEmpty(ftpResultDetail.getFilesize())) {
                        String filesize = ftpResultDetail.getFilesize();
                        long size = Long.valueOf(filesize).longValue() + (long) bytes;
                        ftpResultDetail.setFilesize(String.valueOf(size));
                        ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                    } else {
                        ftpResultDetail.setFilesize(String.valueOf(bytes));
                        ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                    }
                }
            }
        }
        //保存数据传输的基本信息
        if (!ftpTransferMap.containsKey(key)) {
            ftpData.setSourceip(data.getSourceip());
            ftpData.setSourceport(data.getSourceport());
            ftpData.setDesip(data.getDesip());
            ftpData.setDesport(data.getDesport());
            ftpTransferMap.put(key, ftpData);
        }
        //截取4位去空格，FTP请求指令是4位，响应码是3位
        String code = "";
        if (strContent.length() > 4) {
            code = strContent.substring(0, 4).trim();
        }

        if (code.length() == 3) {
            switch (code) {
                case Const.FTP_CWD:
                    String path = strContent.split(Const.FTP_CWD)[1].trim();
                    FTPResultDetail ftpResultDetail = ftpTransferMap.get(key);
                    ftpResultDetail.setFilepath(path);
//					ftpTransferMap.put(key,ftpResultDetail);
                    break;
                case Const.FTP_226_TRANSFER_COMPLETE:
                    //传输完成
                    Set<String> ftpResultKeyMap = ftpResultMap.keySet();
                    for (String reskey : ftpResultKeyMap) {
                        FTPResultDetail ftpEntity = new FTPResultDetail();
                        FTPResultDetail ftpResult = ftpResultMap.get(reskey);
                        ftpEntity.setSourceip(ftpResult.getSourceip());
                        ftpEntity.setSourceport(ftpResult.getSourceport());
                        ftpEntity.setDesip(ftpResult.getDesip());
                        ftpEntity.setDesport(ftpResult.getDesport());
                        ftpEntity.setOperationtime(ftpResult.getOperationtime());
                        ftpEntity.setUsername(ftpResult.getUsername());
                        ftpEntity.setPassword(ftpResult.getPassword());
                        ftpEntity.setFilename(ftpResult.getFilename());
                        ftpEntity.setFilepath(ftpResult.getFilepath());
                        ftpEntity.setFilesize(ftpResult.getFilesize());
                        ftpEntity.setSign(ftpResult.getSign());
                        ftpEntityMap.put(reskey, ftpEntity);
                        ftpListMap.put(reskey, ftpList);
                        //文件传输完成清空list
                        ftpList = new ArrayList<>();
                    }
                    ftpResultMap = new HashMap<>();
                    break;
                default:
                    break;
            }
        } else {
            switch (code) {
                case Const.FTP_USER:
                    //用户名
                    String username = strContent.split(Const.FTP_USER)[1].trim();
                    ftpUserMap.put(key, username);
                    break;
                case Const.FTP_PASS:
                    //密码
                    String user = ftpUserMap.get(key);
                    String password = strContent.split(Const.FTP_PASS)[1].trim();
                    ftpUserMap.put(key, user + Const.AUDIT_SPLIT_JOIN + password);
                    break;
                case Const.FTP_PORT:
                    //IP 地址和两字节的端口 ID
                    int port = getPort(strContent.split(" "));
                    ftpPortMap.put(key, port);
                    break;
                case Const.FTP_STOR:
                    if (ftpTransferMap.containsKey(key)) {
                        String filename = strContent.split(Const.FTP_STOR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_STOR_SIGN);
                        ftpResultDetail.setFilename(filename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        String time = TimeUtils.TimestampToDateStrNew(data.getTime_s());
                        ftpResultDetail.setOperationtime(time);
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = ftpPortMap.get(key);
                        String dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                dataPort + Const.AUDIT_SPLIT_JOIN +
                                data.getDesip() + Const.AUDIT_SPLIT_JOIN + Const.FTP_DATA_TRANSFER_PORT;
                        String fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + filename;
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                case Const.FTP_RETR:
                    if (ftpTransferMap.containsKey(key)) {
                        String filename = strContent.split(Const.FTP_RETR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_RETR_SIGN);
                        ftpResultDetail.setFilename(filename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        String time = TimeUtils.TimestampToDateStrNew(data.getTime_s());
                        ftpResultDetail.setOperationtime(time);
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = ftpPortMap.get(key);
                        String dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                Const.FTP_DATA_TRANSFER_PORT + Const.AUDIT_SPLIT_JOIN +
                                data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                        String fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + filename;
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * FTP客户端数据传输端口计算
     *
     * @param token
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private int getPort(String[] token) {
        String[] portCommand = token[token.length - 1].replaceAll("[()]\\.", "").split(",");
        int port = 0;
        if (portCommand.length == 6) {
            port = (Integer.parseInt(portCommand[4]) * 256) + Integer.parseInt(portCommand[5].replaceAll("\r\n", ""));
        }
        return port;
    }


    /**
     * 分析解析数据包内容保存Map HTTP
     *
     * @param strContent 数据
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private void saveDataToMapForHTTP(Packet data, String strContent) {

        if (strContent.contains(Const.HTTP_SIGN)) {
            //HTTP协议
            data.setProtocol(Const.HTTP_SIGN);
            String[] dataArr = strContent.split("\r\n");
            boolean type = false;
            for (String s : dataArr) {
                if (s.contains("Accept")) {
                    type = true;
                    break;

                }
                if (s.contains("Content-Type")) {
                    if (ParamUtil.checkContentTypeIsTxt(s)) {
                        type = true;
                        break;
                    }
                }
            }
            if (type) {
                String time = TimeUtils.TimestampToDateStrNew(data.getTime_s());
                data.setContent(strContent);
                String method = strContent.split("\r\n")[0].split(" ")[0];
                String[] s = strContent.split("GET|POST|PUT|DELETE");
                if (s.length == 2) {
                    String[] http = s[1].split(Const.HTTP_SIGN);
                    String interfaceuri = http[0].split("\\?")[0].trim();
                    HTTPResultDetail httpResultDetail = new HTTPResultDetail();
                    httpResultDetail.setInterfaceuri(interfaceuri);
                    httpResultDetail.setSourceip(data.getSourceip());
                    httpResultDetail.setSourceport(data.getSourceport());
                    httpResultDetail.setDesip(data.getDesip());
                    httpResultDetail.setDesport(data.getDesport());
                    httpResultDetail.setStarttime(time);
                    httpResultDetail.setReqmethod(method);
                    httpResultDetail.setReqcontent(strContent);
                    httpResultDetail.setSeqnumber(data.getAck_number());

                    String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                            data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport() + Const.AUDIT_SPLIT_JOIN + interfaceuri;

                    interfaceInfoCount(key);
                    //通过请求的ack_number匹配响应的seq_number
                    ackMap.put(data.getAck_number(), httpResultDetail);
                    resultMap.put(key + Const.AUDIT_SPLIT_JOIN + data.getTime_ms(), httpResultDetail);
                } else {
                    //根据响应的seq_number获取请求信息
                    if (ackMap.containsKey(data.getSeq_number())) {
                        String statuscode = strContent.split("\r\n")[0].split(" ")[1];
                        HTTPResultDetail httpResultDetail = ackMap.get(data.getSeq_number());
                        HTTPResultDetail httpResultDetailRes = new HTTPResultDetail();
                        httpResultDetailRes.setInterfaceuri(httpResultDetail.getInterfaceuri());
                        httpResultDetailRes.setSourceip(data.getSourceip());
                        httpResultDetailRes.setSourceport(data.getSourceport());
                        httpResultDetailRes.setDesip(data.getDesip());
                        httpResultDetailRes.setDesport(data.getDesport());
                        httpResultDetailRes.setEndtime(time);
                        httpResultDetailRes.setResstatuscode(statuscode);
                        httpResultDetailRes.setSeqnumber(data.getSeq_number());
//						httpResultDetailRes.setResstatus();
                        httpResultDetailRes.setRescontent(data.getContent());
                        String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                                data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport() + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getInterfaceuri();
                        interfaceInfoCount(key);
                        resultMap.put(key + Const.AUDIT_SPLIT_JOIN + data.getTime_ms(), httpResultDetailRes);
                    }
                }
            }

        }
    }

    /**
     * 统计接口检测总数
     *
     * @param key
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private void interfaceInfoCount(String key) {
        if (countMap.containsKey(key)) {
            countMap.put(key, countMap.get(key) + 1);
        } else {
            countMap.put(key, 1);
        }
    }

    private static String content2Str(byte[] content) {
        char[] chars = new char[content.length];
        for (int i = 0; i < content.length; i++) {
            chars[i] = (char) content[i];
        }
        return String.valueOf(chars);
    }

    /**
     * 字节数据转字符串处理中文乱码
     *
     * @param content
     * @param encode
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String content2Str(byte[] content, String encode) throws UnsupportedEncodingException {
        return new String(content, encode);
    }


    private static void reverseByteArray(byte[] arr) {
        byte temp;
        int n = arr.length;
        for (int i = 0; i < n / 2; i++) {
            temp = arr[i];
            arr[i] = arr[n - 1 - i];
            arr[n - 1 - i] = temp;
        }
    }

    private static int byteArrayToInt(byte[] b, int offset) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }

    private static short byteArrayToShort(byte[] b, int offset) {
        short value = 0;
        for (int i = 0; i < 2; i++) {
            int shift = (2 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }

        return value;
    }


    /**
     * 获取一个文件夹下的所有文件全路径和文件名
     *
     * @param path
     * @param fileNameMap
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    public static void getAllFileName(String path, Map<String, String> fileNameMap) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    if (f.getAbsolutePath().endsWith("pcap")) {
                        fileNameMap.put(f.getAbsolutePath(), f.getName());
                    }
                }
            }
        }
    }

    /*
     *@Decription 变量销毁
     *<AUTHOR>
     *@date 2020/7/10
     */
    public static void destruction() {
        countMap = new HashMap<>();
        resultMap = new HashMap<>();
        ackMap = new HashMap<>();
        sensitivedataMap = new HashMap<>();
        resultTypeSet = new HashSet<>();
        sensitiveDataCountResultMap = new HashMap<>();
        resTypeMap = new HashMap<>();
        totalCountByMethodMap = new HashMap<>();
        ftpUserMap = new HashMap<>();
        ftpTransferMap = new HashMap<>();
        ftpResultMap = new HashMap<>();
        ftpList = new ArrayList<>();
        ftpListMap = new HashMap<>();
        ftpEntityMap = new HashMap<>();
        connect = false;
        ftpPortMap = new HashMap<>();
        ftpDataTransferMap = new HashMap<>();
    }


    /**
     * @Description:检测参数数据通过通用检测规则
     * <AUTHOR> by wangqi
     * @date 2020-09-03
     */
    private List<Integer> checkSensitiveDataByUseRule(String strKey, List<String> list) {
        // 校验参数是否包含敏感数据
        resultTypeSet = new HashSet<>();
        List<Integer> resultTypeList = ProCommonRule.checkIsClearType(sensitivedataMap, sensitiveDataCountResultMap,
                strKey, list.toArray(new String[list.size()]), resultTypeSet, resTypeMap);

        // 统计检测次数
        if (list.size() > 0) {
            if (null == totalCountByMethodMap) {
                totalCountByMethodMap = new HashMap<>();
                totalCountByMethodMap.put(strKey, list.size());
            } else {
                if (totalCountByMethodMap.containsKey(strKey)) {
                    totalCountByMethodMap.put(strKey, totalCountByMethodMap.get(strKey).intValue() + list.size());
                } else {
                    totalCountByMethodMap.put(strKey, list.size());
                }
            }
        }

        return resultTypeList;
    }


    /**
     * @param map:需要根据value排序的map
     * @Description：按照行为时间进行排序，方式采集时时间顺序错误导致审计出错
     * <AUTHOR>
     * @date 2019年12月9日 下午3:13:02
     */
    private static String mapSort(Map<String, Integer> map) {
        if (map == null || map.size() == 0) {
            return null;
        }
        ArrayList<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            // 定义一个比较器
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                Integer num1 = o1.getValue();
                Integer num2 = o2.getValue();
                return num2.compareTo(num1);
            }
        });
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<String, Integer> l : list) {
            jsonObject.put(l.getKey(), l.getValue());
        }
        return jsonObject.toString();
    }
}
