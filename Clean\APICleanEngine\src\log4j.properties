###审计服务一期###
log4j.rootLogger=info,file

### 日志信息输出到日志文件  ###
log4j.appender.file = org.apache.log4j.DailyRollingFileAppender
### 指定消息输出到 logs/log.log文件
log4j.appender.file.File = ./logs/audit1service.log
### true为将消息增加到指定文件中， false指将消息覆盖指定的文件内容
log4j.appender.file.Append = true
### 输出DEBUG级别以上的日志
log4j.appender.file.Threshold = INFO
log4j.appender.file.layout = org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss} [ %t:%r ] - [ %p ] %m%n
log4j.appender.file.Encoding = UTF-8