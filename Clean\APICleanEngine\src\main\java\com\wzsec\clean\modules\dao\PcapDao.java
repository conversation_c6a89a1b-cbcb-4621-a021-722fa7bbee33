package com.wzsec.clean.modules.dao;

import com.wzsec.clean.modules.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: PcapDao
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/6/9
 */
@Mapper
public interface PcapDao {

    //保存概要信息信息
    void saveResult(PcapData pcapData);

    //保存接口流量审核任务
    void saveTask(HashMap<String, String> map);

    //敏感数据检测管理查询
    List<SMSensitiveData> selectSensitiveData(@Param("signSensitive") String signSensitive);

    //结果类型
    List<Map<String, Object>> selectResultType();

    //获取结果apicode
    List<String> selectAuthcheckresultApicode();

    //通过接口标识查询接口编码
    String queryInterfaceCoding(@Param("interfaceIdentification") String interfaceIdentification);

    //通过接口编码查询是否入库
    int queryNumberInterfaces(@Param("apicode") String apicode);

    //通过接口标识查询模型配置接口编码
    Map<String, String> queryIcModelconfig(@Param("interfaceIdentification") String interfaceIdentification);

    //通过接口编码查询接口名称
    String getInterfaceName(@Param("apicode") String apicode);

    //保存鉴权信息
    void saveAuthcheckresult(ApiAuthcheckresult apiAuthcheckresult);

    //保存接口信息表
    void saveIcCallschedule(IcCallschedule icCallschedule);

    // 保存接口信息表(sdd_ic_interfaceinfo)
    void saveIcInterfaceinfo(IcInterfaceInfo icInterfaceInfo);


    void saveIcInterfaceinfoByQinHai(IcInterfaceInfo icInterfaceInfo);


    //保存接口发现
    void saveApidiscovery(Apidiscovery apiApidiscovery);


    /**
     * 接口发现(数安平台)
     *
     * @param apiDiscoverySecurityPlatform api发现安全平台
     */
    void saveApiDiscovery_SecurityPlatform(ApiDiscoverySecurityPlatform apiDiscoverySecurityPlatform);


    //保存模型配置
    void saveIcModelconfig(IcModelconfig icModelconfig);

    //更新
    void update(IcModelconfig icModelconfig);

    //保存其他事件检测信息
    void saveOtherRiskDetect(ApiOtherRiskDetect apiOtherRiskDetect);

    //手机号检测白名单
    List<String> getFileListInfo(@Param("phonedetection") String phonedetection, @Param("whitelist") String whitelist);

    //根据id查询结果类型
    Map<String, Object> getResultTypeById(Integer id);

    //保存详情信息
    void saveDetailResult(PcapData pcapData);

    //保存FTP结果信息
    void saveFtpResult(FTPData ftpData);

    void saveHttpResult(HTTPResult httpResult);

    void saveHttpResultDetail(HTTPResultDetail httpResultDetail);

    void saveFtpResultDetail(FTPResultDetail ftpData);

    void saveFTPResult(FTPResult ftpResult);

    List<String> selectApicode();


    /**
     * 获取接口发现表
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getInterfaceDiscoveryTable();


    /**
     * 获取接口发现表
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getInterfaceDiscoverySecurityPlatform();
}
