package com.wzsec.clean.modules.service.impl;

import com.wzsec.clean.modules.dao.PcapTaskDao;
import com.wzsec.clean.modules.model.PcapTask;
import com.wzsec.clean.modules.service.PcapTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Title: PcapTaskServiceImpl
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/11
 */
@Service
public class PcapTaskServiceImpl implements PcapTaskService {

    @Autowired
    private PcapTaskDao pcapTaskDao;

    /**
     * 查询启用的任务
     *
     * @return
     */
    @Override
    public List<PcapTask> getUseTaskInfo() {
        return pcapTaskDao.getUseTaskInfo();
    }

    @Override
    public List<PcapTask> getTaskByType(String httpType, String ftpType) {
        return pcapTaskDao.getTaskByType(httpType, ftpType);
    }

    @Override
    public PcapTask getTaskById(String id) {
        return pcapTaskDao.getTaskById(id);
    }
}
