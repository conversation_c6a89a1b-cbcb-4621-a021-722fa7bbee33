package com.wzsec.clean.modules.service.impl;

import com.wzsec.clean.modules.dao.PcapStrategyConfigDao;
import com.wzsec.clean.modules.model.PcapStrategyConfig;
import com.wzsec.clean.modules.service.PcapStrategyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Title: PcapStrategyConfigImpl
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/11
 */
@Service
public class PcapStrategyConfigServiceImpl implements PcapStrategyConfigService {

    @Autowired
    private PcapStrategyConfigDao pcapStrategyConfigDao;

    /**
     * 根据策略id查询策略信息
     *
     * @param strategyid
     * @return
     */
    @Override
    public PcapStrategyConfig getStrategyConfig(String strategyid) {
        return pcapStrategyConfigDao.getStrategyConfig(strategyid);
    }
}
