//package cn.ctyun.nfd.common.utils;
//
//import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
//import org.elasticsearch.action.admin.indices.delete.DeleteIndexResponse;
//import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest;
//import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsResponse;
//import org.elasticsearch.action.admin.indices.exists.types.TypesExistsRequest;
//import org.elasticsearch.action.admin.indices.exists.types.TypesExistsResponse;
//import org.elasticsearch.action.index.IndexResponse;
//import org.elasticsearch.client.transport.TransportClient;
//import org.elasticsearch.common.settings.Settings;
//import org.elasticsearch.common.transport.InetSocketTransportAddress;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.net.InetAddress;
//import java.net.UnknownHostException;
//
////import org.elasticsearch.client.RequestOptions;
////import org.elasticsearch.client.RestHighLevelClient;
////import org.elasticsearch.client.indices.GetIndexRequest;
//
///**
// * Title: ESUtil
// * Decription:
// *
// * <AUTHOR>
// * @date 2020/11/17
// */
//public class ES2Util {
//
//    private static Logger log = LoggerFactory.getLogger(ES2Util.class);
//
//
//    //  ES2.3.4版本
//
//    /**
//     * 获取连接
//     * @param clusterName
//     * @param ipAddress
//     * @param port
//     * @return
//     */
//    public static TransportClient getClient(String clusterName,String ipAddress,int port){
//        try {
//        Settings settings = Settings.settingsBuilder()
//                .put("cluster.name", clusterName)
//                .put("client.transport.sniff", true).build();
//            TransportClient client = TransportClient.builder().settings(settings).build()
//                    .addTransportAddress(new InetSocketTransportAddress(InetAddress.getByName("*************"), 9300));
//            log.info("获取ES客户端连接成功");
//            return client;
//        } catch (UnknownHostException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * 删除索引
//     * @param client
//     * @param indexName
//     * @return
//     */
//    public static boolean deleteIndex(TransportClient client,String indexName){
//        DeleteIndexResponse deleteIndexResponse = client.admin().indices().prepareDelete(indexName).get();
//        return deleteIndexResponse.isAcknowledged();
//    }
//
//    /**
//     * 创建文档json
//     * @param client
//     * @param indexName
//     * @param type
//     * @param jsonStr
//     * @return
//     */
//    public static boolean addIndexFile(TransportClient client,String indexName,String type, String jsonStr){
//        IndexResponse indexResponse = client.prepareIndex(indexName,type).setSource(jsonStr).execute().actionGet();
//        return indexResponse.isCreated();
//    }
//
//    /**
//     * 创建索引
//     * @param client
//     * @param indexName
//     * @return
//     */
//    public static boolean createIndex(TransportClient client,String indexName){
//        CreateIndexResponse createIndexResponse = client.admin().indices().prepareCreate(indexName).get();
//        return createIndexResponse.isAcknowledged();
//    }
//
//    /**
//     * 关闭client
//     * @param client
//     */
//    public static void closeClient(TransportClient client) {
//        if (client != null) {
//            try {
//                client.close();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    /**
//     * 判断索引是否存在
//     * @param client
//     * @param indexName
//     * @return
//     */
//    public static boolean isExistsIndex(TransportClient client,String indexName){
//        IndicesExistsResponse response = client.admin().indices().exists( new IndicesExistsRequest().indices(new String[]{indexName})).actionGet();
//        return response.isExists();
//    }
//
//
//    /**
//     * 判断类型是否存在
//     * @param client
//     * @param indexName
//     * @param indexType
//     * @return
//     */
//    public boolean isExistsType(TransportClient client,String indexName,String indexType){
//        TypesExistsResponse response = client.admin().indices() .typesExists(new TypesExistsRequest(new String[]{indexName}, indexType) ).actionGet();
//        return response.isExists();
//    }
//
//    /**
//     * 查询
//     * @param client
//     * @param indexName
//     * @return
//     */
//    public static boolean searchData(TransportClient client,String indexName){
//        IndicesExistsResponse response = client.admin().indices().exists( new IndicesExistsRequest().indices(new String[]{indexName})).actionGet();
//        return response.isExists();
//    }
//
//}
