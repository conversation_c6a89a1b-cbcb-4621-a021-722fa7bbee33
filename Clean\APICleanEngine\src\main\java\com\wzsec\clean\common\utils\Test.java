package com.wzsec.clean.common.utils;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.jasypt.util.text.BasicTextEncryptor;

/**
 * @className: Test
 * @description: TODO 类描述
 * @author: mingcb
 * @date: 2023/5/22
 **/
public class Test {
    public static void main(String[] args) {
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        //加密所需的salt(盐)
        textEncryptor.setPassword("Ct$Ad@20");
        //要加密的数据（数据库的用户名或密码）
        String username = textEncryptor.encrypt("root");
        String password = textEncryptor.encrypt("<EMAIL>");
        System.out.println(username);
        System.out.println(password);

    }
}
