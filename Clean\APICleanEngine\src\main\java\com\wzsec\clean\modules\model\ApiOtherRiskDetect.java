package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 其他事件检测Entity
 *
 * <AUTHOR>
 * @version 2023-02-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiOtherRiskDetect {

    private String id;				//自增ID
    private String apicode;			//接口编码
    private String apiname;			//接口名称
    private String apiurl;			//接口URL
    private String requestip;		//请求ip
    private String serviceip;		//服务ip
    private String incident;		//事件(上传,下载等等)
    private String eventdetails;	//事件详情
    private String risk;			//风险风险(0、1、2、3)
    private String starttime;		//开始时间
    private String checktime;		//检测时间
    private String sparefield1;		//备用字段1
    private String sparefield2;		//备用字段2
    private String sparefield3;		//备用字段3
    private String sparefield4;		//备用字段4

}