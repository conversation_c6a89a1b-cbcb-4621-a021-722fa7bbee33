package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiWeaknesscheck implements Serializable {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 接口编码
     */
    private String apicode;

    /**
     * 接口名称
     */
    private String apiname;

    /**
     * 应用账号
     */
    private String appid;

    /**
     * 应用名称
     */
    private String appname;

    /**
     * URL
     */
    private String apiurl;

    /**
     * 请求ip
     */
    private String requestip;

    /**
     * 请求端口
     */
    private String requestport;

    /**
     * 响应ip
     */
    private String responseip;

    /**
     * 响应端口
     */
    private String responseport;

    /**
     * 检测规则
     */
    private String detectionrule;

    /**
     * 检测详情
     */
    private String detectiondetails;

    /**
     * 级别
     */
    private String risk;

    /**
     * 请求时间
     */
    private String calltime;

    /**
     * 检测时间
     */
    private String checktime;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;

    /**
     * 备用字段5
     */
    private String sparefield5;

    /**
     * 备用字段6
     */
    private String sparefield6;

}