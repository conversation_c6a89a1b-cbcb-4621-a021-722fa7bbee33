<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wzsec.clean.modules.dao.ApiWeaknesscheckDao">


    <insert id="saveWeaknesscheck" parameterType="com.wzsec.clean.modules.model.ApiWeaknesscheck">
        insert into das_api_weaknesscheck(id, apicode, apiname, appid, appname, apiurl, requestip,
                                         requestport, responseip, responseport, detectionrule, detectiondetails,
                                         risk, calltime, checktime, sparefield1, sparefield2, sparefield3,
                                         sparefield4, sparefield5, sparefield6)
        values (#{id}, #{apicode}, #{apiname}, #{appid}, #{appname}, #{apiurl}, #{requestip},
                #{requestport}, #{responseip}, #{responseport}, #{detectionrule}, #{detectiondetails},
                #{risk}, #{calltime}, #{checktime}, #{sparefield1}, #{sparefield2}, #{sparefield3},
                #{sparefield4}, #{sparefield5}, #{sparefield6})
    </insert>

</mapper>
