package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiDiscoverySecurityPlatform implements Serializable {

    private Integer id;

    /**
     * 接口编码
     */
    private String apicode;

    /**
     * 接口名称
     */
    private String apiname;

    /**
     * IP
     */
    private String apiip;

    /**
     * 端口
     */
    private String apiport;

    /**
     * URL
     */
    private String url;

    /**
     * 请求示例
     */
    private String req_example;

    /**
     * 响应示例
     */
    private String res_example;

    /**
     * 请求方式
     */
    private String requestmode;

    /**
     * 状态
     */
    private String apistatus;

    /**
     * 插入时间
     */
    private String inserttime;

    /**
     * 更新人
     */
    private String updateuser;

    /**
     * 更新时间
     */
    private String updatetime;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;

}