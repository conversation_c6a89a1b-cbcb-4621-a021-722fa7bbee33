package com.wzsec.clean.modules.service;

import com.wzsec.clean.modules.model.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: PcapService
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/9/3
 */
public interface PcapService {

    HashMap<String, String> selectSensitiveData(String interfaceFlowSign);

    void saveResult(PcapData pcapData);

    void saveTask(HashMap<String, String> map);

    Map<String, Integer> selectResultType();

    List<String> getFileListInfo(String phonedetection, String whitelist);

    Map<String, Integer> getResultTypeById(Integer resyltTypeNum);

    void saveDetailResult(PcapData pcapData);

    void saveFtpResult(FTPData ftpData);

    void saveHttpResult(HTTPResult httpResult);

    void saveHttpResultDetail(HTTPResultDetail httpResultDetail);

    void saveFtpResultDetail(FTPResultDetail ftpData);

    void saveFTPResult(FTPResult ftpResult);

    List<String> getAuthcheckresultApicode();

    void saveAuthcheckresult(ApiAuthcheckresult apiAuthcheckresult);

    void saveIcCallschedule(IcCallschedule icCallschedule);

    void saveIcInterfaceinfo(IcInterfaceInfo icInterfaceInfo);


    void saveIcInterfaceinfoByQinHai(IcInterfaceInfo icInterfaceInfo);

    void saveApidiscovery(Apidiscovery apiApidiscovery);

    /**
     * 数安平台
     *
     * @param apiDiscoverySecurityPlatform apiDiscoverySecurityPlatform
     */
    void saveApiDiscovery_SecurityPlatform(ApiDiscoverySecurityPlatform apiDiscoverySecurityPlatform);

    void saveIcModelconfig(IcModelconfig icModelconfig);

    void update(IcModelconfig icModelconfig);

    void saveOtherRiskDetect(ApiOtherRiskDetect apiOtherRiskDetect);

    String queryInterfaceCoding(String interfaceIdentification);

    int queryNumberInterfaces(String apicode);

    Map<String, String> queryIcModelconfig(String interfaceIdentification);

    String getInterfaceName(String apicode);

    List<String> selectApicode();


    /**
     * 获取接口发现表
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getInterfaceDiscoveryTable();



    /**
     * 获取接口发现表(数安平台)
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getInterfaceDiscoverySecurityPlatform();
}
