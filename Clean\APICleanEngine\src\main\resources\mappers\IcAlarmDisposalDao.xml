<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wzsec.clean.modules.dao.IcAlarmDisposalDao">


    <insert id="saveResult" parameterType="com.wzsec.clean.modules.model.IcAlarmDisposal">
        insert into sdd_ic_alarmdisposal(id, apicode, apiname, detectionmodel, circumstantiality, risk, checktime,
                                         treatmentstate, note, reservefield1, reservefield2, reservefield3,
                                         reservefield4, area, department, incidenthandler, eventhandlingtime, sourceip,
                                         sourceport, destinationip, destinationport, account, eventrule, pushnumber)
        values (#{id}, #{apicode}, #{apiname}, #{detectionmodel}, #{circumstantiality}, #{risk}, #{checktime},
                #{treatmentstate}, #{note}, #{reservefield1}, #{reservefield2}, #{reservefield3},
                #{reservefield4}, #{area}, #{department}, #{incidenthandler}, #{eventhandlingtime}, #{sourceip},
                #{sourceport}, #{destinationip}, #{destinationport}, #{account}, #{eventrule}, #{pushnumber})
    </insert>

</mapper>
