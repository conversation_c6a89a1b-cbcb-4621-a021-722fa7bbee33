package com.wzsec.clean.modules.model;


public class HTTPResultDetail {
    private String id;
    private String seqnumber;//序列号/确认号
    private String interfaceuri;//接口名称
    private String sourceip;//原地址 4字节
    private String sourceport;//原端口
    private String desip;//目的地址
    private String desport;//目的端口
    private String reqmethod;//请求方法
    private String resstatuscode;//响应状态码
    private String resstatus;//响应状态
    private String reqcontent;//请求内容
    private String rescontent;//响应内容
    private String checkrule;//检测规则
    private String resulttype;//结果类型
    private String resulttypecount;//结果类型统计数量
    private String checktotalcount;//检测总数量
    private String ratio;//比例
    private String sensitivedata;//敏感数据及数量
    private String risk;//风险程度
    private String userid;//用户id
    private String username;//用户名称
    private String starttime;//操作时间
    private String endtime;//操作时间
    private String checktime;//检测时间
    private String sparefield1;//备用字段1
    private String sparefield2;//备用字段2
    private String sparefield3;//备用字段3
    private String sparefield4;//备用字段4

    public String getSeqnumber() {
        return seqnumber;
    }

    public void setSeqnumber(String seqnumber) {
        this.seqnumber = seqnumber;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInterfaceuri() {
        return interfaceuri;
    }

    public void setInterfaceuri(String interfaceuri) {
        this.interfaceuri = interfaceuri;
    }

    public String getSourceip() {
        return sourceip;
    }

    public void setSourceip(String sourceip) {
        this.sourceip = sourceip;
    }

    public String getSourceport() {
        return sourceport;
    }

    public void setSourceport(String sourceport) {
        this.sourceport = sourceport;
    }

    public String getDesip() {
        return desip;
    }

    public void setDesip(String desip) {
        this.desip = desip;
    }

    public String getDesport() {
        return desport;
    }

    public void setDesport(String desport) {
        this.desport = desport;
    }

    public String getReqmethod() {
        return reqmethod;
    }

    public void setReqmethod(String reqmethod) {
        this.reqmethod = reqmethod;
    }

    public String getResstatuscode() {
        return resstatuscode;
    }

    public void setResstatuscode(String resstatuscode) {
        this.resstatuscode = resstatuscode;
    }

    public String getResstatus() {
        return resstatus;
    }

    public void setResstatus(String resstatus) {
        this.resstatus = resstatus;
    }

    public String getReqcontent() {
        return reqcontent;
    }

    public void setReqcontent(String reqcontent) {
        this.reqcontent = reqcontent;
    }

    public String getRescontent() {
        return rescontent;
    }

    public void setRescontent(String rescontent) {
        this.rescontent = rescontent;
    }

    public String getCheckrule() {
        return checkrule;
    }

    public void setCheckrule(String checkrule) {
        this.checkrule = checkrule;
    }

    public String getResulttype() {
        return resulttype;
    }

    public void setResulttype(String resulttype) {
        this.resulttype = resulttype;
    }

    public String getResulttypecount() {
        return resulttypecount;
    }

    public void setResulttypecount(String resulttypecount) {
        this.resulttypecount = resulttypecount;
    }

    public String getChecktotalcount() {
        return checktotalcount;
    }

    public void setChecktotalcount(String checktotalcount) {
        this.checktotalcount = checktotalcount;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getSensitivedata() {
        return sensitivedata;
    }

    public void setSensitivedata(String sensitivedata) {
        this.sensitivedata = sensitivedata;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getChecktime() {
        return checktime;
    }

    public void setChecktime(String checktime) {
        this.checktime = checktime;
    }

    public String getSparefield1() {
        return sparefield1;
    }

    public void setSparefield1(String sparefield1) {
        this.sparefield1 = sparefield1;
    }

    public String getSparefield2() {
        return sparefield2;
    }

    public void setSparefield2(String sparefield2) {
        this.sparefield2 = sparefield2;
    }

    public String getSparefield3() {
        return sparefield3;
    }

    public void setSparefield3(String sparefield3) {
        this.sparefield3 = sparefield3;
    }

    public String getSparefield4() {
        return sparefield4;
    }

    public void setSparefield4(String sparefield4) {
        this.sparefield4 = sparefield4;
    }
}
