package com.wzsec.clean.common.rule;

import com.wzsec.clean.common.utils.Const;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * Title: ProCommonRule
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/18
 */
public class ProCommonRule {

    /**
     * 统计敏感数据类型
     *
     * @param sensitivedataMap
     * @param sensitiveDataCountResultMap
     * @param strKey
     * @param datas
     * @param resultTypeSet
     * @return
     */
    public static List<Integer> checkIsClearType(HashMap<String, String> sensitivedataMap, Map<String, Map<String, Integer>> sensitiveDataCountResultMap, String strKey, String[] datas, HashSet<String> resultTypeSet, Map<String, Integer> resTypeMap) {
        List<Integer> resultTypeList = new ArrayList<>();
        //是否包含敏感数据
        boolean containsSensitive = false;
        for (int i = 0; i < datas.length; i++) {
            boolean isSen = false;
            if (sensitivedataMap.containsKey(Const.MOBILENUMBER_SIGN)) {
                if (datas[i].startsWith("1") && datas[i].length() == 11) {
                    if (ProRule.p_checkPhoneNumber(datas[i])) {
                        // 跳过白名单手机号的检测
                        if (!Const.whitePhoneList.contains(datas[i])) {// "明文手机号"
                            isSen = true;
                            containsSensitive = true;
                            resultTypeList.add(resTypeMap.get(Const.PAL_MOBILENUMBER_SIGN));
                            setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_MOBILENUMBER_SIGN, datas[i]);
                            setResultType(resultTypeSet, strKey, Const.PAL_MOBILENUMBER_SIGN);
                            continue;
                        }
                    }
                }
            }
            if (sensitivedataMap.containsKey(Const.IDENTITY_SIGN)) {
                if (datas[i].length() == 18) {
                    if (ProRule.p_checkIdcode(datas[i])) { // "明文身份证号";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.PAL_IDENTITY_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_IDENTITY_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.PAL_IDENTITY_SIGN);
                        continue;
                    }
                }
            }
            if (sensitivedataMap.containsKey(Const.IMSI_SIGN)) {
                if (datas[i].length() == 15) {
                    if (datas[i].startsWith("460")) {
                        if (ProRule.p_checkIMSI(datas[i])) {// "明文IMSI";
                            isSen = true;
                            containsSensitive = true;
                            resultTypeList.add(resTypeMap.get(Const.PAL_IMSI_SIGN));
                            setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_IMSI_SIGN, datas[i]);
                            setResultType(resultTypeSet, strKey, Const.PAL_IMSI_SIGN);
                            continue;
                        }
                    }
                }
            }
            if (sensitivedataMap.containsKey(Const.IMEI_SIGN)) {
                if (datas[i].length() == 15) {
                    if (ProRule.p_checkIMEI(datas[i])) { // "明文IMEI";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.PAL_IMEI_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_IMEI_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.PAL_IMEI_SIGN);
                        continue;
                    }
                }
            }
            if (sensitivedataMap.containsKey(Const.MEID_SIGN)) {
                if (datas[i].length() >= 14 || datas[i].length() <= 16) {
                    if (ProRule.p_checkMEID(datas[i])) {// "明文MEID";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.PAL_MEID_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_MEID_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.PAL_MEID_SIGN);
                        continue;
                    }
                }
            }
            if (sensitivedataMap.containsKey(Const.FIXEDLINE_SIGN)) {
                if (i > 0 && (datas[i].length() == 7 || datas[i].length() == 8)) {
                    if (datas[i - 1].length() == 3 || datas[i - 1].length() == 4) {
                        String ss = datas[i - 1] + "-" + datas[i];
                        if (ProRule.p_checkFixphone(ss)) { // "明文固话";
                            isSen = true;
                            containsSensitive = true;
                            resultTypeList.add(resTypeMap.get(Const.PAL_FIXEDLINE_SIGN));
                            setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_FIXEDLINE_SIGN, datas[i]);
                            setResultType(resultTypeSet, strKey, Const.PAL_FIXEDLINE_SIGN);
                            continue;
                        }
                    }
                }
            }
            if (sensitivedataMap.containsKey(Const.EMAIL_SIGN)) {
                if (datas[i].contains("@") && datas[i].contains(".")) {
                    if (ProRule.p_checkEmail(datas[i])) {// "明文邮箱";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.PAL_EMAIL_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.PAL_EMAIL_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.PAL_FIXEDLINE_SIGN);
                        continue;
                    }
                }
            }
            //省份检测
            if (sensitivedataMap.containsKey(Const.PROVINCE_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkProvinces(datas[i])) {// "明文省份";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_PROVINCE_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_PROVINCE_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_PROVINCE_SIGN);
                        continue;
                    }
                }
            }
            //民族检测
            if (sensitivedataMap.containsKey(Const.NATIONAL_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkNational(datas[i])) {// "明文民族";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_NATIONAL_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_NATIONAL_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_NATIONAL_SIGN);
                        continue;
                    }
                }
            }
            //地址检测
            if (sensitivedataMap.containsKey(Const.ADDRESS_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkAddress(datas[i])) {// "明文地址";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_ADDRESS_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_ADDRESS_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_ADDRESS_SIGN);
                        continue;
                    }
                }
            }
            //姓名检测
//			if (sensitivedataMap.containsKey(Const.NAME_SIGN)) {
//				if (StringUtils.isNotEmpty(datas[i])) {
//					if (p_checkName(datas[i])) {// "明文名称";
//						isSen = true;
//						containsSensitive = true;
//						resultTypeList.add(resTypeMap.get(Const.IC_NAME_SIGN));
//						setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_NAME_SIGN, datas[i]);
//						setResultType(resultTypeSet,strKey, Const.IC_NAME_SIGN);
//                      continue;
//					}
//				}
//			}
            //企业名称检测
            if (sensitivedataMap.containsKey(Const.ENTERPRISENAME_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkEnterpriseName(datas[i])) {// "明文企业名称";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_ENTERPRISENAME_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_ENTERPRISENAME_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_ENTERPRISENAME_SIGN);
                        continue;
                    }
                }
            }
            //QQ号检测
            if (sensitivedataMap.containsKey(Const.QQ_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkQQ(datas[i])) {// "明文QQ号";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_QQ_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_QQ_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_QQ_SIGN);
                        continue;
                    }
                }
            }
            //社会统一信用代码检测
            if (sensitivedataMap.containsKey(Const.UNIFIEDCREDITCODE_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkUnifiedCreditCode(datas[i])) {// "明文社会统一信用代码";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_UNIFIEDCREDITCODE_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_UNIFIEDCREDITCODE_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_UNIFIEDCREDITCODE_SIGN);
                        continue;
                    }
                }
            }
            //营业执照检测
            if (sensitivedataMap.containsKey(Const.BUSINESSLICENSE_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkBusinesslicense(datas[i])) {// "明文营业执照";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_BUSINESSLICENSE_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_BUSINESSLICENSE_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_BUSINESSLICENSE_SIGN);
                        continue;
                    }
                }
            }
            //银行卡号检测
            if (sensitivedataMap.containsKey(Const.BANKCARD_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkBankCard(datas[i])) {// "明文银行卡号";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_BANKCARD_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_BANKCARD_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_BANKCARD_SIGN);
                        continue;
                    }
                }
            }
            //组织机构代码检测
            if (sensitivedataMap.containsKey(Const.VALIDENTPCODE_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkValidEntpCode(datas[i])) {// "明文组织机构代码";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_VALIDENTPCODE_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_VALIDENTPCODE_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_VALIDENTPCODE_SIGN);
                        continue;
                    }
                }
            }
            //税务登记证号码检测
            if (sensitivedataMap.containsKey(Const.TAXATIONNO_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkTaxationNo(datas[i])) {// "明文组税务登记证号码";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_TAXATIONNO_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_TAXATIONNO_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_TAXATIONNO_SIGN);
                        continue;
                    }
                }
            }
            //车牌号检测
            if (sensitivedataMap.containsKey(Const.CARNUMBERNO_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkCarnumberNO(datas[i])) {// "明文车牌号";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_CARNUMBERNO_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_CARNUMBERNO_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_CARNUMBERNO_SIGN);
                        continue;
                    }
                }
            }
            //车辆识别号码检测
            if (sensitivedataMap.containsKey(Const.VIN_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkVIN(datas[i])) {// "明文车辆识别号码";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_VIN_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_VIN_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_VIN_SIGN);
                        continue;
                    }
                }
            }
            //港澳通行证号码检测
            if (sensitivedataMap.containsKey(Const.HMPASSCODE_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkHMPassCheck(datas[i])) {// "明文港澳通行证号码";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_HMPASSCODE_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_HMPASSCODE_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_HMPASSCODE_SIGN);
                        continue;
                    }
                }
            }
            //军官证号码检测
            if (sensitivedataMap.containsKey(Const.OffICERCARD_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkOfficerCard(datas[i])) {// "明文军官证号码";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_OffICERCARD_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_OffICERCARD_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_OffICERCARD_SIGN);
                        continue;
                    }
                }
            }
            //护照号检测
            if (sensitivedataMap.containsKey(Const.PASSPORTCARD_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkPassPortCard(datas[i])) {// "明文护照号";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_PASSPORTCARD_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_PASSPORTCARD_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_PASSPORTCARD_SIGN);
                        continue;
                    }
                }
            }

            //开户许可证检测
            if (sensitivedataMap.containsKey(Const.ACCOUNTOPENINGPERMITNO_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkAccountOpeningPermitNo(datas[i])) {// "明文开户许可证";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_ACCOUNTOPENINGPERMITNO_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_ACCOUNTOPENINGPERMITNO_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_ACCOUNTOPENINGPERMITNO_SIGN);
                        continue;
                    }
                }
            }
            //IPv6检测
            if (sensitivedataMap.containsKey(Const.IPV6_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkIPv6(datas[i])) {// "明文IPv6";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_IPV6_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_IPV6_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_IPV6_SIGN);
                        continue;
                    }
                }
            }
            //IP检测
            if (sensitivedataMap.containsKey(Const.IPV6_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkIP(datas[i])) {// "明文IP";
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.IC_IP_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.IC_IP_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.IC_IP_SIGN);
                        continue;
                    }
                }
            }

            //AES加密串检测
            if (sensitivedataMap.containsKey(Const.AES_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkAES(datas[i])) {
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.AES_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.AES_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.AES_SIGN);
                        continue;
                    }
                }
            }

            //3DES加密串检测
            if (sensitivedataMap.containsKey(Const.DES_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_check3DES(datas[i])) {
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.DES_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.DES_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.DES_SIGN);
                        continue;
                    }
                }
            }

            //MD5加密串检测
            if (sensitivedataMap.containsKey(Const.MD5_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkMD5(datas[i])) {
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.MD5_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.MD5_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.MD5_SIGN);
                        continue;
                    }
                }
            }

            //SHA256加密串检测
            if (sensitivedataMap.containsKey(Const.SHA256_SIGN)) {
                if (StringUtils.isNotEmpty(datas[i])) {
                    if (ProRule.p_checkSHA256(datas[i])) {
                        isSen = true;
                        containsSensitive = true;
                        resultTypeList.add(resTypeMap.get(Const.SHA256_SIGN));
                        setCountResult(sensitiveDataCountResultMap, strKey, Const.SHA256_SIGN, datas[i]);
                        setResultType(resultTypeSet, strKey, Const.SHA256_SIGN);
                        continue;
                    }
                }
            }
            if (!isSen) {
                resultTypeList.add(resTypeMap.get(Const.NORMAL_SIGN));
                setCountResult(sensitiveDataCountResultMap, strKey, Const.NORMAL_SIGN, datas[i]);
            }
        }
        if (!containsSensitive) {
            setResultType(resultTypeSet, strKey, Const.NORMAL_SIGN);
        }
        return resultTypeList;
    }

    /**
     * @param key：敏感数据类型
     * @param value：敏感数据
     * @Description：记录各敏感数据及其出现的次数
     * <AUTHOR>
     * @date 2019年12月16日 下午3:33:31
     */
    private static void setCountResult(Map<String, Map<String, Integer>> sensitiveDataCountResultMap, String strKey,
                                       String key, String value) {
        // MAP数据结构：{"姓名":{"张坤祥":2,"李四":1,"张三":1},"手机号":{"17610631163":2,"17610631162":2}}
        // Map<String, Integer> resultMap =
        // SAASLogCheckTaskXxlController.sensitiveDataCountResultMap.get(strKey+Const.AUDIT_SPLIT_JOIN+key);
        Map<String, Integer> resultMap = sensitiveDataCountResultMap.get(strKey + Const.AUDIT_SPLIT_JOIN + key);
        if (resultMap == null || resultMap.size() == 0) {
            Map<String, Integer> resultMaptemp = new HashMap<>();
            resultMaptemp.put(value, 1);
            sensitiveDataCountResultMap.put(strKey + Const.AUDIT_SPLIT_JOIN + key, resultMaptemp);
        } else {
            Integer count = resultMap.get(value);
            if (count == null || count == 0) {
                count = 1;
            } else {
                count++;
            }
            resultMap.put(value, count);
        }
    }

    /**
     * 校验一行包含所有结果类型并返回类型
     *
     * @param resultTypeSet
     * @param strKey
     * @param key
     */
    private static void setResultType(HashSet<String> resultTypeSet, String strKey, String key) {
        if (resultTypeSet != null) {
            if (resultTypeSet.size() == 0) {
                resultTypeSet.add(strKey + Const.AUDIT_SPLIT_JOIN + key);
            } else {
                resultTypeSet.add(strKey + Const.AUDIT_SPLIT_JOIN + key);
            }
        }
    }
}
