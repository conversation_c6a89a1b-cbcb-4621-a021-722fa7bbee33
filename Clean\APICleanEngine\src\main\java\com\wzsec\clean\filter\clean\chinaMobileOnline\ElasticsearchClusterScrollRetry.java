package com.wzsec.clean.filter.clean.chinaMobileOnline;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.DateUtils;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.IcInterfaceInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Elasticsearch集群滚动模式(HTTP请求实现)
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Slf4j
@Component
public class ElasticsearchClusterScrollRetry {

    private final RestHighLevelClient restHighLevelClient;

    public ElasticsearchClusterScrollRetry(RestHighLevelClient restHighLevelClient) {
        this.restHighLevelClient = restHighLevelClient;
    }

    private static List<String> esNodes; // 集群节点列表
    private static final int BATCH_SIZE = Integer.parseInt(ConfigurationManager.getProperty("netflow.es.scrollsize").trim()); // 每次获取的文档数量
    private static final String SCROLL_TIMEOUT = ConfigurationManager.getProperty("netflow.es.scrolltimeValueMinutes").trim() + "m"; // Scroll 上下文的超时时间(1m)
    private static final String USERNAME = ConfigurationManager.getProperty("netflow.es.username").trim(); // 用户名
    private static final String PASSWORD = ConfigurationManager.getProperty("netflow.es.password").trim(); // 密码
    private static final String HOSTS = ConfigurationManager.getProperty("netflow.es.hostlist").trim();// 集群地址

    /**
     * elasticsearch请求获取数据
     */
    public void elasticsearchRequestsObtainData() {

        // [中移在线]源端索引(aop_busi_yyyy_MM)
        String INDEX_NAME = getSourceIndex();

        String taskStartTime = DateUtils.getNowTime();
        log.info("任务开始时间:" + taskStartTime);

        // TODO 获取接口规范表信息,匹配写入ES
        Map<String, IcInterfaceInfo> icInterfaceInfoMap = ChinaMobileOnlineDatabaseInteraction.queryInterfaceSpecificationInfo();

        // TODO 获取接口发现接口清单
        List<String> interfaceDiscoveryList = ChinaMobileOnlineDatabaseInteraction.queryIcDiscoverApiCode(); //接口发现接口清单
        Map<String, ApiCallNetFlow> cleanApiCallNetFlowMap = new ConcurrentHashMap<>(); //清洗后写ES对象

        // 节点解析
        esNodes = parseNodes(HOSTS);

        // 使用 AtomicReference 保存 scrollId
        AtomicReference<String> scrollIdRef = new AtomicReference<>(null);

        // 获取昨日的时间范围(例如 TRDNG_TIME 字段为字符串)
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startDate = yesterday.atStartOfDay().format(formatter);  // 昨日开始时间
        String endDate = yesterday.atTime(23, 59, 59).format(formatter);  // 昨日结束时间

        // 创建带账号密码的 HTTP 客户端
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(USERNAME, PASSWORD));
        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCredentialsProvider(credentialsProvider)
                .build();

        ObjectMapper objectMapper = new ObjectMapper(); // 用于解析 JSON

        try {
            // 初始化 Scroll 查询
            JsonNode searchResponse = executeWithRetries(httpClient, objectMapper, esNodes, node ->
                    executeSearchQuery(INDEX_NAME, httpClient, objectMapper, node, startDate, endDate));

            if (searchResponse != null) {
                scrollIdRef.set(searchResponse.get("_scroll_id").asText());
                List<JsonNode> hits = parseHits(searchResponse);
                // 循环获取数据
                while (!hits.isEmpty()) {
                    // TODO 处理每批次数据,解析组装
                    processHits(hits, cleanApiCallNetFlowMap, interfaceDiscoveryList, icInterfaceInfoMap, objectMapper);

                    // 使用 Scroll ID 获取下一批数据
                    JsonNode scrollResponse = executeWithRetries(httpClient, objectMapper, esNodes, node ->
                            executeScrollQuery(httpClient, objectMapper, scrollIdRef.get(), node));
                    scrollIdRef.set(scrollResponse.get("_scroll_id").asText());

                    // 处理滚动查询文档
                    hits = parseHits(scrollResponse);
                }
            }
        } catch (IOException e) {
            System.err.println("请求失败：" + e.getMessage());
        } finally {
            // 清除 Scroll 上下文
            if (scrollIdRef.get() != null) {
                try {
                    executeWithRetries(httpClient, objectMapper, esNodes, node -> {
                        clearScroll(httpClient, objectMapper, scrollIdRef.get(), node);
                        return null;
                    });
                } catch (IOException e) {
                    System.err.println("清除 Scroll ID 失败：" + e.getMessage());
                }
            }

            // 关闭 HttpClient
            try {
                httpClient.close();
            } catch (IOException e) {
                System.err.println("关闭 HttpClient 失败：" + e.getMessage());
            }

            cleanApiCallNetFlowMap = null;

            String taskEndTime = DateUtils.getNowTime();
            log.info("任务结束时间:" + taskEndTime);
            long totalUsrTime = DateUtils.getTimeSecondsByBothDate(taskStartTime, taskEndTime);
            log.info("检测任务完成，总耗时:" + totalUsrTime + "s");
        }
    }

    /**
     * 随机尝试多个节点，直到成功或所有节点都失败
     */
    private static <T> T executeWithRetries(CloseableHttpClient httpClient, ObjectMapper objectMapper,
                                            List<String> nodes, NodeAction<T> action) throws IOException {
        List<String> shuffledNodes = new ArrayList<>(nodes);
        Collections.shuffle(shuffledNodes); // 随机打乱节点顺序

        IOException lastException = null;
        for (String node : shuffledNodes) {
            try {
                return action.execute(node);
            } catch (IOException e) {
                System.err.println("节点 " + node + " 请求失败：" + e.getMessage());
                lastException = e;
            }
        }
        throw lastException; // 所有节点尝试失败后抛出异常
    }

    /**
     * 初始化搜索请求，添加时间范围和字段匹配查询
     *
     * @param INDEX_NAME   索引名称
     * @param httpClient   http客户端
     * @param objectMapper 对象映射器
     * @param node         node
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return {@link JsonNode }
     * @throws IOException IOException
     */
    private static JsonNode executeSearchQuery(String INDEX_NAME, CloseableHttpClient httpClient, ObjectMapper objectMapper, String node, String startDate, String endDate) throws IOException {
        String searchUrl = "http://" + node + "/" + INDEX_NAME + "/_search?scroll=" + SCROLL_TIMEOUT;
        // 构建查询请求的 JSON 体
        String requestBody = String.format(
                "{\n" +
                        "  \"size\": %d,\n" +
                        "  \"query\": {\n" +
                        "    \"bool\": {\n" +
                        "      \"must\": [\n" +
                        "        {\n" +
                        "          \"range\": {\n" +
                        "            \"TRDNG_TIME\": {\n" +
                        "              \"gte\": \"%s\",\n" +
                        "              \"lte\": \"%s\"\n" +
                        "            }\n" +
                        "          }\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"terms\": {\n" +
                        "            \"LOG_TYPECD\": [\"1\", \"4\"]\n" +
                        "          }\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    }\n" +
                        "  }\n" +
                        "}",
                BATCH_SIZE, startDate, endDate);

        HttpPost httpPost = new HttpPost(searchUrl);
        httpPost.setEntity(new StringEntity(requestBody, "UTF-8"));
        httpPost.setHeader("Content-Type", "application/json");

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            return objectMapper.readTree(response.getEntity().getContent());
        }
    }

    /**
     * 使用 Scroll ID 获取下一批数据
     */
    private static JsonNode executeScrollQuery(CloseableHttpClient httpClient, ObjectMapper objectMapper, String scrollId, String node) throws IOException {
        String scrollUrl = "http://" + node + "/_search/scroll";

        // 构建滚动查询的 JSON 体
        String requestBody = String.format("{\"scroll\":\"%s\",\"scroll_id\":\"%s\"}", SCROLL_TIMEOUT, scrollId);
        HttpPost httpPost = new HttpPost(scrollUrl);
        httpPost.setEntity(new StringEntity(requestBody, "UTF-8"));
        httpPost.setHeader("Content-Type", "application/json");

        // System.out.println("滚动查询请求URL: " + scrollUrl + " ,scroll_id: " + requestBody);

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            return objectMapper.readTree(response.getEntity().getContent());
        }
    }

    /**
     * 清除 Scroll 上下文
     */
    private static void clearScroll(CloseableHttpClient httpClient, ObjectMapper objectMapper, String scrollId, String node) throws IOException {
        String clearUrl = "http://" + node + "/_search/scroll";
        // 构建清除 Scroll 的 JSON 体
        String requestBody = String.format("{\"scroll_id\":[\"%s\"]}", scrollId);
        HttpPost httpPost = new HttpPost(clearUrl);
        httpPost.setEntity(new StringEntity(requestBody, "UTF-8"));
        httpPost.setHeader("Content-Type", "application/json");

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            objectMapper.readTree(response.getEntity().getContent());
        }
    }

    /**
     * 解析并处理查询结果中的文档
     */
    private static List<JsonNode> parseHits(JsonNode searchResponse) {
        List<JsonNode> hits = new ArrayList<>();
        JsonNode hitsNode = searchResponse.path("hits").path("hits");
        for (JsonNode hit : hitsNode) {
            hits.add(hit);
        }
        return hits;
    }

    /**
     * 处理查询的每一批次数据
     *
     * @param hits                   数据集
     * @param cleanApiCallNetFlowMap 清洗接口对象
     */
    private void processHits(List<JsonNode> hits,
                             Map<String, ApiCallNetFlow> cleanApiCallNetFlowMap,
                             List<String> interfaceDiscoveryList,
                             Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                             ObjectMapper objectMapper) {
        for (JsonNode hit : hits) {
            try {
                // 获取文档 ID
                String docId = hit.get("_id").asText();
                // 获取文档内容 (_source)
                JsonNode sourceNode = hit.path("_source");

                // 解析字段数据
                String abilityId = sourceNode.path("ABILITY_ID").asText(); //能力ID
                String abilityName = sourceNode.path("ABILITY_NAME").asText(); //能力名称
                String abilitySys = sourceNode.path("ABILITY_SYS").asText(); //能力模块ID
                String abilitySysName = sourceNode.path("ABILITY_SYSNAME").asText(); //能力模块名称
                String custId = sourceNode.path("CUST_ID").asText(); //客户ID
                String errorMsgCntt = sourceNode.path("ERROR_MSG_CNTT").asText(); //错误消息
                String logTypecd = sourceNode.path("LOG_TYPECD").asText(); //日志类型
                String monitorType = sourceNode.path("MONITOR_TYPE").asText(); //监控类型
                String msgCntt = sourceNode.path("MSG_CNTT").asText(); //交易报文
                String rtnTransid = sourceNode.path("RTN_TRANSID").asText(); //平台流水号
                String serviceId = sourceNode.path("SERVICE_ID").asText(); //服务ID
                String serviceName = sourceNode.path("SERVICE_NAME").asText(); //服务名称
                Integer srvInvkstsCd = sourceNode.path("SRV_INVKSTSCD").asInt(); //交易状态ID
                String trdngTime = sourceNode.path("TRDNG_TIME").asText(); //交易时间

                // TODO 对于已存在平台流水号的的直接入库
                if (cleanApiCallNetFlowMap.containsKey(rtnTransid)) {

                    ApiCallNetFlow apiCallNetFlow = cleanApiCallNetFlowMap.get(rtnTransid);
                    RequestParser.logTypeRecognitionRequestResponse(apiCallNetFlow, logTypecd, msgCntt);  //请求响应

                    // TODO 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
                    ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
                    eventDTO.setCategory("netflow");
                    apiCallNetFlow.setEvent(eventDTO);

                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = formatter.parse(apiCallNetFlow.getCalltime());
                    int hour = date.getHours();
                    apiCallNetFlow.setReqtimetag(String.valueOf(hour)); //TODO 请求时刻

                    apiCallNetFlow.setRessize((long) apiCallNetFlow.getRescontent().getData().length()); //TODO 接口响应字符数
                    apiCallNetFlow.setAkapicode(apiCallNetFlow.getClientip() + "," + apiCallNetFlow.getApicode()); //TODO 冗余租户ID,接口编码

                    // 分域及CSF编码
                    IcInterfaceInfo icInterfaceInfo = icInterfaceInfoMap.get(abilityId);
                    apiCallNetFlow.setCsfcode(icInterfaceInfo == null ? "" : icInterfaceInfo.getRegister_unit_mozi_org_id());//CSF编码
                    apiCallNetFlow.setDeptid(icInterfaceInfo == null ? "" : icInterfaceInfo.getArea());//组织机构ID
                    apiCallNetFlow.setDeptname(icInterfaceInfo == null ? "" : icInterfaceInfo.getRegister_unit());//组织机构ID

                    apiCallNetFlow.setTimestamp(Instant.now()); //TODO  @timestamp

                    // Jackson序列化错误
                    // objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                    // String jsonString = objectMapper.writeValueAsString(apiCallNetFlow);

                    // 使用Fastjson进行序列化
                    String jsonString = JSON.toJSONString(apiCallNetFlow, SerializerFeature.WriteMapNullValue);

                    // TODO 写入ES
                    chinaMobileOnlineVersion(jsonString);

                    // TODO 新增接口写入接口发现表
                    ChinaMobileOnlineDatabaseInteraction.interfaceDiscovery(apiCallNetFlow, interfaceDiscoveryList);

                    // TODO 清理map已写入ES的数据
                    cleanApiCallNetFlowMap.remove(rtnTransid);
                } else {

                    // 此处进行转换逻辑的具体实现
                    ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();

                    // 日志类型识别请求响应
                    RequestParser.logTypeRecognitionRequestResponse(apiCallNetFlow, logTypecd, msgCntt); //请求响应

                    apiCallNetFlow.setApiname(abilityName); //接口名称
                    apiCallNetFlow.setAbilitymoduleid(abilitySys); // 模块ID
                    apiCallNetFlow.setAbilitymodulename(abilitySysName); // 模块名称
                    apiCallNetFlow.setSeqnum(rtnTransid); //请求标识
                    apiCallNetFlow.setApicode(abilityId); //接口编码
                    apiCallNetFlow.setClientip(custId); //客户端ID
                    apiCallNetFlow.setCalltime(trdngTime); //请求时间
                    apiCallNetFlow.setCleantime(DateUtils.getCurrentTime("yyyy-MM-dd HH:mm:ss")); //清洗时间(当前时间)
                    // 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
                    ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
                    eventDTO.setCategory("netflow");
                    apiCallNetFlow.setEvent(eventDTO);
                    LocalDateTime localDateTime = DateUtils.stringToDate(trdngTime, "yyyy-MM-dd HH:mm:ss");
                    int hour = localDateTime.getHour();
                    apiCallNetFlow.setReqtimetag(String.valueOf(hour)); //时间段标识,用于非工作时段场景检测
                    cleanApiCallNetFlowMap.put(rtnTransid, apiCallNetFlow); //流水号仅对应请求或响应,先放入map
                }
            } catch (Exception e) {
                System.err.println("解析文档失败: " + hit.toString() + "，错误: " + e.getMessage());
            }
        }


    }

    /**
     * 回调接口，供随机节点选择时执行动作
     */
    @FunctionalInterface
    interface NodeAction<T> {
        T execute(String node) throws IOException;
    }


    /**
     * 中移在线写入目标ES
     *
     * @param message 消息
     * @throws IOException IOException
     */
    private void chinaMobileOnlineVersion(String message) {
        try {
            //推送ES索引日期设置为前一天
            IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1))
                    .id(UUID.randomUUID().toString())
                    .source(message, XContentType.JSON);
            //写入es中
            restHighLevelClient.index(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取ES源索引(索引按每月创建yyyyMM)  aop_busi_202411
     *
     * @return {@link String }
     */
    static String getSourceIndex() {
        //pcap文件清洗处理模式
        String sourceIndex = ConfigurationManager.getProperty("netflow.es.index.prefix").trim(); //源端索引前缀
        String pcapDate = "";
        // 指定日期清洗
        pcapDate = ConfigurationManager.getProperty("netflow.checkdate").trim();
        boolean validDate = DateUtils.isValidDateFormat(pcapDate, "yyyyMM");
        if (!validDate) {
            //默认检测前一天
            pcapDate = TimeUtils.getNextMonth(new Date(),
                    Integer.parseInt(ConfigurationManager.getProperty("netflow.checkintervalday").trim()));
        }
        return sourceIndex + pcapDate;
    }


    /**
     * 解析逗号分隔的节点字符串为 List<String>
     *
     * @param nodesInput 节点输入
     * @return {@link List }<{@link String }>
     */
    private static List<String> parseNodes(String nodesInput) {
        String[] nodesArray = nodesInput.split(",");
        List<String> nodesList = new ArrayList<>();
        for (String node : nodesArray) {
            nodesList.add(node.trim()); // 去除多余的空格
        }
        return nodesList;
    }


}
