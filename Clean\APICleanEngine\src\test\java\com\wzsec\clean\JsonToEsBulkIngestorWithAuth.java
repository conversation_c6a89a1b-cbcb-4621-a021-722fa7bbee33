package com.wzsec.clean;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

public class JsonToEsBulkIngestorWithAuth {

    private static final String INDEX_NAME = "netflow-2025.01.01";
    private static final String USERNAME = "elastic";
    private static final String PASSWORD = "wzsec@2022";

    public static void main(String[] args) {
        String filePath = "E:\\Work\\33.测试数据\\eslog.txt";
        insertJsonLinesToEs(filePath);
    }

    /**
     * 将JSON Lines文件中的每一行插入到Elasticsearch中。
     *
     * @param filePath JSON Lines文件的路径
     */
    public static void insertJsonLinesToEs(String filePath) {
        RestHighLevelClient client = null;
        BufferedReader reader = null;

        try {
            // 配置Elasticsearch客户端
            RestClientBuilder builder = RestClient.builder(
                    new HttpHost("**************", 9200, "http"));

            if (USERNAME != null && PASSWORD != null) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(
                        AuthScope.ANY,
                        new UsernamePasswordCredentials(USERNAME, PASSWORD)
                );
                builder.setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
            }

            client = new RestHighLevelClient(builder);

            // 使用BufferedReader逐行读取文件
            reader = new BufferedReader(new FileReader(filePath));
            String line;
            BulkRequest bulkRequest = new BulkRequest();
            int batchSize = 1000; // 每批处理1000条，可以根据需要调整

            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue; // 跳过空行
                }
                IndexRequest indexRequest = new IndexRequest(INDEX_NAME)
                        .source(line, XContentType.JSON);
                bulkRequest.add(indexRequest);

                if (bulkRequest.numberOfActions() >= batchSize) {
                    executeBulk(client, bulkRequest);
                    bulkRequest = new BulkRequest(); // 重置批量请求
                }
            }

            // 处理剩余的文档
            if (bulkRequest.numberOfActions() > 0) {
                executeBulk(client, bulkRequest);
            }

            System.out.println("成功插入 " + countLines(filePath) + " 条数据到索引 " + INDEX_NAME);

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (reader != null) reader.close();
                if (client != null) client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 执行批量请求并处理响应。
     *
     * @param client      Elasticsearch高阶客户端
     * @param bulkRequest 批量请求
     */
    private static void executeBulk(RestHighLevelClient client, BulkRequest bulkRequest) {
        try {
            BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                System.err.println("批量插入失败: " + bulkResponse.buildFailureMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 计算文件的总行数（用于显示成功插入的数量）。
     *
     * @param filePath 文件路径
     * @return 文件的总行数
     * @throws IOException 如果读取文件时发生错误
     */
    private static long countLines(String filePath) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            return reader.lines().count();
        }
    }
}