package com.wzsec.clean.filter.clean.HainanBigData;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.analysis.PacketParser;
import com.wzsec.clean.modules.model.Packet;
import com.wzsec.clean.modules.model.Pcap;
import com.wzsec.clean.modules.model.PcapHeader;
import com.wzsec.clean.modules.model.PcapTask;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 海南流量清洗独立场景【索引为:csbflow-yyyy.MM.dd】
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
@Slf4j
public class PcapMessageByHaiNan {

    public static ConcurrentHashMap<String, JSONObject> dataByHaiNanMap = new ConcurrentHashMap<>();
    public static ConcurrentHashMap<String, JSONObject> httpJsonByHaiNanMap = new ConcurrentHashMap<>();

    private static int threadNum = 2;// 默认开启线程数量

    static {
        Integer temp = threadNum = ConfigurationManager.getInteger("CleanPcapThreadNum");
        if (temp != null) {
            threadNum = temp;
        }
    }


    /**
     * 海南流量清洗独立场景【索引为:csbflow-yyyy.MM.dd】 (时间,接口编码,XFF,请求头,请求体,响应头,响应体)
     *
     * @param fielDir            事业dir
     * @param filename           文件名
     * @param restoreFilePath    恢复文件路径
     * @param ftpRestoreFilePath ftp恢复文件路径
     * @param pcapTask           pcap任务
     * @throws IOException ioexception
     */
    public static void parserByHaiNan(String fielDir, String filename, String restoreFilePath,
                                      String ftpRestoreFilePath, PcapTask pcapTask) throws IOException {

        FileInputStream is = new FileInputStream(fielDir);
        Pcap pcap = null;
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        pcap = new Pcap();
        PcapHeader header = new PcapHeader();
        int m = is.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        header.setMagic(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_2);
        reverseByteArray(buffer_2);
        header.setMagor_version(byteArrayToShort(buffer_2, 0));
        m = is.read(buffer_2);
        reverseByteArray(buffer_2);
        header.setMinor_version(byteArrayToShort(buffer_2, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setTimezone(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setSigflags(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setSnaplen(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setLinktype(byteArrayToInt(buffer_4, 0));
        pcap.setHeader(header);


        while (m > 0) {
            Packet data = new Packet();

            // TODO 时间戳（秒）
            m = is.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            //TODO 时间戳（微妙）
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            // TODO 抓包长度
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            //TODO 实际长度
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));
            byte[] content = new byte[data.getpLength()];


            m = is.read(content);
            //TCP协议
            //TODO 源端口
            StringBuilder sbd = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 34;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                sbd.append(appendZero(hexStr));
            }
            //Integer souport = Integer.valueOf(sbd.toString(), 16);
            //data.setSourceport(String.valueOf(souport));


            //TODO 目标端口
            StringBuilder sbe = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 36;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                sbe.append(appendZero(hexStr));
            }
            //Integer desport = Integer.valueOf(sbe.toString(), 16);
            //data.setDesport(String.valueOf(desport));

            // TODO 序号
            StringBuilder sbf = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 38;
                sbf.append(content[b] & 0xff);
            }
            sbf.deleteCharAt(sbf.length() - 1);
            //data.setSeq_number(sbf.toString());

            //TODO 确认号
            StringBuilder sbg = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 42;
                sbg.append(content[b] & 0xff);
            }
            sbg.deleteCharAt(sbg.length() - 1);
            //data.setAck_number(sbg.toString());
            //data.setContent_byte(content);

            String strContent = content2Str(content, "UTF-8");
            data.setContent(strContent);

            // TODO 清洗HTTP相关
            cleanHttpFlowMessage(data);
        }

        is.close();
    }


    /**
     * TODO  new 海南流量清洗场景
     *
     * @param data 数据
     */
    public static void cleanHttpFlowMessageNew(Packet data) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        if (strContent.contains(Const.HTTP_SIGN)) {
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");
            boolean type = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.contains("Accept") && !s.contains("Accept-")) {
                    type = true;
                    continue;
                }
                // 判断响应头
                if (s.contains("Content-Type")) {
                    type = true;
                    continue;
                }
            }

            if (type) {

                String responseHeader = ""; //响应头

                String[] s1 = strContent.split("GET|POST|PUT|DELETE");
                if (s1.length == 2) {
                    String requestId = ""; //请求编码
                    String requestTime = ""; //请求时间
                    String apicode = ""; //接口编码
                    String xff = ""; //代理路由
                    String requestHeader = ""; //请求头

                    //TODO 新增字段
                    String desip = "";
                    String sourceip = "";
                    String reqcontent = "";
                    String interfaceuri = "";
                    String rescontent = "";
                    String code = "";
                    String apiname = "";
                    String ak = "";


                    String[] s = strContent.split("\r\n\r\n")[0].split("\r\n");

                    for (int i = 0; i < s.length; i++) {

                        // 请求标识
                        if (s[i].trim().contains("x-request-id")) {
                            requestId = s[i].trim().substring(("x-request-id").length() + 1).trim();
                        }

                        // 请求时间
                        if (s[i].trim().contains("hnjhpt_rtime")) {
                            requestTime = s[i].trim().substring(("hnjhpt_rtime").length() + 1).trim();
                        }

                        // apicode
                        if (s[i].trim().contains("_api_name")) {
                            apicode = s[i].trim().substring(("_api_name").length() + 1).trim();
                        }

                        // apiname
                        if (s[i].trim().contains("hnjhpt_sid")) {
                            apiname = s[i].trim().substring(("hnjhpt_sid").length() + 1).trim();
                        }

                        //ak标识
                        if (s[i].trim().contains("_api_access_key")) {
                            ak = s[i].trim().substring(("_api_access_key").length() + 1).trim();
                        }

                        // xff
                        if (s[i].trim().contains("x-forwarded-for") || s[i].trim().contains("X-Forwarded-For")) {
                            //路由流转
                            if (s[i].trim().contains("x-forwarded-for")) {
                                xff = s[i].trim().substring(("x-forwarded-for").length() + 1).trim();
                            } else {
                                xff = s[i].trim().substring(("X-Forwarded-For").length() + 1).trim();
                            }
                        }

                        // 客户端及服务端IP
                        if (s[i].trim().contains("X-Real-IP") || s[i].trim().contains("x-real-ip")) {
                            //客户端IP
                            desip = s[i].trim().substring(("X-Real-IP").length() + 1).trim();
                        }
                        if (s[i].trim().contains("x-forwarded-for") || s[i].trim().contains("X-Forwarded-For")) {
                            //服务端IP
                            String XFF = s[i].trim().substring(("x-forwarded-for").length() + 1).trim();

                            try {
                                sourceip = XFF.substring(0, xff.indexOf(","));
                            } catch (Exception e) {
                                sourceip = XFF;
                            }
                        }


                    }

                    String requestBody = "";
                    requestHeader = StringUtils.join(dataArr, "\r\n");
                    if (dataArr.length > 1) {
                        try {
                            requestBody = strContent.split("\r\n\r\n")[1];
                        } catch (Exception e) {
                            requestBody = "";
                        }
                    }


                    String[] split1 = strContent.split("GET|POST|PUT|DELETE");
                    if (split1.length == 2) {
                        String[] http = split1[1].split("HTTP");
                        // 接口路径
                        interfaceuri = http[0].trim();
                    }

                    //TODO 请求体
                    joInfo.put("apicode", apicode);
                    joInfo.put("xff", xff);
                    joInfo.put("requestTime", requestTime);
                    joInfo.put("requestHeader", requestHeader);
                    joInfo.put("requestBody", requestBody);

                    //TODO  新增字段
                    joInfo.put("apiname", apiname);
                    joInfo.put("starttime", requestTime);
                    joInfo.put("desip", desip);
                    joInfo.put("sourceip", sourceip);
                    joInfo.put("reqcontent", requestBody);
                    joInfo.put("interfaceuri", URLUtil.decode(interfaceuri));
                    joInfo.put("ak", ak);

                    dataByHaiNanMap.put(requestId, joInfo);

                } else {

                    // 响应第一段
                    if (strContent.contains(Const.HTTP_SIGN)) {
                        String restId = "";
                        //状态码
                        String statuscode = "200";

                        String[] s2 = strContent.split("\r\n\r\n")[0].split("\r\n");
                        for (int i = 0; i < s2.length; i++) {
                            if (s2[i].trim().contains("x-request-id")) {
                                restId = s2[i].trim().substring(("x-request-id").length() + 1).trim();
                            }

                            if (s2[i].trim().contains(Const.HTTP_SIGN)) {
                                String[] arr = s2[i].split("\\s+");
                                int index = 0;
                                for (int j = 0; j < arr.length; j++) {
                                    if (arr[j].contains(Const.HTTP_SIGN)) {
                                        index = j + 1;
                                    }
                                }
                                try {
                                    statuscode = arr[index];
                                } catch (Exception e) {
                                    statuscode = "";
                                }
                            }

                        }

                        //根据响应的seq_number获取请求信息
                        if (dataByHaiNanMap.containsKey(restId)) {
                            //responseHeader = strContent.split("\r\n")[0].split(" ")[1];

                            responseHeader = StringUtils.join(dataArr, "\r\n");
                            String responseBody = "";
                            if (dataArr.length > 1) {
                                try {
                                    responseBody = strContent.split("\r\n\r\n")[1];
                                } catch (Exception e) {
                                    responseBody = "";
                                }
                            }

                            //String requestheader = (String) dataByHaiNanMap.get(restId).get("requestHeader");
                            //String requsetbody = (String) dataByHaiNanMap.get(restId).get("requestBody");
                            //String requestTime = (String) dataByHaiNanMap.get(restId).get("requestTime");
                            String apicode = (String) dataByHaiNanMap.get(restId).get("apicode");
                            String xff = (String) dataByHaiNanMap.get(restId).get("xff");
                            joType.put("seqnum", restId);
                            //joType.put("time", requestTime);
                            joType.put("apicode", apicode);
                            joType.put("responsebody", responseBody);

                            //TODO  新增字段
                            String apiname = (String) dataByHaiNanMap.get(restId).get("apiname");
                            String starttime = (String) dataByHaiNanMap.get(restId).get("starttime");
                            String desip = (String) dataByHaiNanMap.get(restId).get("desip");
                            String sourceip = (String) dataByHaiNanMap.get(restId).get("sourceip");
                            String reqcontent = (String) dataByHaiNanMap.get(restId).get("reqcontent");
                            String interfaceuri = (String) dataByHaiNanMap.get(restId).get("interfaceuri");
                            String ak = (String) dataByHaiNanMap.get(restId).get("ak");
                            joType.put("apiname", apiname);
                            joType.put("starttime", data.getTime_s());
                            joType.put("desip", desip);
                            joType.put("sourceip", sourceip);
                            joType.put("reqcontent", reqcontent);
                            joType.put("interfaceuri", interfaceuri);
                            joType.put("ak", ak);

                            if (responseBody.startsWith("{") && !responseBody.endsWith("}")) {
                                int endLocation = responseBody.lastIndexOf("}") + 1;
                                responseBody = responseBody.substring(0, endLocation);
                            }

                            joType.put("rescontent", responseBody);  //响应参数
                            joType.put("statuscode", statuscode);

                            httpJsonByHaiNanMap.put(restId, joType);
                            dataByHaiNanMap.remove(restId);
                        }
                    }
                }
            }
        }
    }


    /**
     * TODO  海南流量清洗独立场景【索引为:csbflow-yyyy.MM.dd】 (时间,接口编码,XFF,请求头,请求体,响应头,响应体)
     *
     * @param data 数据
     */
    public static void cleanHttpFlowMessage(Packet data) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        // 响应第一段、文件传输响应多部分、接口传输响应多部分
        if (strContent.contains(Const.HTTP_SIGN)) {

            // '\r\n\r\n'截取请求头
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");

            boolean type = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.contains("Accept") && !s.contains("Accept-")) {
                    type = true;
                    continue;
                }

                // 判断响应头
                if (s.contains("Content-Type")) {
                    type = true;
                    continue;
                }
            }

            if (type) {

                String responseHeader = ""; //响应头

                String[] s1 = strContent.split("GET|POST|PUT|DELETE");
                if (s1.length == 2) {
                    String requestId = ""; //请求编码
                    String requestTime = ""; //请求时间
                    String apicode = ""; //接口编码
                    String xff = ""; //代理路由
                    String requestHeader = ""; //请求头

                    String[] s = strContent.split("\r\n\r\n")[0].split("\r\n");

                    for (int i = 0; i < s.length; i++) {

                        if (s[i].trim().contains("x-request-id")) {
                            requestId = s[i].trim().substring(("x-request-id").length() + 1).trim();
                        }

                        if (s[i].trim().contains("hnjhpt_rtime")) {
                            requestTime = s[i].trim().substring(("hnjhpt_rtime").length() + 1).trim();
                        }

                        if (s[i].trim().contains("_api_name")) {
                            apicode = s[i].trim().substring(("_api_name").length() + 1).trim();
                        }

                        if (s[i].trim().contains("x-forwarded-for") || s[i].trim().contains("X-Forwarded-For")) {
                            //路由流转
                            if (s[i].trim().contains("x-forwarded-for")) {
                                xff = s[i].trim().substring(("x-forwarded-for").length() + 1).trim();
                            } else {
                                xff = s[i].trim().substring(("X-Forwarded-For").length() + 1).trim();
                            }
                        }
                    }

                    String requestBody = "";
                    requestHeader = StringUtils.join(dataArr, "\r\n");
                    if (dataArr.length > 1) {
                        try {
                            requestBody = strContent.split("\r\n\r\n")[1];
                        } catch (Exception e) {
                            requestBody = "";
                        }
                    }

                    //请求
                    joInfo.put("apicode", apicode);
                    joInfo.put("xff", xff);
                    joInfo.put("requestTime", requestTime);
                    joInfo.put("requestHeader", requestHeader);
                    joInfo.put("requestBody", requestBody);
                    dataByHaiNanMap.put(requestId, joInfo);

                } else {

                    // 响应第一段
                    if (strContent.contains(Const.HTTP_SIGN)) {
                        String restId = "";
                        String[] s2 = strContent.split("\r\n\r\n")[0].split("\r\n");
                        for (int i = 0; i < s2.length; i++) {
                            if (s2[i].trim().contains("x-request-id")) {
                                restId = s2[i].trim().substring(("x-request-id").length() + 1).trim();
                            }
                        }

                        //根据响应的seq_number获取请求信息
                        if (dataByHaiNanMap.containsKey(restId)) {
                            //responseHeader = strContent.split("\r\n")[0].split(" ")[1];

                            responseHeader = StringUtils.join(dataArr, "\r\n");
                            String responseBody = "";
                            if (dataArr.length > 1) {
                                try {
                                    responseBody = strContent.split("\r\n\r\n")[1];
                                } catch (Exception e) {
                                    responseBody = "";
                                }
                            }

                            String requestheader = (String) dataByHaiNanMap.get(restId).get("requestHeader");
                            String requsetbody = (String) dataByHaiNanMap.get(restId).get("requestBody");
                            String requestTime = (String) dataByHaiNanMap.get(restId).get("requestTime");
                            String apicode = (String) dataByHaiNanMap.get(restId).get("apicode");
                            String xff = (String) dataByHaiNanMap.get(restId).get("xff");

                            joType.put("seqnum", restId);
                            joType.put("time", requestTime);
                            joType.put("apicode", apicode);
                            joType.put("forwarded", xff);
                            joType.put("requestheader", requestheader);
                            joType.put("requsetbody", requsetbody);
                            joType.put("responseheader", responseHeader);
                            joType.put("responsebody", responseBody);
                            httpJsonByHaiNanMap.put(restId, joType);
                            //System.out.println(joType);
                            dataByHaiNanMap.remove(restId);
                        }
                    }
                }
            }
        }
    }


    private static void reverseByteArray(byte[] arr) {
        byte temp;
        int n = arr.length;
        for (int i = 0; i < n / 2; i++) {
            temp = arr[i];
            arr[i] = arr[n - 1 - i];
            arr[n - 1 - i] = temp;
        }
    }

    private static int byteArrayToInt(byte[] b, int offset) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }


    private static short byteArrayToShort(byte[] b, int offset) {
        short value = 0;
        for (int i = 0; i < 2; i++) {
            int shift = (2 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }


    /**
     * 字节数据转字符串处理中文乱码
     *
     * @param content
     * @param encode
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String content2Str(byte[] content, String encode) throws UnsupportedEncodingException {
        return new String(content, encode);
    }


    private static String appendZero(String str) {
        if (str.length() == 1) {
            return "0" + str;
        } else {
            return str;
        }
    }


    /**
     * 变量销毁
     */
    public static void destruction() {
        PacketParser.dataMap = new HashMap<>();
        PacketParser.ftpUserMap = new HashMap<>();
        PacketParser.ftpPortMap = new HashMap<>();
        PacketParser.ftpDataTransferMap = new HashMap<>();
        PacketParser.ftpTransferMap = new HashMap<>();
        PacketParser.ftpResultMap = new HashMap<>();
        PacketParser.filetransferMap = new HashMap<>();
        PacketParser.transferByteMap = new HashMap<>();
        PacketParser.realityByteMap = new HashMap<>();
        PacketParser.fileByteMap = new HashMap<>();
        PacketParser.interfaceTransferMap = new HashMap<>();
    }


}
