#!/bin/bash
#备注：-eq //equals等于; -ne //no equals不等于
#接口审计系统停止脚本

#1.检查ds_bdas_system服务是否正常
# 需改动地方: ds_bdas_system-2.4.1.jar , 已实际jar名调整
bdasSystemName=ds_bdas_system-2.4.1.jar
bdasSystemProID=`ps -ef | grep "$bdasSystemName" | grep -v "$0" | grep -v "grep" | awk '{print $2}'`
kill -9 $bdasSystemProID
sleep 5s
bdasSystemProCount=`ps -ef |grep "$bdasSystemName" |grep -v "grep" |wc -l`
if [ 0 != $bdasSystemProCount ]
then
  kill -9 $bdasSystemProID
else
  echo "接口审计系统${bdasSystemName}程序停止运行"
fi

#2.检查ds_bdas_engine服务是否正常
# 需改动地方: ds_bdas_engine-2.4.1.jar , 已实际jar名调整
bdasEngineName=ds_bdas_engine-2.4.1.jar
bdasEngineProID=`ps -ef | grep "$bdasEngineName" | grep -v "$0" | grep -v "grep" | awk '{print $2}'`
kill -9	$bdasEngineProID
sleep 5s
bdasEngineProCount=`ps -ef |grep "$bdasEngineName" |grep -v "grep" |wc -l`
if [ 0 != $bdasEngineProCount ]
then
  kill -9 $bdasEngineProCount
else
  echo "接口审计系统${bdasEngineName}程序停止运行"
fi
