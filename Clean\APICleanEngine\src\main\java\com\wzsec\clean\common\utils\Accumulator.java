package com.wzsec.clean.common.utils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 使用AtomicInteger保证原子性操作
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
public class Accumulator {
    private AtomicInteger value = new AtomicInteger(0);

    public void reset() {
        value.set(0);
    }

    public void addToValue(int amount) {
        value.addAndGet(amount);
    }

    public int getValue() {
        return value.get();
    }
}

