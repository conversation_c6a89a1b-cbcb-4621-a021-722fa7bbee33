package com.wzsec.clean.common.quzrtz.config;

import lombok.Data;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 创建ES连接对象配置
 *
 * <AUTHOR>
 * @date 2022/01/30
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "elasticsearch")
public class ElasticSearchConfig {

    private String hostlist;

    private String username;

    private String password;


    @Bean
    public RestHighLevelClient client() {
        try {
            long t = System.currentTimeMillis();
            /** 用户认证对象 */
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            /** 设置账号密码 */
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
            /** 创建rest client对象 */
            HttpHost[] httpHosts = Arrays.stream(hostlist.split(",")).map(x -> {
                String[] hostInfo = x.split(":");
                return new HttpHost(hostInfo[0], Integer.parseInt(hostInfo[1]));
            }).toArray(HttpHost[]::new);

            RestClientBuilder builder = RestClient.builder(httpHosts)
                    .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                        @Override
                        public HttpAsyncClientBuilder customizeHttpClient(
                                HttpAsyncClientBuilder httpClientBuilder) {
                            return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                        }
                    });
            RestHighLevelClient client = new RestHighLevelClient(builder);
            long t1 = System.currentTimeMillis();
            System.out.println("获得连接，耗时:" + (t1 - t) + "s");
            return client;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
