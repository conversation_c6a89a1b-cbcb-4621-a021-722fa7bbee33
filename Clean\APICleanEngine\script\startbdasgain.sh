#!/bin/bash
#备注：-eq //equals等于; -ne //no equals不等于
#接口审计采集清洗启动脚本

#1.检查 CleanEngine 服务是否正常
# 需改动地方: CleanEngine-2.4.1.jar , 以实际jar名调整
bdasCleanEngineName=CleanEngine-1.2.1.jar
# 需改动地方: 服务所在路径, /data/zhengh/ds_bdas/cleanEngine/ , 已实际jar包所在路径调整
bdasCleanEngineDir=/data/zhengh/ds_bdas/cleanEngine/
bdasCleanEngineProCount=`ps -ef |grep "$bdasCleanEngineName" |grep -v "grep" |wc -l`
if [ $bdasCleanEngineProCount -eq 0 ]
then
  nohup java -Xms2g -Xmx4g -XX:NewRatio=1 -XX:SurvivorRatio=8 -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -jar $bdasCleanEngineDir/$bdasCleanEngineName > nohup.out 2>&1 &
  sleep 20s
  bdasCleanEngineProNewCount=`ps -ef |grep "$bdasCleanEngineName" |grep -v "grep" |wc -l`
  if [ $bdasCleanEngineProNewCount  -eq  1 ];then
        echo "接口审计系统${bdasCleanEngineName}程序成功启动"
   fi
else
  echo "接口审计系统${bdasCleanEngineName}进程存在，已在运行"
fi

#2.检查 CollectTool 服务是否正常
# 需改动地方: CollectTool-0.0.1-SNAPSHOT.jar , 以实际jar名调整
bdasCollectToolName=CollectTool-0.0.1-SNAPSHOT.jar
# 需改动地方: 服务所在路径, /data/zhengh/collectTool/ , 已实际jar包所在路径调整
bdasCollectToolDir=/data/zhengh/collectTool/
bdasCollectToolProCount=`ps -ef |grep "$bdasCollectToolName" |grep -v "grep" |wc -l`
if [ $bdasCollectToolProCount -eq 0 ]
then
  nohup java -jar $bdasCollectToolDir/$bdasCollectToolName > nohup.out 2>&1 &
  sleep 20s
  bdasCollectToolProNewCount=`ps -ef |grep "$bdasCollectToolName" |grep -v "grep" |wc -l`
  if [ $bdasCollectToolProNewCount  -eq  1 ];then
        echo "接口审计系统${bdasCollectToolName}程序成功启动"
   fi
else
  echo "接口审计系统${bdasCollectToolName}进程存在，已在运行"
fi