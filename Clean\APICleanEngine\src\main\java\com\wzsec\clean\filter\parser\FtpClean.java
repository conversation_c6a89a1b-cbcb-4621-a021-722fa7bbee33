package com.wzsec.clean.filter.parser;

import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.common.analysis.PacketParser;
import com.wzsec.clean.modules.model.FTPResultDetail;
import com.wzsec.clean.modules.model.Packet;
import com.wzsec.clean.common.analysis.PcapParser_v3;
import org.apache.commons.lang3.StringUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Set;

import static com.wzsec.clean.common.analysis.PacketParser.*;
import static com.wzsec.clean.common.analysis.PcapParser_v3.ftpList;

public class FtpClean {


    /**
     * @description FTP解析pcap文件
     * <AUTHOR>
     * @date 2020-11-02
     */
    public static void parser(String fielDir) throws IOException {

        FileInputStream fis = new FileInputStream(fielDir);
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        int m = fis.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);

        while (m > 0) {
            Packet data = new Packet();

            m = fis.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));


            byte[] content = new byte[data.getpLength()];
            m = fis.read(content);

            //截取有效负载payload 文本内容从66位开始
            if (content.length>66){
                byte[] contentbyte = new byte[content.length-66];
                System.arraycopy(content, 66, contentbyte, 0, content.length-66);
                data.setContent_byte(contentbyte);
            }else {
                data.setContent_byte(content);
            }



            byte[] ver_ihla = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 14;
                ver_ihla[0] = content[b];
            }

            byte[] pro = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 23;
                pro[i] = content[b];
            }

            StringBuilder sbr = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 26;
                sbr.append((int) (content[b] & 0xff));
                sbr.append(".");
            }
            sbr.deleteCharAt(sbr.length() - 1);
            data.setSourceip(sbr.toString());

            StringBuilder sba = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 30;
                sba.append((int) (content[b] & 0xff));
                sba.append(".");
            }
            sba.deleteCharAt(sba.length() - 1);
            data.setDesip(sba.toString());

            if ((short) ver_ihla[0] == 69) {
                if ((short) pro[0] == 6) {
                    //TCP协议
                    StringBuilder sbd=new StringBuilder();
                    for(int i=0;i<2;i++){
                        int b=i+34;
                        sbd.append(Integer.toHexString(content[b] & 0xff));
                    }
                    Integer souport = Integer.valueOf(sbd.toString(), 16);
                    data.setSourceport(String.valueOf(souport));

                    StringBuilder sbe=new StringBuilder();
                    for(int i=0;i<2;i++){
                        int b=i+36;
                        sbe.append(Integer.toHexString(content[b] & 0xff));
                    }
                    Integer desport = Integer.valueOf(sbe.toString(), 16);
                    data.setDesport(String.valueOf(desport));

                    StringBuilder sbf = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 38;
                        sbf.append((int) (content[b] & 0xff));
                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    data.setSeq_number(sbf.toString());


                    StringBuilder sbg = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 42;
                        sbg.append((int) (content[b] & 0xff));
                    }
                    sbg.deleteCharAt(sbg.length() - 1);
                    data.setAck_number(sbg.toString());

                    //数据包转文本内容
                    String strContent = content2Str(data.getContent_byte(),"UTF-8");

                    //分析响应内容并保存List FTP
                    saveDataToListForFTP(data, strContent);
                }
            }

        }
        fis.close();
    }



    /**
     * 分析解析数据包内容保存List FTP
     *
     * @param strContent 数据
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    public static void saveDataToListForFTP(Packet data, String strContent) {
        strContent = strContent.replaceAll("\r\n", "");
        FTPResultDetail ftpData = new FTPResultDetail();

        //FTP 协议
        String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();
        //拆分数据传输的内容
        if (ftpDataTransferMap.containsKey(key)) {
            if (data.getContent_byte().length > 66) {//过滤字节小于66位的数据
                String ftpTransferKey = ftpDataTransferMap.get(key);
                int bytes = data.getContent_byte().length;
                String[] dataStr = strContent.split(",|:|\"|\\{|}|\\[|]|\t|\r|\n|\r\n");
                ArrayList<String> list = PacketParser.convertArrToList(dataStr);
                ftpList.addAll(list);

                //计算文件大小
                if (ftpResultMap.containsKey(ftpTransferKey)) {
                    FTPResultDetail ftpResultDetail = ftpResultMap.get(ftpTransferKey);
                    if (StringUtils.isNotEmpty(ftpResultDetail.getFilesize())) {
                        String filesize = ftpResultDetail.getFilesize();
                        long size = Long.valueOf(filesize).longValue() + (long) bytes;
                        ftpResultDetail.setFilesize(String.valueOf(size));
                        ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                    } else {
                        ftpResultDetail.setFilesize(String.valueOf(bytes));
                        ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                    }
                }
            }
        }
        //保存数据传输的基本信息
        if (!ftpTransferMap.containsKey(key)) {
            ftpData.setSourceip(data.getSourceip());
            ftpData.setSourceport(data.getSourceport());
            ftpData.setDesip(data.getDesip());
            ftpData.setDesport(data.getDesport());
            ftpTransferMap.put(key, ftpData);
        }
        //截取4位去空格，FTP请求指令是4位，响应码是3位
        String code = "";
        if (strContent.length() > 4) {
            code = strContent.substring(0, 4).trim();
        }

        if (code.length() == 3) {
            switch (code) {
                case Const.FTP_CWD:
                    String path = strContent.split(Const.FTP_CWD)[1].trim();
                    FTPResultDetail ftpResultDetail = ftpTransferMap.get(key);
                    ftpResultDetail.setFilepath(path);
//					ftpTransferMap.put(key,ftpResultDetail);
                    break;
                case Const.FTP_226_TRANSFER_COMPLETE:
                    //传输完成
                    Set<String> ftpResultKeyMap = ftpResultMap.keySet();
                    for (String reskey : ftpResultKeyMap) {
                        FTPResultDetail ftpEntity = new FTPResultDetail();
                        FTPResultDetail ftpResult = ftpResultMap.get(reskey);
                        ftpEntity.setSourceip(ftpResult.getSourceip());
                        ftpEntity.setSourceport(ftpResult.getSourceport());
                        ftpEntity.setDesip(ftpResult.getDesip());
                        ftpEntity.setDesport(ftpResult.getDesport());
                        ftpEntity.setOperationtime(ftpResult.getOperationtime());
                        ftpEntity.setUsername(ftpResult.getUsername());
                        ftpEntity.setPassword(ftpResult.getPassword());
                        ftpEntity.setFilename(ftpResult.getFilename());
                        ftpEntity.setFilepath(ftpResult.getFilepath());
                        ftpEntity.setFilesize(ftpResult.getFilesize());
                        ftpEntity.setSign(ftpResult.getSign());
                        PcapParser_v3.ftpListMap.put(reskey, ftpList);
                        PcapParser_v3.ftpEntityMap.put(reskey, ftpEntity);
                        //文件传输完成清空list
                        ftpList = new ArrayList<>();
                    }
                    ftpResultMap = new HashMap<>();
                    break;
                default:
                    break;
            }
        } else {
            switch (code) {
                case Const.FTP_USER:
                    //用户名
                    String username = strContent.split(Const.FTP_USER)[1].trim();
                    ftpUserMap.put(key, username);
                    break;
                case Const.FTP_PASS:
                    //密码
                    String user = ftpUserMap.get(key);
                    String password = strContent.split(Const.FTP_PASS)[1].trim();
                    ftpUserMap.put(key, user + Const.AUDIT_SPLIT_JOIN + password);
                    break;
                case Const.FTP_PORT:
                    //IP 地址和两字节的端口 ID
                    int port = getPortList(strContent.split(" "));
                    ftpPortMap.put(key, port);
                    break;
                case Const.FTP_STOR:
                    if (ftpTransferMap.containsKey(key)) {
                        String filename = strContent.split(Const.FTP_STOR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_STOR_SIGN);
                        ftpResultDetail.setFilename(filename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        String time = TimeUtils.TimestampToDateStrNew(data.getTime_s());
                        ftpResultDetail.setOperationtime(time);
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = ftpPortMap.get(key);
                        String dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                dataPort + Const.AUDIT_SPLIT_JOIN +
                                data.getDesip() + Const.AUDIT_SPLIT_JOIN + Const.FTP_DATA_TRANSFER_PORT;
                        String fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + filename;
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                case Const.FTP_RETR:
                    if (ftpTransferMap.containsKey(key)) {
                        String filename = strContent.split(Const.FTP_RETR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_RETR_SIGN);
                        ftpResultDetail.setFilename(filename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        String time = TimeUtils.TimestampToDateStrNew(data.getTime_s());
                        ftpResultDetail.setOperationtime(time);
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = ftpPortMap.get(key);
                        String dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                Const.FTP_DATA_TRANSFER_PORT + Const.AUDIT_SPLIT_JOIN +
                                data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                        String fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + filename;
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * FTP客户端数据传输端口计算
     * @param token
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private static int getPortList(String[] token) {
        String[] portCommand = token[token.length - 1].replaceAll("[()]\\.", "").split(",");
        int port = 0;
        if (portCommand.length == 6){
            port = (Integer.parseInt(portCommand[4]) * 256) + Integer.parseInt(portCommand[5].replaceAll("\r\n", ""));
        }
        return port;
    }
}
