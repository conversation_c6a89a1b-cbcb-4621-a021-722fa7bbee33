package com.wzsec.clean.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.stream.Collectors;

@Slf4j
@Component
public class IPUtils {

    private static Searcher searcher;

    /**
     * 在 Nginx 等代理之后获取用户真实 IP 地址
     *
     * @return 用户的真实 IP 地址
     */
    public static String getIpAddress(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String ip = request.getHeader("x-forwarded-for");
        if (isIpaddress(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (isIpaddress(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (isIpaddress(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (isIpaddress(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (isIpaddress(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
                // 根据网卡取本机配置的IP
                try {
                    InetAddress inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (UnknownHostException e) {
                    return ""; // 返回空字符串而不是打印堆栈
                }
            }
        }
        return ip;
    }

    /**
     * 判断是否为 IP 地址
     *
     * @param ip IP 地址
     */
    public static boolean isIpaddress(String ip) {
        return ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip);
    }

    /**
     * 获取本地 IP 地址
     *
     * @return 本地 IP 地址
     */
    public static String getHostIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            return "127.0.0.1"; // 返回默认值
        }
    }

    /**
     * 获取主机名
     *
     * @return 本地主机名
     */
    public static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            return "未知"; // 返回默认值
        }
    }

    /**
     * 根据 IP 地址从 ip2region.db 中获取地理位置
     *
     * @param ip IP 地址
     * @return IP 归属地
     */
    public static String getCityInfo(String ip) {
        try {
            return searcher.search(ip);
        } catch (Exception e) {
            return ""; // 发生异常时返回空字符串
        }
    }

    /**
     * 在服务启动时加载 ip2region.db 到内存中
     * 解决打包 jar 后找不到 ip2region.db 的问题
     */
    @PostConstruct
    private static void initIp2regionResource() {
        InputStream inputStream = null;
        try {
            inputStream = new ClassPathResource("ip2region.xdb").getInputStream();
            byte[] dbBinStr = FileCopyUtils.copyToByteArray(inputStream);
            // 创建一个完全基于内存的查询对象
            searcher = Searcher.newWithBuffer(dbBinStr);
        } catch (Exception e) {
            log.error("加载 ip2region.xdb 失败: {}", e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close(); // 确保流被关闭
                } catch (IOException e) {
                    log.warn("关闭输入流时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 根据 IP 地址返回归属地
     *
     * @param ip IP 地址
     * @return IP 归属地
     */
    public static String getIpRegion(String ip) {
        if (StringUtils.isBlank(ip)) {
            return ""; // 输入为空时直接返回空字符串
        }
        try {
            if (searcher == null) {
                initIp2regionResource(); // 确保 searcher 已初始化
            }
            // 获取城市信息并处理
            String cityInfo = getCityInfo(ip);
            // 去掉值为0的部分并用|连接
            String result = Arrays.stream(cityInfo.split("\\|"))
                    .filter(part -> !"0".equals(part))
                    .collect(Collectors.joining("|"));
            // 返回结果，确保没有结尾的|
            return result.endsWith("|") ? result.substring(0, result.length() - 1) : result;
        } catch (Exception e) {
            return ""; // 发生异常时返回空字符串
        }
    }


    public static void main(String[] args) {
        System.out.println(getIpRegion("*************"));
    }
}
