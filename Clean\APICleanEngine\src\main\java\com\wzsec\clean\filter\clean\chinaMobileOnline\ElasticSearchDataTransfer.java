package com.wzsec.clean.filter.clean.chinaMobileOnline;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.ApiDiscoverySecurityPlatform;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.wzsec.clean.modules.service.PcapService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * [中移在线]读取ES源数据,清洗后写入目标端ES
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Slf4j
@Component
public class ElasticSearchDataTransfer {

    private static final ObjectMapper objectMapper = new ObjectMapper(); // 用于对象转换
    private static final long SCROLL_TIMEOUT = Long.parseLong(ConfigurationManager.getProperty("netflow.es.scrolltimeValueMinutes").trim()); // scroll超时时间（分钟）
    private static final int BATCH_SIZE = Integer.parseInt(ConfigurationManager.getProperty("netflow.es.scrollsize").trim()); // 批次大小

    // private static final RestHighLevelClient targetClient = ES7Util.getClient(
    //         ConfigurationManager.getProperty("elasticsearch.username").trim(),
    //         ConfigurationManager.getProperty("elasticsearch.password").trim(),
    //         ConfigurationManager.getProperty("elasticsearch.hostlist").trim()); //目标端ES连接

    private static final RestHighLevelClient targetClient = SpringUtils.getApplicationContext().getBean(RestHighLevelClient.class);

    private static final RestHighLevelClient sourceClient = ES7Util.getClient(
            ConfigurationManager.getProperty("netflow.es.username").trim(),
            ConfigurationManager.getProperty("netflow.es.password").trim(),
            ConfigurationManager.getProperty("netflow.es.hostlist").trim()); //源端ES连接

    // TODO ES写库线程池
    private static final ExecutorService esWriteThreadPool = Executors.newFixedThreadPool(2, new ThreadFactory() {
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setName("ChinaMobileOnline_WriteThread-" + threadNumber.getAndIncrement());
            return thread;
        }
    });

    private static final String LOG_TYPE_REQUEST = "1"; // 日志类型为请求
    private static final String LOG_TYPE_RESPONSE = "4"; // 日志类型为响应

    private static final PcapService pcapService = SpringUtils.getApplicationContext().getBean(PcapService.class);


    /**
     * 数据传输
     *
     * @throws IOException IOException
     */
    public void transferData(String esIndexTime) throws IOException {

        // TODO 获取接口发现接口清单
        List<String> interfaceDiscoveryList = pcapService.getInterfaceDiscoverySecurityPlatform(); //接口发现接口清单

        Map<String, ApiDiscoverySecurityPlatform> interfaceDiscoveryMap = new HashMap<>(); //待入库新增接口清单

        // 目标索引(netflow)
        String targetIndex = Const.NETFLOW + "-" + esIndexTime;
        // 源端索引(aop_busi_yyyyMM)
        String sourceIndex = getSourceIndex();

        // 获取昨天的开始和结束时间
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); // 自定义日期格式
        String startOfYesterday = yesterday.atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME); // 00:00:00
        // String startOfYesterday = yesterday.atStartOfDay().format(formatter); // 00:00:00
        String endOfYesterday = yesterday.atTime(23, 59, 59).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME); // 23:59:59
        // String endOfYesterday = yesterday.atTime(23, 59, 59).format(formatter); // 23:59:59

        // 设置日期范围查询
        String dateField = "TRDNG_TIME"; // TODO 请求时间字段

        // 初始化滚动查询请求
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(SCROLL_TIMEOUT));
        SearchRequest searchRequest = new SearchRequest(sourceIndex);
        searchRequest.scroll(scroll);

        // 构建查询条件，包括时间范围过滤
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.boolQuery()
                        .must(QueryBuilders.rangeQuery(dateField).gte(startOfYesterday).lte(endOfYesterday)) // 时间范围查询
                        .must(QueryBuilders.matchAllQuery()) // 默认匹配所有数据
                        .filter(QueryBuilders.termsQuery("LOG_TYPECD", "1", "4")) //日志类型代码 1请求,4响应
                )
                .size(BATCH_SIZE); // 批次大小

        searchRequest.source(sourceBuilder);

        // 执行初始查询，获取第一个批次的数据
        assert sourceClient != null;
        SearchResponse searchResponse = sourceClient.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();
        SearchHit[] searchHits = searchResponse.getHits().getHits();

        try {
            // 处理滚动批次，直到没有更多数据
            while (searchHits != null && searchHits.length > 0) {
                // 处理和转换数据
                Map<String, ApiCallNetFlow> transformedData = processHits(searchHits);

                // 接口发现对象
                interfaceDiscovery(transformedData, interfaceDiscoveryList, interfaceDiscoveryMap);

                // 异步写入当前批次的数据, 写入操作不阻塞读取
                CompletableFuture.runAsync(() -> {
                    try {
                        bulkWriteToTarget(transformedData, targetIndex);
                    } catch (IOException e) {
                        log.error("批量写入失败", e);
                    }
                }, esWriteThreadPool);

                // 请求下一个批次的数据
                searchResponse = getNextScrollBatch(scrollId);
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }
        } finally {
            // 确保清除滚动查询，释放资源
            clearScroll(scrollId);
            // 释放源端客户端连接
            ES7Util.closeClient(sourceClient);
        }
    }

    /**
     * 界面发现
     *
     * @param transformedData        转换数据
     * @param interfaceDiscoveryList 接口发现列表
     * @param interfaceDiscoveryMap  界面发现图
     */
    private static void interfaceDiscovery(Map<String, ApiCallNetFlow> transformedData,
                                           List<String> interfaceDiscoveryList,
                                           Map<String, ApiDiscoverySecurityPlatform> interfaceDiscoveryMap) {
        // TODO 新增接口写入接口发现表
        for (ApiCallNetFlow apiCallNetFlow : transformedData.values()) {  //只遍历流量对象
            if (!interfaceDiscoveryList.contains(apiCallNetFlow.getApicode())) {
                ApiDiscoverySecurityPlatform apiDiscoverySecurityPlatform = new ApiDiscoverySecurityPlatform();
                apiDiscoverySecurityPlatform.setApicode(apiCallNetFlow.getApicode());
                apiDiscoverySecurityPlatform.setApiname(apiCallNetFlow.getApiname());
                apiDiscoverySecurityPlatform.setApiip("");
                apiDiscoverySecurityPlatform.setApiport("");
                apiDiscoverySecurityPlatform.setUrl("");
                apiDiscoverySecurityPlatform.setReq_example(apiCallNetFlow.getReqcontent().getParams());
                apiDiscoverySecurityPlatform.setRes_example(apiCallNetFlow.getRescontent().getData());
                apiDiscoverySecurityPlatform.setRequestmode("");
                apiDiscoverySecurityPlatform.setApistatus(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);
                apiDiscoverySecurityPlatform.setInserttime(DateUtil.now());
                apiDiscoverySecurityPlatform.setSparefield1(apiCallNetFlow.getAbilitymoduleid()); // 能力模块ID
                apiDiscoverySecurityPlatform.setSparefield2(apiCallNetFlow.getAbilitymodulename()); // 能力模块名称
                apiDiscoverySecurityPlatform.setSparefield3(apiCallNetFlow.getClientip());  //客户ID
                apiDiscoverySecurityPlatform.setSparefield4("");  //客户端名称
                interfaceDiscoveryMap.put(apiCallNetFlow.getApiport(), apiDiscoverySecurityPlatform);
                interfaceDiscoveryList.add(apiCallNetFlow.getApicode());
                pcapService.saveApiDiscovery_SecurityPlatform(apiDiscoverySecurityPlatform);
            }
        }
    }

    /**
     * 处理和转换数据
     *
     * @param searchHits 当前批次的搜索结果
     * @return 转换后的数据
     */
    private Map<String, ApiCallNetFlow> processHits(SearchHit[] searchHits) {
        ConcurrentHashMap<String, ApiCallNetFlow> apiCallNetFlowMap = new ConcurrentHashMap<>();
        for (SearchHit hit : searchHits) {
            if (hit.getSourceAsMap() == null) {
                // 没有有效的sourceData,跳过这个结果
                continue;
            }
            Map<String, Object> sourceData = hit.getSourceAsMap();
            try {
                // 将源数据转换为 ChinaMobileOnlineSource 对象
                ChinaMobileOnlineSource chinaMobileOnlineSource = objectMapper.convertValue(sourceData, ChinaMobileOnlineSource.class);
                // 从源数据中取出参数转为插入目标端ES对象
                transformData(apiCallNetFlowMap, chinaMobileOnlineSource);
            } catch (Exception e) {
                // 记录转换失败的日志，避免影响整个批次
                e.printStackTrace();
                System.err.println("转换失败, hit ID: " + hit.getId() + ", 错误信息: " + e.getMessage());
            }
        }
        return apiCallNetFlowMap;
    }


    /**
     * 数据转换方法，将源对象转换为目标对象
     *
     * @param apiCallNetFlowMap       api调用网络流图
     * @param chinaMobileOnlineSource 中移在线源数据文档
     * @return 转换后的目标对象 ApiCallNetFlow
     */
    private void transformData(Map<String, ApiCallNetFlow> apiCallNetFlowMap,
                               ChinaMobileOnlineSource chinaMobileOnlineSource) {

        String LOG_TYPECD = chinaMobileOnlineSource.getLogTypecd(); //日志类型(1接入请求,2转接请求,3转接响应,4接入响应,-1接入系统内部异常)
        String MSG_CNTT = chinaMobileOnlineSource.getMsgCntt(); //交易报文
        String RTN_TRANSID = chinaMobileOnlineSource.getRtnTransid();//平台流水号

        // 1. 请求响应完整
        if (apiCallNetFlowMap.containsKey(RTN_TRANSID)) {  // 包含流水号即需要合并请求响应
            // 日志类型识别请求响应(对象存在于map中)
            ApiCallNetFlow apiCallNetFlow = apiCallNetFlowMap.get(RTN_TRANSID);
            logTypeRecognitionRequestResponse(apiCallNetFlow, LOG_TYPECD, MSG_CNTT);  //请求响应

            apiCallNetFlowMap.put(RTN_TRANSID, apiCallNetFlow);

            // 2. 流水号仅对应请求或响应
        } else {
            String ABILITY_ID = chinaMobileOnlineSource.getAbilityId(); // 能力ID
            String ABILITY_NAME = chinaMobileOnlineSource.getAbilityName(); //能力名称
            String ABILITY_SYS = chinaMobileOnlineSource.getAbilitySys(); //能力模块ID
            String ABILITY_SYSNAME = chinaMobileOnlineSource.getAbilityName(); //能力模块名称
            String CUST_ID = chinaMobileOnlineSource.getCustId(); //客户ID
            String ERROR_MSG_CNTT = chinaMobileOnlineSource.getErrorMsgCntt(); //错误消息
            String MONITOR_TYPE = chinaMobileOnlineSource.getMonitorType(); //监控类型
            String SERVICE_ID = chinaMobileOnlineSource.getServiceId(); //服务ID
            String SERVICE_NAME = chinaMobileOnlineSource.getServiceName();//服务名称
            Integer SRV_INVKSTSCD = chinaMobileOnlineSource.getSrvInvkstsCd(); // 交易状态ID (0成功,1失败,2无效)
            String TRDNG_TIME = chinaMobileOnlineSource.getTrdngTime();// 交易时间

            // 此处进行转换逻辑的具体实现
            ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();

            apiCallNetFlow.setApiname(ABILITY_NAME); //接口名称
            apiCallNetFlow.setAbilitymoduleid(ABILITY_SYS); // 模块ID
            apiCallNetFlow.setAbilitymodulename(ABILITY_SYSNAME); // 模块ID

            // 日志类型识别请求响应
            logTypeRecognitionRequestResponse(apiCallNetFlow, LOG_TYPECD, MSG_CNTT); //请求响应

            apiCallNetFlow.setSeqnum(RTN_TRANSID); //请求标识
            apiCallNetFlow.setApicode(ABILITY_ID); //接口编码
            apiCallNetFlow.setClientip(CUST_ID); //客户端ID
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = outputFormat.format(TRDNG_TIME);
            apiCallNetFlow.setCalltime(formattedDate); //请求时间
            apiCallNetFlow.setCleantime(TimeUtils.getReqTime());

            // 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
            ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
            eventDTO.setCategory("netflow");
            apiCallNetFlow.setEvent(eventDTO);

            // int hour = TRDNG_TIME.getHours();
            // apiCallNetFlow.setReqtimetag(String.valueOf(hour)); //时间段标识,用于非工作时段场景检测
            apiCallNetFlowMap.put(RTN_TRANSID, apiCallNetFlow); //流水号仅对应请求或响应,先放入map
        }
    }

    /**
     * 日志类型识别请求响应
     *
     * @param apiCallNetFlow 目标端写入ES对象
     * @param LOG_TYPECD     日志类型
     * @param MSG_CNTT       交易报文
     */
    private static void logTypeRecognitionRequestResponse(ApiCallNetFlow apiCallNetFlow,
                                                          String LOG_TYPECD,
                                                          String MSG_CNTT) {
        if (LOG_TYPECD.equals(LOG_TYPE_REQUEST)) { //日志类型为请求
            // TODO 请求需解析请求头和请求体
            Params params = new Params();
            params.setParams(MSG_CNTT);
            // params.setHead();
            apiCallNetFlow.setReqcontent(params);
        } else if (LOG_TYPECD.equals(LOG_TYPE_RESPONSE)) { //日志类型为响应
            DataRescontent dataRescontent = new DataRescontent();
            dataRescontent.setData(MSG_CNTT);
            apiCallNetFlow.setRescontent(dataRescontent);
        }
    }

    /**
     * 批量写入目标ES集群
     *
     * @param transformedData 转换数据
     * @param targetIndex     目标索引
     * @throws IOException IOException
     */
    private void bulkWriteToTarget(Map<String, ApiCallNetFlow> transformedData, String targetIndex) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        for (ApiCallNetFlow transformedDatum : transformedData.values()) {  //只遍历流量对象
            transformedDatum.setTimestamp(Instant.now());
            String jsonData = JSON.toJSONString(transformedDatum);
            // 创建 IndexRequest 并指定 JSON 内容类型
            IndexRequest indexRequest = new IndexRequest(targetIndex)
                    .id(UUID.randomUUID().toString())
                    .source(jsonData, XContentType.JSON);
            // 将请求添加到批量请求中
            bulkRequest.add(indexRequest);
        }
        // 执行批量写入
        BulkResponse bulkResponse = targetClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        if (bulkResponse.hasFailures()) {
            // 记录失败的项
            System.err.println("批量写入错误: " + bulkResponse.buildFailureMessage());
        }
    }

    /**
     * 获取下一个滚动批次
     *
     * @param scrollId 滚动ID
     * @return 返回下一个批次的响应
     * @throws IOException IOException
     */
    private SearchResponse getNextScrollBatch(String scrollId) throws IOException {
        SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
        scrollRequest.scroll(TimeValue.timeValueMinutes(SCROLL_TIMEOUT)); // 设置滚动超时时间
        assert sourceClient != null;
        return sourceClient.scroll(scrollRequest, RequestOptions.DEFAULT);
    }

    /**
     * 清除滚动ID
     *
     * @param scrollId 滚动ID
     * @throws IOException IOException
     */
    private void clearScroll(String scrollId) throws IOException {
        if (scrollId != null) {
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            assert sourceClient != null;
            sourceClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        }
    }

    /**
     * 获取ES源索引(索引按每月创建yyyyMM)  aop_busi_202411
     *
     * @return {@link String }
     */
    private static String getSourceIndex() {
        //pcap文件清洗处理模式
        String cleaningMode = ConfigurationManager.getProperty("netflow.cleaningmode").trim(); //清洗模式
        String sourceIndex = ConfigurationManager.getProperty("netflow.es.index.prefix").trim(); //源端索引前缀
        String pcapDate = "";
        if (cleaningMode.equals(Const.CLEANING_MODE_DAY)) {
            // 指定日期清洗
            pcapDate = ConfigurationManager.getProperty("netflow.checkdate").trim();
            boolean validDate = DateUtils.isValidDateFormat(pcapDate, "yyyyMM");
            if (!validDate) {
                //默认检测前一天
                pcapDate = TimeUtils.getNextMonth(new Date(), Integer.parseInt(ConfigurationManager.getProperty("netflow.checkintervalday").trim()));
            }
        } else if (cleaningMode.equals(Const.CLEANING_MODE_HOUR)) {
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();
            // 获取前一个小时的时间
            LocalDateTime previousHourTime = currentTime.minusHours(1);
            DateTimeFormatter firstFormatter = DateTimeFormatter.ofPattern("yyyyMM");
            pcapDate = previousHourTime.format(firstFormatter);
        }
        return sourceIndex + pcapDate;
    }

}
