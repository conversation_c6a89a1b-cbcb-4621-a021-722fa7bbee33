package com.wzsec.clean.common.analysis;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.wzsec.clean.common.rule.IcLogCheckTaskClean;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.filter.clean.HainanBigData.PcapMessageByHaiNan;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.ApiWeaknesscheckService;
import com.wzsec.clean.modules.service.IcAlarmDisposalService;
import com.wzsec.clean.modules.service.IcInterfaceInfoService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * Title: PacketParser
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/5
 */
@Component
public class PacketParser {

    private static Logger log = LoggerFactory.getLogger(PacketParser.class);
    public static HashMap<String, JSONObject> dataMap = new HashMap<>();
    public static HashMap<String, JSONObject> filetransferMap = new HashMap<>();
    public static HashMap<String, JSONObject> interfaceTransferMap = new HashMap<>();
    public static HashMap<String, String> transferByteMap = new HashMap<>();
    public static HashMap<String, Integer> realityByteMap = new HashMap<>();
    public static HashMap<String, byte[]> fileByteMap = new HashMap<>();
    public static HashMap<String, JSONObject> httpJsonMap = new HashMap<>();

    //FTP信息Map
    public static Map<String, String> ftpUserMap = new HashMap<>();
    public static Map<String, Integer> ftpPortMap = new HashMap<>();
    public static Map<String, String> ftpDataTransferMap = new HashMap<>();
    public static Map<String, FTPResultDetail> ftpTransferMap = new HashMap<>();
    public static Map<String, FTPResultDetail> ftpResultMap = new HashMap<>();

    // 被动模式、主动模式标识
    public static boolean ispassive = false;

    @Autowired
    private IcInterfaceInfoService icInterfaceInfoService;

    @Autowired
    private IcAlarmDisposalService icAlarmDisposalService;

    @Autowired
    private ApiWeaknesscheckService apiWeaknesscheckService;

    private static PacketParser packetParser;

    @PostConstruct
    public void init() {
        packetParser = this;
        packetParser.icInterfaceInfoService = this.icInterfaceInfoService;
        packetParser.icAlarmDisposalService = this.icAlarmDisposalService;
        packetParser.apiWeaknesscheckService = this.apiWeaknesscheckService;
    }

    /**
     * @description 解析pcap文件
     * <AUTHOR>
     * @date 2020-11-02
     */
    public static void parserToES(String fielDir, String index, String filename, RestHighLevelClient client, String httprestoreFilePath, String ftpRestoreFilePath, String checktime, PcapTask pcapTask) throws IOException {

        FileInputStream fis = new FileInputStream(fielDir);
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        int m = fis.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);

        while (m > 0) {
            Packet data = new Packet();

            m = fis.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            m = fis.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));


            byte[] content = new byte[data.getpLength()];
            m = fis.read(content);

            //pcap Internet Protocol Version 中 header 报文长度 截取后四位
            byte[] ipv_headerlen = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 14;
                ipv_headerlen[0] = content[b];
            }

            String ip_header = Integer.toBinaryString(ipv_headerlen[0]);
            String ip_header_bin = "";
            if (ip_header.length() > 4) {
                ip_header_bin = ip_header.substring(ip_header.length() - 4);
            } else {
                ip_header_bin = ip_header;
            }
            int ip_header_len = Integer.parseInt(ip_header_bin, 2);

            //pcap Transmission Control Protocol 中 header 报文长度 7位截取前3位，8位截取前4位
            StringBuilder tcp_header = new StringBuilder();
            for (int i = 0; i < 1; i++) {
                int b = i + 46;
                tcp_header.append(Integer.toHexString(content[b] & 0xff));
            }
            Integer tcp_header_dec = Integer.valueOf(tcp_header.toString(), 16);
            String tcp_header_bin = Integer.toBinaryString(tcp_header_dec);
            String tcp_header_bin_str = "";
            if (tcp_header_bin.length() > 4) {
                tcp_header_bin_str = tcp_header_bin.substring(0, tcp_header_bin.length() - 4);
            } else {
                tcp_header_bin_str = tcp_header_bin;
            }
            Integer tcp_header_len = Integer.valueOf(tcp_header_bin_str, 2);

            //总长度
            StringBuilder tl = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 16;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                tl.append(appendZero(hexStr));
            }
            Integer len = Integer.valueOf(tl.toString(), 16);


            //截取有效负载payload长度=总长度 - (ip_header_len * 4) - (tcp_header_len * 4)
            int payloadLen = len - (ip_header_len * buffer_4.length) - (tcp_header_len * buffer_4.length);
            if (payloadLen > 0) {
                byte[] contentbyte = new byte[payloadLen];
                System.arraycopy(content, content.length - contentbyte.length, contentbyte, 0, payloadLen);
                data.setContent_byte(contentbyte);
            } else {
                continue;
            }

            byte[] pro = new byte[1];
            for (int i = 0; i < 1; i++) {
                int b = i + 23;
                pro[i] = content[b];
            }

            StringBuilder sbr = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 26;
                sbr.append(content[b] & 0xff);
                sbr.append(".");
            }
            sbr.deleteCharAt(sbr.length() - 1);
            data.setSourceip(sbr.toString());

            StringBuilder sba = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 30;
                sba.append(content[b] & 0xff);
                sba.append(".");
            }
            sba.deleteCharAt(sba.length() - 1);
            data.setDesip(sba.toString());

            if ((short) ipv_headerlen[0] == 69) {
                if ((short) pro[0] == 6) {
                    //TCP协议
                    StringBuilder sbd = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 34;
                        String hexStr = Integer.toHexString(content[b] & 0xff);
                        sbd.append(appendZero(hexStr));
                    }
                    Integer souport = Integer.valueOf(sbd.toString(), 16);
                    data.setSourceport(String.valueOf(souport));

                    StringBuilder sbe = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 36;
                        String hexStr = Integer.toHexString(content[b] & 0xff);
                        sbe.append(appendZero(hexStr));
                    }
                    Integer desport = Integer.valueOf(sbe.toString(), 16);
                    data.setDesport(String.valueOf(desport));

                    StringBuilder sbf = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 38;
                        sbf.append(content[b] & 0xff);
                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    data.setSeq_number(sbf.toString());


                    StringBuilder sbg = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 42;
                        sbg.append(content[b] & 0xff);
                    }
                    sbg.deleteCharAt(sbg.length() - 1);
                    data.setAck_number(sbg.toString());

                    //数据包转文本内容
                    String strContent = content2Str(data.getContent_byte(), "UTF-8");

                    data.setContent(strContent);

                    if (Const.INTERFACE_FLOW_SIGN.equals(pcapTask.getType())) {
                        //清洗HTTP流量到ES磁盘
                        cleanHttpFlowToES(data, filename, httprestoreFilePath, checktime);
                    }

                    if (Const.FTP_FLOW_SIGN.equals(pcapTask.getType())) {
                        //清洗FTP流量到ES磁盘
                        cleanFTPFlowToES(data, index, filename, client, ftpRestoreFilePath, checktime);
                    }
                }
            }

        }
        fis.close();
    }


    /**
     * @description 解析pcap文件
     * <AUTHOR>
     * @date 2020-11-02
     */
    public static void parser(String fielDir, String pcapCleanAfterPath, String filename, String restoreFilePath, String ftpRestoreFilePath, PcapTask pcapTask) throws IOException {

        Map<String, IcInterfaceInfo> icInterfaceInfoMap = new HashMap<>();
        List<String> apicodeList = new ArrayList<>();
        List<Packet> whetherAccessIsUnauthorizedList = new ArrayList<>();

        //获取接口信息(仅加载一次)
        if (icInterfaceInfoMap.size() <= 0) {
            List<IcInterfaceInfo> icInterfaceInfos = packetParser.icInterfaceInfoService.selectInterfaceInfo();
            for (IcInterfaceInfo icInterfaceInfo : icInterfaceInfos) {
                icInterfaceInfoMap.put(icInterfaceInfo.getApicode(), icInterfaceInfo);
                apicodeList.add(icInterfaceInfo.getApicode());
            }
        }

        FileInputStream fis = new FileInputStream(fielDir);
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        int m = fis.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_2);
        reverseByteArray(buffer_2);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);
        m = fis.read(buffer_4);
        reverseByteArray(buffer_4);


        while (m > 0) {
            Packet data = new Packet();

            byte[] content = new byte[0];
            byte[] ipv_headerlen = new byte[0];
            byte[] pro = new byte[0];
            try {
                m = fis.read(buffer_4);
                if (m < 0) {
                    break;
                }
                reverseByteArray(buffer_4);
                data.setTime_s(byteArrayToInt(buffer_4, 0));  //时间戳(秒)

                m = fis.read(buffer_4);
                reverseByteArray(buffer_4);
                data.setTime_ms(byteArrayToInt(buffer_4, 0));  //时间戳(微秒)

                m = fis.read(buffer_4);
                reverseByteArray(buffer_4);
                data.setpLength(byteArrayToInt(buffer_4, 0));  //抓包长度

                m = fis.read(buffer_4);
                reverseByteArray(buffer_4);
                data.setLength(byteArrayToInt(buffer_4, 0));  //实际长度


                content = new byte[data.getpLength()];
                m = fis.read(content);

                //pcap Internet Protocol Version 中 header 报文长度 截取后四位
                ipv_headerlen = new byte[1];
                for (int i = 0; i < 1; i++) {
                    int b = i + 14;
                    ipv_headerlen[0] = content[b];
                }

                String ip_header = Integer.toBinaryString(ipv_headerlen[0]);
                String ip_header_bin = "";
                if (ip_header.length() > 4) {
                    ip_header_bin = ip_header.substring(ip_header.length() - 4);
                } else {
                    ip_header_bin = ip_header;
                }
                int ip_header_len = Integer.parseInt(ip_header_bin, 2);

                //pcap Transmission Control Protocol 中 header 报文长度 7位截取前3位，8位截取前4位   TODO
                StringBuilder tcp_header = new StringBuilder();
                for (int i = 0; i < 1; i++) {
                    int b = i + 46;
                    tcp_header.append(Integer.toHexString(content[b] & 0xff));
                }
                Integer tcp_header_dec = Integer.valueOf(tcp_header.toString(), 16);
                String tcp_header_bin = Integer.toBinaryString(tcp_header_dec);
                String tcp_header_bin_str = "";
                if (tcp_header_bin.length() > 4) {
                    tcp_header_bin_str = tcp_header_bin.substring(0, tcp_header_bin.length() - 4);
                } else {
                    tcp_header_bin_str = tcp_header_bin;
                }
                Integer tcp_header_len = Integer.valueOf(tcp_header_bin_str, 2);

                //总长度
                StringBuilder tl = new StringBuilder();
                for (int i = 0; i < 2; i++) {
                    int b = i + 16;
                    String hexStr = Integer.toHexString(content[b] & 0xff);
                    tl.append(appendZero(hexStr));
                }
                Integer len = Integer.valueOf(tl.toString(), 16);


                //截取有效负载payload长度=总长度 - (ip_header_len * 4) - (tcp_header_len * 4)
                int payloadLen = len - (ip_header_len * buffer_4.length) - (tcp_header_len * buffer_4.length);
                if (payloadLen > 0) {
                    byte[] contentbyte = new byte[payloadLen];

                    System.arraycopy(content, content.length - contentbyte.length, contentbyte, 0, payloadLen);  //TODO

                    data.setContent_byte(contentbyte);   //TODO 数据包

                } else {
                    continue;
                }

                pro = new byte[1];
                for (int i = 0; i < 1; i++) {
                    int b = i + 23;
                    pro[i] = content[b];
                }

                StringBuilder sbr = new StringBuilder();
                for (int i = 0; i < 4; i++) {
                    int b = i + 26;
                    sbr.append(content[b] & 0xff);
                    sbr.append(".");
                }
                sbr.deleteCharAt(sbr.length() - 1);
                data.setSourceip(sbr.toString());  // 原地址

                StringBuilder sba = new StringBuilder();
                for (int i = 0; i < 4; i++) {
                    int b = i + 30;
                    sba.append(content[b] & 0xff);
                    sba.append(".");
                }
                sba.deleteCharAt(sba.length() - 1);
                data.setDesip(sba.toString());  //TODO 目标地址
            } catch (Exception e) {
                e.printStackTrace();
                //Console.log("数据包解析出现异常,异常信息为: {}", e.getMessage());
            }

            if ((short) ipv_headerlen[0] == 69) {    //TODO 原有:69
                if ((short) pro[0] == 6) {
                    //TCP协议
                    StringBuilder sbd = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 34;
                        String hexStr = Integer.toHexString(content[b] & 0xff);
                        sbd.append(appendZero(hexStr));
                    }
                    Integer souport = Integer.valueOf(sbd.toString(), 16);
                    data.setSourceport(String.valueOf(souport));  // 源端口

                    StringBuilder sbe = new StringBuilder();
                    for (int i = 0; i < 2; i++) {
                        int b = i + 36;
                        String hexStr = Integer.toHexString(content[b] & 0xff);
                        sbe.append(appendZero(hexStr));
                    }
                    Integer desport = Integer.valueOf(sbe.toString(), 16);
                    data.setDesport(String.valueOf(desport));  // 目标端口

                    StringBuilder sbf = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 38;
                        sbf.append(content[b] & 0xff);
                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    data.setSeq_number(sbf.toString());  // 序号


                    StringBuilder sbg = new StringBuilder();
                    for (int i = 0; i < 4; i++) {
                        int b = i + 42;
                        sbg.append(content[b] & 0xff);
                    }
                    sbg.deleteCharAt(sbg.length() - 1);
                    data.setAck_number(sbg.toString()); // 确认号

                    //数据包转文本内容
//                    String strContent = content2Str(data.getContent_byte(), StandardCharsets.UTF_8);
                    String strContent = new String(data.getContent_byte(), StandardCharsets.UTF_8);
//                    String strContent1 = new String(data.getContent_byte(), StandardCharsets.US_ASCII);
//                    String strContent2 = new String(data.getContent_byte(), StandardCharsets.ISO_8859_1);
//                    String strContent3 = new String(data.getContent_byte(), StandardCharsets.UTF_16BE);
//                    String strContent4 = new String(data.getContent_byte(), StandardCharsets.UTF_16LE);
//                    String strContent5 = new String(data.getContent_byte(), StandardCharsets.UTF_16);
//
//                    Console.log("数据包解析的文件内容为1: {}", strContent);
//                    Console.log("数据包解析的文件内容为2: {}", strContent1);
//                    Console.log("数据包解析的文件内容为3: {}", strContent2);
//                    Console.log("数据包解析的文件内容为4: {}", strContent3);
//                    Console.log("数据包解析的文件内容为5: {}", strContent4);
//                    Console.log("数据包解析的文件内容为: {}", strContent5);
//                    Console.log("------------------------------");

                    data.setContent(strContent);

//                    whetherAccessIsUnauthorized(data, strContent, whetherAccessIsUnauthorizedList);

                    if (Const.INTERFACE_FLOW_SIGN.equals(pcapTask.getType())) {   // 检测类型（0：接口日志，1：接口流量）
                        //清洗HTTP流量到本地磁盘
                        cleanHttpFlow(data, pcapCleanAfterPath, filename, restoreFilePath);

                    }

                    if (Const.FTP_FLOW_SIGN.equals(pcapTask.getType())) {
                        //清洗FTP流量到本地磁盘
                        cleanFTPFlow(data, pcapCleanAfterPath, filename, ftpRestoreFilePath);
                    }
                }
            }

        }

        try {
            if (whetherAccessIsUnauthorizedList != null) {
                List<Packet> newList = whetherAccessIsUnauthorizedList.stream().collect(Collectors
                        .collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Packet::getUrl))),
                                ArrayList::new));
                for (Packet packet : newList) {
                    if (apicodeList.contains(MD5Util.encrypt(packet.getUrl()))) {
                        writeAnAlarm(icInterfaceInfoMap, packet);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        fis.close();
    }


    /**
     * 是否未授权访问
     *
     * @param data       详情
     * @param strContent 请求内容
     */
    public static void whetherAccessIsUnauthorized(Packet data, String strContent, List<Packet> list) {

        try {
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");

            boolean b = true;

            String url = "", content = "", risk = "";
            if (strContent.contains(Const.HTTP_SIGN)) {
                //获取url进行判断
                url = strContent.split("GET|POST|PUT|DELETE")[1].split(Const.HTTP_SIGN)[0].trim();
                String sourceport = data.getSourceport();

                if (url.contains(Const.UNAUTHORIZED_ACCESS_ES_CAT) || url.contains(Const.UNAUTHORIZED_ACCESS_ES_RIVER) ||
                        url.contains(Const.UNAUTHORIZED_ACCESS_ES_SEARCH) || url.contains(Const.UNAUTHORIZED_ACCESS_ES_NODES)
                ) {
                    for (String s : dataArr) {
                        if (s.startsWith(Const.UNAUTHORIZED_ACCESS_AUTHENTICATIONMODE)) {
                            content = Const.UNAUTHORIZED_ACCESS_ES;
                            risk = Const.RISK_MIDDLE;
                            data.setUrl(url);
                            data.setAlarmContent(Const.UNAUTHORIZED_ACCESS_ES);
                            data.setRisk(Const.RISK_MIDDLE);
                            b = false;
                        }
                    }
                } else if ((Const.UNAUTHORIZED_ACCESS_Hadoop_50070.equals(sourceport) ||
                        Const.UNAUTHORIZED_ACCESS_Hadoop_8020.equals(sourceport)) && strContent.contains(Const.UNAUTHORIZED_ACCESS_HADOOP_CHARS)) {
                    for (String s : dataArr) {
                        if (s.startsWith(Const.UNAUTHORIZED_ACCESS_AUTHENTICATIONMODE)) {
                            content = Const.UNAUTHORIZED_ACCESS_HADOOP;
                            risk = Const.RISK_MIDDLE;
                            data.setUrl(url);
                            data.setAlarmContent(Const.UNAUTHORIZED_ACCESS_HADOOP);
                            data.setRisk(Const.RISK_MIDDLE);
                            b = false;
                        }
                    }
                }
            }
            if (!b) {
                list.add(data);
//                    writeAnAlarm(icInterfaceInfoMap, data, url, content ,risk);
            }
        } catch (Exception e) {
        }
    }

    //入库
    public static void writeAnAlarm(Map<String, IcInterfaceInfo> icInterfaceInfoMap, Packet data) {
        String apicode = null;
        try {
            apicode = MD5Util.encrypt(data.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
        }
        String apiname = icInterfaceInfoMap.get(apicode) == null ? "" : icInterfaceInfoMap.get(apicode).getApiname();


        ApiWeaknesscheck apiWeaknesscheck = new ApiWeaknesscheck();
        apiWeaknesscheck.setApicode(apicode);
        apiWeaknesscheck.setApiname(apiname);
        Date date = TimeUtils.stampForDate(data.getTime_s());
        String callTime = TimeUtils.dateForString(date);
        apiWeaknesscheck.setCalltime(callTime);
        apiWeaknesscheck.setApiurl(data.getUrl());
        apiWeaknesscheck.setRequestip(data.getSourceip());
        apiWeaknesscheck.setRequestport(data.getSourceport());
        apiWeaknesscheck.setResponseip(data.getDesip());
        apiWeaknesscheck.setResponseport(data.getDesport());
        apiWeaknesscheck.setChecktime(TimeUtils.getReqTime());
        apiWeaknesscheck.setDetectionrule(data.getAlarmContent());
        apiWeaknesscheck.setRisk(data.getRisk());
        packetParser.apiWeaknesscheckService.saveWeaknesscheck(apiWeaknesscheck); //保存


        IcAlarmDisposal icAlarmdisposal = new IcAlarmDisposal();
//                String str = "ElasticSearch未授权访问";
//                String eventDetails = StrUtil.format(str);

        icAlarmdisposal.setApicode(apicode); //接口编码
        icAlarmdisposal.setApiname(apiname); //接口名称
        icAlarmdisposal.setDetectionmodel(Const.DICT_API_INTERFACE_AUTHENTICATION); //检测模型
        icAlarmdisposal.setCircumstantiality(data.getAlarmContent()); //事件详情
        icAlarmdisposal.setRisk(data.getRisk()); //风险程度
        icAlarmdisposal.setReservefield1(data.getRisk()); //调整级别
        icAlarmdisposal.setChecktime(DateUtil.now());
        icAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态

        icAlarmdisposal.setSourceip(data.getSourceip());//源ip
        icAlarmdisposal.setDestinationip(data.getDesip());//目标IP
        icAlarmdisposal.setDestinationport(data.getDesport());//目标端口
        icAlarmdisposal.setEventrule(Const.DICT_API_INTERFACE_AUTHENTICATION_CHARS);// 规则()
        icAlarmdisposal.setAccount(icInterfaceInfoMap.get(apicode) == null ? "" : icInterfaceInfoMap.get(apicode).getApp());//事件相关用户名
        icAlarmdisposal.setArea(icInterfaceInfoMap.get(apicode) == null ? "" : icInterfaceInfoMap.get(apicode).getArea());//获取区县信息
        icAlarmdisposal.setDepartment(icInterfaceInfoMap.get(apicode) == null ? "" : icInterfaceInfoMap.get(apicode).getData_source());//获取部门信息


        packetParser.icAlarmDisposalService.saveResult(icAlarmdisposal); //保存到告警处置表

        //告警推送syslog
        new MonitorRiskAlarmData().sendIcExample(icAlarmdisposal);
    }


    //TODO 海南流量解析相关(读取)

    /**
     * 海南流量解析相关
     *
     * @param fielDir            事业dir
     * @param pcapCleanAfterPath pcap清洁路径后
     * @param filename           文件名
     * @param restoreFilePath    恢复文件路径
     * @param ftpRestoreFilePath ftp恢复文件路径
     * @param pcapTask           pcap任务
     * @throws IOException ioexception
     */
    public static void parserByProxy(String fielDir, String pcapCleanAfterPath,
                                     String filename, String restoreFilePath,
                                     String ftpRestoreFilePath, PcapTask pcapTask) throws IOException {

        FileInputStream is = new FileInputStream(fielDir);
        Pcap pcap = null;
        byte[] buffer_4 = new byte[4];
        byte[] buffer_2 = new byte[2];
        pcap = new Pcap();
        PcapHeader header = new PcapHeader();
        int m = is.read(buffer_4);
        if (m != 4) {
            return;
        }

        reverseByteArray(buffer_4);
        header.setMagic(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_2);
        reverseByteArray(buffer_2);
        header.setMagor_version(byteArrayToShort(buffer_2, 0));
        m = is.read(buffer_2);
        reverseByteArray(buffer_2);
        header.setMinor_version(byteArrayToShort(buffer_2, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setTimezone(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setSigflags(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setSnaplen(byteArrayToInt(buffer_4, 0));
        m = is.read(buffer_4);
        reverseByteArray(buffer_4);
        header.setLinktype(byteArrayToInt(buffer_4, 0));
        pcap.setHeader(header);


        while (m > 0) {
            Packet data = new Packet();

            // TODO 时间戳（秒）
            m = is.read(buffer_4);
            if (m < 0) {
                break;
            }
            reverseByteArray(buffer_4);
            data.setTime_s(byteArrayToInt(buffer_4, 0));

            //TODO 时间戳（微妙）
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setTime_ms(byteArrayToInt(buffer_4, 0));

            // TODO 抓包长度
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setpLength(byteArrayToInt(buffer_4, 0));

            //TODO 实际长度
            m = is.read(buffer_4);
            reverseByteArray(buffer_4);
            data.setLength(byteArrayToInt(buffer_4, 0));
            byte[] content = new byte[data.getpLength()];


            m = is.read(content);
            //TCP协议
            //TODO 源端口
            StringBuilder sbd = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 34;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                sbd.append(appendZero(hexStr));
            }
            Integer souport = Integer.valueOf(sbd.toString(), 16);
            data.setSourceport(String.valueOf(souport));


            //TODO 目标端口
            StringBuilder sbe = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                int b = i + 36;
                String hexStr = Integer.toHexString(content[b] & 0xff);
                sbe.append(appendZero(hexStr));
            }
            Integer desport = Integer.valueOf(sbe.toString(), 16);
            data.setDesport(String.valueOf(desport));

            // TODO 序号
            StringBuilder sbf = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 38;
                sbf.append(content[b] & 0xff);
            }
            sbf.deleteCharAt(sbf.length() - 1);
            data.setSeq_number(sbf.toString());

            //TODO 确认号
            StringBuilder sbg = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                int b = i + 42;
                sbg.append(content[b] & 0xff);
            }
            sbg.deleteCharAt(sbg.length() - 1);
            data.setAck_number(sbg.toString());
            data.setContent_byte(content);

            String strContent = content2Str(content, "UTF-8");
            data.setContent(strContent);

            //// TODO 清洗HTTP相关
            //PcapMessageByHaiNan.cleanHttpFlowMessage(data);

            PcapMessageByHaiNan.cleanHttpFlowMessageNew(data);

            if (Const.INTERFACE_FLOW_SIGN.equals(pcapTask.getType())) {   // 检测类型（0：接口日志，1：接口流量）
                //清洗HTTP流量
                //cleanHttpFlowByHaiNan(data, restoreFilePath);
            }

            if (Const.FTP_FLOW_SIGN.equals(pcapTask.getType())) {
                //清洗FTP流量
                cleanFTPFlow(data, pcapCleanAfterPath, filename, ftpRestoreFilePath);
            }

        }

        is.close();
    }


    /**
     * TODO 海南流量解析相关(清洗)【索引为:netflow-yyyy.MM.dd】
     *
     * @param data            数据
     * @param resotreFilePath resotre文件路径
     */
    public void cleanHttpFlowByHaiNan(Packet data, String resotreFilePath) {
        JSONObject joInfo = new JSONObject(true);

        String strContent = data.getContent();

        // TODO 解析拾取可用信息
        try {
            if (strContent.contains("GET") || strContent.contains("POST") ||
                    strContent.contains("PUT") || strContent.contains("DELETE")) {

                String desip = "";
                String sourceip = "";
                String reqcontent = "";
                String interfaceuri = "";
                String rescontent = "";
                String code = "";

                String[] split = strContent.split("\r\n\r\n")[0].split("\r\n");
                for (int i = 0; i < split.length; i++) {
                    if (split[i].trim().contains("_api_name")) {
                        code = split[i].trim().substring(("_api_name").length() + 1).trim();
                    }

                    if (split[i].trim().contains("X-Real-IP") || split[i].trim().contains("x-real-ip")) {
                        //客户端IP
                        desip = split[i].trim().substring(("X-Real-IP").length() + 1).trim();
                    }
                    if (split[i].trim().contains("x-forwarded-for") || split[i].trim().contains("X-Forwarded-For")) {
                        //服务端IP
                        String xff = split[i].trim().substring(("x-forwarded-for").length() + 1).trim();

                        try {
                            sourceip = xff.substring(0, xff.indexOf(","));
                        } catch (Exception e) {
                            sourceip = xff;
                        }
                    }

                }

                String[] splits = strContent.split("\r\n\r\n");
                //响应参数
                try {
                    rescontent = splits[1].trim();
                } catch (Exception e) {
                    rescontent = "";
                }


                String resData = "";

                if (rescontent.startsWith("{")) {
                    //从起始位置截取到最后一个"}"之前的全部字符,即去掉JSON后乱码或其他字符
                    resData = rescontent.substring(0, rescontent.lastIndexOf("}") + 1);
                } else {
                    resData = String.valueOf(JSONUtil.createObj().put("data", rescontent));
                }


                String[] split1 = strContent.split("GET|POST|PUT|DELETE");
                if (split1.length == 2) {
                    String[] http = split1[1].split("HTTP");
                    //接口编码
                    interfaceuri = http[0].trim();
                }

                //状态码
                String statuscode = "200";
                if (strContent.startsWith(Const.STATUS)) {
                    statuscode = strContent.split("\r\n")[0].split(" ")[1];
                }

                joInfo.put("apicode", code);
                joInfo.put("starttime", data.getTime_s());
                joInfo.put("desip", desip);
                joInfo.put("sourceip", sourceip);
                joInfo.put("reqcontent", reqcontent);
                joInfo.put("interfaceuri", URLUtil.decode(interfaceuri));
                joInfo.put("rescontent", URLUtil.decode(resData));
                joInfo.put("statuscode", statuscode);

                if (StringUtils.isNotEmpty(interfaceuri) && StringUtils.isNotEmpty(rescontent)
                        && StringUtils.isNotEmpty(resData)) {
                    httpJsonMap.put(data.getAck_number(), joInfo);
                }

            }

        } catch (Exception e) {
            Console.log("解析出现异常,异常为 :{}", e.getMessage());
            //e.printStackTrace();
        }

    }


    /**
     * 服务器流量解析相关
     *
     * @param data               数据
     * @param pcapCleanAfterPath pcap清洁路径后
     * @param filename           文件名
     * @param resotreFilePath    resotre文件路径
     */
    public void cleanHttpFlowByJinHua(Packet data, String pcapCleanAfterPath, String filename, String resotreFilePath) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        // 响应第一段、文件传输响应多部分、接口传输响应多部分
        if (strContent.contains(Const.HTTP_SIGN) ||
                filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN) ||
                interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {

            // '\r\n\r\n'截取请求头
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");
            boolean type = false;
            boolean interfaceTransfer = true;
            boolean fileTransfer = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.startsWith("Accept") && !s.startsWith("Accept-")) {
                    type = true;
                    continue;

                }
                // 判断响应头
                if (s.startsWith("Content-Type")) {
                    if (ParamUtil.checkContentTypeIsTxt(s)) {
                        type = true;
                        // 判断多部分文件传输
                        if (s.contains("application/octet-stream")) {
                            fileTransfer = true;
                            interfaceTransfer = false;
                        }
                        continue;
                    }
                }
                // 判断响应头 文件传输
                if (s.startsWith("Content-Disposition")) {
                    String[] options = s.split(":")[1].split(";");
                    String downFileName = "";
                    for (String option : options) {
                        if (option.contains("filename")) {
                            downFileName = option.split("=")[1].replaceAll("\\\"", "").trim();
                        }
                    }
                    // 文件名-文件字节大小
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String contentTypeSize = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), downFileName);
                    }
                }
                // 文件名-文件字节大小
                if (fileTransfer == true && s.startsWith("Content-Length")) {
                    String contentTypeSize = s.split(":")[1].trim();
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String downFileName = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), contentTypeSize);
                    }
                }
            }
            // 判断接口是否多部分传输
            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
            }
            // 判断文件是否多部分传输
            if (filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
                fileTransfer = true;
                interfaceTransfer = false;
            }
            if (type) {
                String[] s = strContent.split("GET|POST|PUT|DELETE");
                if (s.length == 2) {
                    //请求
                    String reqmethod = strContent.split("\r\n")[0].split(" ")[0];
                    String[] http = s[1].split(Const.HTTP_SIGN);
                    String acknum = data.getAck_number();
                    String interfaceuri = http[0].split("\\?")[0].trim();
                    String sourceip = data.getSourceip();
                    String sourceport = data.getSourceport();
                    String desip = data.getDesip();
                    String desport = data.getDesport();
                    String reqcontent = strContent;
                    joInfo.put("seqnumber", acknum);
                    joInfo.put("interfaceuri", interfaceuri);
                    joInfo.put("sourceip", sourceip);
                    joInfo.put("sourceport", sourceport);
                    joInfo.put("desip", desip);
                    joInfo.put("desport", desport);
                    joInfo.put("reqmethod", reqmethod);
                    joInfo.put("starttime", data.getTime_s());
                    joInfo.put("reqcontent", reqcontent);
                    dataMap.put(acknum, joInfo);
                } else {
                    // 响应第一段
                    if (strContent.startsWith(Const.HTTP_SIGN)) {
                        //根据响应的seq_number获取请求信息
                        if (dataMap.containsKey(data.getSeq_number())) {
                            String resstatuscode = strContent.split("\r\n")[0].split(" ")[1];
                            JSONObject reqJson = dataMap.get(data.getSeq_number());
                            String seqnumber = data.getSeq_number();
                            String interfaceuri = reqJson.get("interfaceuri").toString();
                            String sourceip = data.getSourceip();
                            String sourceport = data.getSourceport();
                            String desip = data.getDesip();
                            String desport = data.getDesport();
                            //String resstatus = "";
                            String rescontent = strContent;
                            joInfo.put("seqnumber", seqnumber);
                            joInfo.put("interfaceuri", interfaceuri);
                            joInfo.put("sourceip", sourceip);
                            joInfo.put("sourceport", sourceport);
                            joInfo.put("desip", desip);
                            joInfo.put("desport", desport);
                            joInfo.put("endtime", data.getTime_s());
                            joInfo.put("resstatuscode", resstatuscode);
                            joInfo.put("rescontent", rescontent);
                            joType.put("req", reqJson);
                            joType.put("res", joInfo);
                            dataMap.remove(seqnumber);

                            // 接口传输map
                            if (interfaceTransfer) {
                                if (!interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                    httpJsonMap.put(data.getAck_number(), joType);
                                }
                            }

                            // 文件传输map
                            if (fileTransfer) {
                                if (!filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                }
                                // 文件传输实际字节大小
                                if (realityByteMap.containsKey(data.getAck_number())) {
                                    Integer len = realityByteMap.get(data.getAck_number());
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length + len);
                                } else {
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length);
                                }
                                // 组装多部分传输字节数组
                                if (fileByteMap.containsKey(data.getAck_number())) {
                                    byte[] bytes = fileByteMap.get(data.getAck_number());
                                    byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                    System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                    System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                    fileByteMap.put(data.getAck_number(), newByte);
                                } else {
                                    fileByteMap.put(data.getAck_number(), data.getContent_byte());
                                }
                            }
                        }
                    } else {
                        /** 响应多部分 多部分传输 多个响应ack相同
                         根据响应的文件多部分ack_number获取字节
                         接口传输保存结果 */
                        if (interfaceTransfer) {
                            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN) &&
                                    interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                JSONObject reqJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String rescontent = resJson.getString("rescontent");
                                StringBuilder rescontentBuilder = new StringBuilder();
                                rescontentBuilder.append(rescontent).append(data.getContent());
                                resJson.put("rescontent", rescontentBuilder.toString());
                                JSONObject joResult = new JSONObject(true);
                                joResult.put("req", reqJson);
                                joResult.put("res", resJson);
                                httpJsonMap.put(data.getAck_number(), joResult);
                            }
                        }

                        //文件传输
                        if (fileTransfer) {
                            int contentLen = 0;
                            int realityLen = 0;
                            String downFileName = "";

                            // 文件传输实际字节大小
                            if (realityByteMap.containsKey(data.getAck_number())) {
                                Integer len = realityByteMap.get(data.getAck_number());
                                contentLen = data.getContent_byte().length + len;
                                realityByteMap.put(data.getAck_number(), contentLen);
                            }

                            // 组装多部分传输字节数组
                            if (fileByteMap.containsKey(data.getAck_number())) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                fileByteMap.put(data.getAck_number(), newByte);
                            }

                            if (transferByteMap.containsKey(data.getAck_number())) {
                                String[] filenameAndSize = transferByteMap.get(data.getAck_number()).split(Const.AUDIT_SPLIT_JOIN);
                                if (filenameAndSize.length == 2) {
                                    downFileName = filenameAndSize[0];
                                    realityLen = Integer.parseInt(filenameAndSize[1]);
                                }

                            }

                            // 判断拼起来的包长度是否大于文件字节真实长度
                            if (contentLen > realityLen) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                // 开始索引
                                int startIndex = contentLen - realityLen;
                                byte[] fileByte = new byte[realityLen];
                                System.arraycopy(bytes, startIndex, fileByte, 0, fileByte.length);
                                FileUtils.getFileByBytes(fileByte, resotreFilePath, downFileName);
                                // 读取文件内容转成str
                                String fileContent = getFileContent(resotreFilePath, downFileName);
                                JSONObject reqJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String interfaceuri = reqJson.get("interfaceuri").toString();
                                String resstatuscode = resJson.get("resstatuscode").toString();
                                String rescontent = resJson.get("rescontent").toString();
                                String seqnumber = resJson.get("seqnumber").toString();
                                String sourceip = data.getSourceip();
                                String sourceport = data.getSourceport();
                                String desip = data.getDesip();
                                String desport = data.getDesport();
                                //String resstatus = "";
                                joInfo.put("seqnumber", seqnumber);
                                joInfo.put("interfaceuri", interfaceuri);
                                joInfo.put("sourceip", sourceip);
                                joInfo.put("sourceport", sourceport);
                                joInfo.put("desip", desip);
                                joInfo.put("desport", desport);
                                joInfo.put("endtime", data.getTime_s());
                                joInfo.put("resstatuscode", resstatuscode);
                                rescontent = rescontent.split("\r\n\r\n")[0] + "\r\n\r\n";
                                joInfo.put("rescontent", rescontent + fileContent);
                                joType.put("req", reqJson);
                                joType.put("res", joInfo);
                                filetransferMap.remove(data.getAck_number() + Const.REQ_SIGN);
                                filetransferMap.remove(data.getAck_number() + Const.RES_SIGN);
                                httpJsonMap.put(data.getAck_number(), joType);
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 清洗FTP流量到ES
     *
     * <AUTHOR>
     * @date 2020-11-17
     */
    private static void cleanFTPFlowToES(Packet data, String index, String resFilename, RestHighLevelClient client, String ftpRestoreFilePath, String checktime) {

        String strContent = data.getContent().replaceAll("\r\n", "");
        FTPResultDetail ftpData = new FTPResultDetail();

        //FTP 协议
        String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();
        String datakey1 = "";
        String datakey2 = "";
        if (ispassive) {
            datakey1 = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport();
            datakey2 = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();
        } else {
            datakey1 = key;
        }
        //拆分数据传输的内容
        String ftpTransferKey = "";
        if (ftpDataTransferMap.containsKey(datakey1)) {
            ftpTransferKey = ftpDataTransferMap.get(datakey1);
        } else if (ftpDataTransferMap.containsKey(datakey2)) {
            ftpTransferKey = ftpDataTransferMap.get(datakey1);
        }
        if (StringUtils.isNotEmpty(ftpTransferKey)) {
            int bytes = data.getContent_byte().length;

            //TODO 暂时排除jar
            if (!ftpTransferKey.endsWith("jar")) {
                //输出到文件中
                append2File(strContent, ftpRestoreFilePath + File.separator + ftpTransferKey);
            }
            //计算文件大小
            if (ftpResultMap.containsKey(ftpTransferKey)) {
                FTPResultDetail ftpResultDetail = ftpResultMap.get(ftpTransferKey);
                if (StringUtils.isNotEmpty(ftpResultDetail.getFilesize())) {
                    String filesize = ftpResultDetail.getFilesize();
                    long size = Long.valueOf(filesize).longValue() + (long) bytes;
                    ftpResultDetail.setFilesize(String.valueOf(size));
                    ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                } else {
                    ftpResultDetail.setFilesize(String.valueOf(bytes));
                    ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                }
            }
        }
        //保存数据传输的基本信息
        if (!ftpTransferMap.containsKey(key)) {
            ftpData.setSourceip(data.getSourceip());
            ftpData.setSourceport(data.getSourceport());
            ftpData.setDesip(data.getDesip());
            ftpData.setDesport(data.getDesport());
            ftpTransferMap.put(key, ftpData);
        }
        //截取4位去空格，FTP请求指令是4位，响应码是3位
        String code = "";
        if (strContent.length() > 4) {
            code = strContent.substring(0, 4).trim();
        }

        if (code.length() == 3) {
            switch (code) {
                case Const.FTP_CWD:
                    String path = strContent.split(Const.FTP_CWD)[1].trim();
                    if (ftpTransferMap.containsKey(key)) {
                        FTPResultDetail ftpResultDetail = ftpTransferMap.get(key);
                        ftpResultDetail.setFilepath(path);
                    }
                    break;
                case Const.FTP_226_TRANSFER_COMPLETE:
                    //传输完成
                    JSONObject joInfo = new JSONObject(true);
                    Set<String> ftpResultKeyMap = ftpResultMap.keySet();
                    for (String reskey : ftpResultKeyMap) {
                        FTPResultDetail ftpResult = ftpResultMap.get(reskey);
                        String sourceip = ftpResult.getSourceip();
                        String sourceport = ftpResult.getSourceport();
                        String desip = ftpResult.getDesip();
                        String desport = ftpResult.getDesport();
                        String sign = ftpResult.getSign();
                        String operationtime = ftpResult.getOperationtime();
                        String username = ftpResult.getUsername();
                        String password = ftpResult.getPassword();
                        String filename = ftpResult.getFilename();
                        String filepath = ftpResult.getFilepath();
                        String filesize = ftpResult.getFilesize();
                        String ftp = Const.FTP_SIGN.toLowerCase();
                        joInfo.put("sourceip", sourceip);
                        joInfo.put("sourceport", sourceport);
                        joInfo.put("desip", desip);
                        joInfo.put("desport", desport);
                        joInfo.put("sign", sign);
                        joInfo.put("operationtime", operationtime);
                        joInfo.put("username", username);
                        joInfo.put("password", password);
                        joInfo.put("filename", filename);
                        joInfo.put("filepath", filepath);
                        joInfo.put("filesize", filesize);
                        joInfo.put("@checktime", checktime);

                        String ftpIndexName = "";
                        if (index.endsWith(Const.AUDIT_SPLIT_JOIN)) {
                            ftpIndexName = index + Const.FTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + checktime;
                        } else {
                            ftpIndexName = index + Const.AUDIT_SPLIT_JOIN + Const.FTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + checktime;
                        }
                        //将清洗之后数据保存到ES
                        boolean exists = ES7Util.existsIndex(client, ftpIndexName);
                        if (exists) {
                            //保存数据
                            ES7Util.addData(client, ftpIndexName, joInfo);
                        } else {
                            //创建索引
                            boolean createIndex = ES7Util.createIndex(client, ftpIndexName);
                            if (createIndex) {
                                ES7Util.addData(client, ftpIndexName, joInfo);
                            }
                        }
                    }
                    ftpResultMap = new HashMap<>();
                    break;
                case Const.FTP_227_PASV:
                    ispassive = true;
                    String passivekey = data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport() + Const.AUDIT_SPLIT_JOIN +
                            data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport();
                    // 被动模式
                    String ipAndPort = strContent.substring(strContent.indexOf("(") + 1, strContent.lastIndexOf(")"));
                    //IP 地址和两字节的端口 ID
                    int port = getPort(ipAndPort);
                    ftpPortMap.put(passivekey, port);
                    break;
                default:
                    break;
            }
        } else {
            switch (code) {
                case Const.FTP_USER:
                    //用户名
                    String username = strContent.split(Const.FTP_USER)[1].trim();
                    ftpUserMap.put(key, username);
                    break;
                case Const.FTP_PASS:
                    //密码
                    if (ftpUserMap.containsKey(key)) {
                        String user = ftpUserMap.get(key);
                        String password = strContent.split(Const.FTP_PASS)[1].trim();
                        ftpUserMap.put(key, user + Const.AUDIT_SPLIT_JOIN + password);
                    }
                    break;
                case Const.FTP_PORT:
                    ispassive = false;
                    //IP 地址和两字节的端口 ID
                    int port = getPort(strContent.split(" ")[1]);
                    ftpPortMap.put(key, port);
                    break;
                case Const.FTP_STOR:
                    if (ftpTransferMap.containsKey(key)) {
                        String ftpfilename = strContent.split(Const.FTP_STOR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_STOR_SIGN);
                        ftpResultDetail.setFilename(ftpfilename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        ftpResultDetail.setOperationtime(String.valueOf(data.getTime_s()));
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = 0;
                        if (ftpPortMap.containsKey(key)) {
                            dataPort = ftpPortMap.get(key);
                        }
                        String dataKey = "";
                        String fileKey = "";
                        if (ispassive) {
                            // 被动
                            /*dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                    (Integer.parseInt(data.getSourceport())+1) + Const.AUDIT_SPLIT_JOIN +
                                    data.getDesip() + Const.AUDIT_SPLIT_JOIN + dataPort;*/
                            dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        } else {
                            // 主动
                            dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                    dataPort + Const.AUDIT_SPLIT_JOIN +
                                    data.getDesip() + Const.AUDIT_SPLIT_JOIN + Const.FTP_DATA_TRANSFER_PORT;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        }
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                case Const.FTP_RETR:
                    if (ftpTransferMap.containsKey(key)) {
                        String ftpfilename = strContent.split(Const.FTP_RETR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_RETR_SIGN);
                        ftpResultDetail.setFilename(ftpfilename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        ftpResultDetail.setOperationtime(String.valueOf(data.getTime_s()));
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = 0;
                        if (ftpPortMap.containsKey(key)) {
                            dataPort = ftpPortMap.get(key);
                        }
                        String dataKey = "";
                        String fileKey = "";
                        if (ispassive) {
                            // 被动
                            /*dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                    dataPort + Const.AUDIT_SPLIT_JOIN +
                                    data.getSourceip() + Const.AUDIT_SPLIT_JOIN + (Integer.parseInt(data.getSourceport())+1);*/
                            dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        } else {
                            // 主动
                            dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                    Const.FTP_DATA_TRANSFER_PORT + Const.AUDIT_SPLIT_JOIN +
                                    data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;
                        }
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                default:
                    break;
            }
        }
    }


    /**
     * 清洗FTP流量
     *
     * <AUTHOR>
     * @date 2020-11-05
     */
    private static void cleanFTPFlow(Packet data, String pcapCleanAfterPath, String resFilename, String ftpRestoreFilePath) {
        String afterPath = pcapCleanAfterPath + File.separator + Const.FTP_SIGN + File.separator
                + Const.FTP_SIGN + "_clean_" + resFilename.replace("pcap", "txt");

        String strContent = data.getContent();
        FTPResultDetail ftpData = new FTPResultDetail();

        //FTP 协议
        String key = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();

        // 解决无法确定被动模式下客户端端口问题
        String datakey1 = "";
        String datakey2 = "";
        if (ispassive) {
            datakey1 = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport();
            datakey2 = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport();
        } else {
            datakey1 = key;
        }
        // 拆分数据传输的内容
        String ftpTransferKey = "";
        if (ftpDataTransferMap.containsKey(datakey1)) {
            ftpTransferKey = ftpDataTransferMap.get(datakey1);
        } else if (ftpDataTransferMap.containsKey(datakey2)) {
            ftpTransferKey = ftpDataTransferMap.get(datakey1);
        }
        if (StringUtils.isNotEmpty(ftpTransferKey)) {
            int bytes = data.getContent_byte().length;

            //TODO 暂时排除jar
            if (!ftpTransferKey.endsWith("jar")) {
                //输出到文件中
                append2File(strContent, ftpRestoreFilePath + File.separator + ftpTransferKey);
            }
            //计算文件大小
            if (ftpResultMap.containsKey(ftpTransferKey)) {
                FTPResultDetail ftpResultDetail = ftpResultMap.get(ftpTransferKey);
                if (StringUtils.isNotEmpty(ftpResultDetail.getFilesize())) {
                    String filesize = ftpResultDetail.getFilesize();
                    long size = Long.valueOf(filesize).longValue() + (long) bytes;
                    ftpResultDetail.setFilesize(String.valueOf(size));
                    ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                } else {
                    ftpResultDetail.setFilesize(String.valueOf(bytes));
                    ftpResultMap.put(ftpTransferKey, ftpResultDetail);
                }
            }
        }
        //保存数据传输的基本信息
        if (!ftpTransferMap.containsKey(key)) {
            ftpData.setSourceip(data.getSourceip());
            ftpData.setSourceport(data.getSourceport());
            ftpData.setDesip(data.getDesip());
            ftpData.setDesport(data.getDesport());
            ftpTransferMap.put(key, ftpData);
        }
        //截取4位去空格，FTP请求指令是4位，响应码是3位
        String code = "";
        if (strContent.length() > 4) {
            code = strContent.substring(0, 4).trim();
        }

        if (code.length() == 3) {
            switch (code) {
                case Const.FTP_CWD:
                    String path = strContent.split(Const.FTP_CWD)[1].trim();
                    if (ftpTransferMap.containsKey(key)) {
                        FTPResultDetail ftpResultDetail = ftpTransferMap.get(key);
                        ftpResultDetail.setFilepath(path);
                    }
                    break;
                case Const.FTP_226_TRANSFER_COMPLETE:
                    //传输完成
                    JSONObject joInfo = new JSONObject(true);
                    Set<String> ftpResultKeyMap = ftpResultMap.keySet();
                    for (String reskey : ftpResultKeyMap) {
                        FTPResultDetail ftpResult = ftpResultMap.get(reskey);
                        String sourceip = ftpResult.getSourceip();
                        String sourceport = ftpResult.getSourceport();
                        String desip = ftpResult.getDesip();
                        String desport = ftpResult.getDesport();
                        String sign = ftpResult.getSign();
                        String operationtime = ftpResult.getOperationtime();
                        String username = ftpResult.getUsername();
                        String password = ftpResult.getPassword();
                        String filename = ftpResult.getFilename();
                        String filepath = ftpResult.getFilepath();
                        String filesize = ftpResult.getFilesize();
                        joInfo.put("sourceip", sourceip);
                        joInfo.put("sourceport", sourceport);
                        joInfo.put("desip", desip);
                        joInfo.put("desport", desport);
                        joInfo.put("sign", sign);
                        joInfo.put("operationtime", operationtime);
                        joInfo.put("username", username);
                        joInfo.put("password", password);
                        joInfo.put("filename", filename);
                        joInfo.put("filepath", filepath);
                        joInfo.put("filesize", filesize);

                        //输出到清洗之后文件
                        append2File(joInfo.toJSONString() + "\n", afterPath);
                    }
                    ftpResultMap = new HashMap<>();
                    break;
                case Const.FTP_227_PASV:
                    ispassive = true;
                    String passivekey = data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getDesport() + Const.AUDIT_SPLIT_JOIN +
                            data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getSourceport();
                    // 被动模式
                    String ipAndPort = strContent.substring(strContent.indexOf("(") + 1, strContent.lastIndexOf(")"));
                    //IP 地址和两字节的端口 ID
                    int port = getPort(ipAndPort);
                    ftpPortMap.put(passivekey, port);
                    break;
                default:
                    break;
            }
        } else {
            switch (code) {
                case Const.FTP_USER:
                    //用户名
                    String username = strContent.split(Const.FTP_USER)[1].trim();
                    ftpUserMap.put(key, username);
                    break;
                case Const.FTP_PASS:
                    //密码
                    if (ftpUserMap.containsKey(key)) {
                        String user = ftpUserMap.get(key);
                        String password = strContent.split(Const.FTP_PASS)[1].trim();
                        ftpUserMap.put(key, user + Const.AUDIT_SPLIT_JOIN + password);
                    }
                    break;
                case Const.FTP_PORT:
                    ispassive = false;
                    //IP 地址和两字节的端口 ID
                    int port = getPort(strContent.split(" ")[1]);
                    ftpPortMap.put(key, port);
                    break;
                case Const.FTP_STOR:
                    if (ftpTransferMap.containsKey(key)) {
                        String ftpfilename = strContent.split(Const.FTP_STOR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_STOR_SIGN);
                        ftpResultDetail.setFilename(ftpfilename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        ftpResultDetail.setOperationtime(String.valueOf(data.getTime_s()));
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = 0;
                        if (ftpPortMap.containsKey(key)) {
                            dataPort = ftpPortMap.get(key);
                        }
                        String dataKey = "";
                        String fileKey = "";
                        if (ispassive) {
                            // 被动
                            /*dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                    (Integer.parseInt(data.getSourceport())+1) + Const.AUDIT_SPLIT_JOIN +
                                    data.getDesip() + Const.AUDIT_SPLIT_JOIN + dataPort;*/
                            dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN + data.getDesip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        } else {
                            // 主动
                            dataKey = data.getSourceip() + Const.AUDIT_SPLIT_JOIN +
                                    dataPort + Const.AUDIT_SPLIT_JOIN +
                                    data.getDesip() + Const.AUDIT_SPLIT_JOIN + Const.FTP_DATA_TRANSFER_PORT;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        }
                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                case Const.FTP_RETR:
                    if (ftpTransferMap.containsKey(key)) {
                        String ftpfilename = strContent.split(Const.FTP_RETR)[1].trim();
                        FTPResultDetail ftpTransfer = ftpTransferMap.get(key);
                        FTPResultDetail ftpResultDetail = new FTPResultDetail();
                        ftpResultDetail.setSourceip(ftpTransfer.getSourceip());
                        ftpResultDetail.setSourceport(ftpTransfer.getSourceport());
                        ftpResultDetail.setDesip(ftpTransfer.getDesip());
                        ftpResultDetail.setDesport(ftpTransfer.getDesport());
                        ftpResultDetail.setSign(Const.FTP_RETR_SIGN);
                        ftpResultDetail.setFilename(ftpfilename);
                        ftpResultDetail.setFilepath(ftpTransfer.getFilepath());
                        ftpResultDetail.setOperationtime(String.valueOf(data.getTime_s()));
                        if (ftpUserMap.containsKey(key)) {
                            String user_pass = ftpUserMap.get(key);
                            ftpResultDetail.setUsername(user_pass.split(Const.AUDIT_SPLIT_JOIN)[0]);
                            ftpResultDetail.setPassword(user_pass.split(Const.AUDIT_SPLIT_JOIN)[1]);
                        }
                        Integer dataPort = 0;
                        if (ftpPortMap.containsKey(key)) {
                            dataPort = ftpPortMap.get(key);
                        }
                        String dataKey = "";
                        String fileKey = "";
                        if (ispassive) {
                            // 被动
                            /*dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                    dataPort + Const.AUDIT_SPLIT_JOIN +
                                    data.getSourceip() + Const.AUDIT_SPLIT_JOIN + (Integer.parseInt(data.getSourceport())+1);*/
                            dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN + data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;

                        } else {
                            // 主动
                            dataKey = data.getDesip() + Const.AUDIT_SPLIT_JOIN +
                                    Const.FTP_DATA_TRANSFER_PORT + Const.AUDIT_SPLIT_JOIN +
                                    data.getSourceip() + Const.AUDIT_SPLIT_JOIN + dataPort;
                            fileKey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpfilename;
                        }

                        ftpDataTransferMap.put(dataKey, fileKey);
                        ftpResultMap.put(fileKey, ftpResultDetail);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 清洗HTTP流量保存到ES
     *
     * <AUTHOR>
     * @date 2020-12-03
     */
    public static void cleanHttpFlowToES(Packet data, String filename, String httprestoreFilePath, String checktime) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        // 响应第一段、文件传输响应多部分、接口传输响应多部分
        if (strContent.contains(Const.HTTP_SIGN) ||
                filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN) ||
                interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {

            // '\r\n\r\n'截取请求头
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");
            boolean type = false;
            boolean interfaceTransfer = true;
            boolean fileTransfer = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.startsWith("Accept") && !s.startsWith("Accept-")) {
                    type = true;
                    continue;

                }
                // 判断响应头
                if (s.startsWith("Content-Type")) {
                    if (ParamUtil.checkContentTypeIsTxt(s)) {
                        type = true;
                        // 判断多部分文件传输
                        if (s.contains("application/octet-stream")) {
                            fileTransfer = true;
                            interfaceTransfer = false;
                        }
                        continue;
                    }
                }
                // 判断响应头 文件传输
                if (s.startsWith("Content-Disposition")) {
                    String[] options = s.split(":")[1].split(";");
                    String downFileName = "";
                    for (String option : options) {
                        if (option.contains("filename")) {
                            downFileName = option.split("=")[1].replaceAll("\\\"", "").trim();
                        }
                    }
                    // 文件名-文件字节大小
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String contentTypeSize = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), downFileName);
                    }
                }
                // 文件名-文件字节大小
                if (fileTransfer == true && s.startsWith("Content-Length")) {
                    String contentTypeSize = s.split(":")[1].trim();
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String downFileName = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), contentTypeSize);
                    }
                }
            }
            // 判断接口是否多部分传输
            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
            }
            // 判断文件是否多部分传输
            if (filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
                fileTransfer = true;
                interfaceTransfer = false;
            }
            if (type) {
                String[] s = strContent.split("GET|POST|PUT|DELETE");
                if (s.length == 2) {
                    //请求
                    String reqmethod = strContent.split("\r\n")[0].split(" ")[0];
                    String[] http = s[1].split(Const.HTTP_SIGN);
                    String acknum = data.getAck_number();
                    String interfaceuri = http[0].split("\\?")[0].trim();
                    String sourceip = data.getSourceip();
                    String sourceport = data.getSourceport();
                    String desip = data.getDesip();
                    String desport = data.getDesport();
                    String reqcontent = strContent;
                    joInfo.put("seqnumber", acknum);
                    joInfo.put("interfaceuri", interfaceuri);
                    joInfo.put("sourceip", sourceip);
                    joInfo.put("sourceport", sourceport);
                    joInfo.put("desip", desip);
                    joInfo.put("desport", desport);
                    joInfo.put("reqmethod", reqmethod);
                    joInfo.put("starttime", data.getTime_s());
                    joInfo.put("reqcontent", reqcontent);
                    dataMap.put(acknum, joInfo);
                } else {
                    String req = Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_POINT_JOIN + Const.REQ_SIGN.toLowerCase();
                    String res = Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_POINT_JOIN + Const.RES_SIGN.toLowerCase();
                    // 响应第一段
                    if (strContent.startsWith(Const.HTTP_SIGN)) {
                        //根据响应的seq_number获取请求信息
                        if (dataMap.containsKey(data.getSeq_number())) {
                            String resstatuscode = strContent.split("\r\n")[0].split(" ")[1];
                            JSONObject reqJson = dataMap.get(data.getSeq_number());
                            String seqnumber = data.getSeq_number();
                            String interfaceuri = reqJson.get("interfaceuri").toString();
                            String sourceip = data.getSourceip();
                            String sourceport = data.getSourceport();
                            String desip = data.getDesip();
                            String desport = data.getDesport();
                            //String resstatus = "";
                            String rescontent = strContent;
                            joInfo.put("seqnumber", seqnumber);
                            joInfo.put("interfaceuri", interfaceuri);
                            joInfo.put("sourceip", sourceip);
                            joInfo.put("sourceport", sourceport);
                            joInfo.put("desip", desip);
                            joInfo.put("desport", desport);
                            joInfo.put("endtime", data.getTime_s());
                            joInfo.put("resstatuscode", resstatuscode);
                            joInfo.put("rescontent", rescontent);
                            joType.put("@checktime", checktime);
                            joType.put(req, reqJson);
                            joType.put(res, joInfo);
                            dataMap.remove(seqnumber);

                            // 接口传输map
                            if (interfaceTransfer) {
                                if (!interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                    httpJsonMap.put(data.getAck_number(), joType);
                                }
                            }

                            // 文件传输map
                            if (fileTransfer) {
                                if (!filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                }
                                // 文件传输实际字节大小
                                if (realityByteMap.containsKey(data.getAck_number())) {
                                    Integer len = realityByteMap.get(data.getAck_number());
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length + len);
                                } else {
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length);
                                }
                                // 组装多部分传输字节数组
                                if (fileByteMap.containsKey(data.getAck_number())) {
                                    byte[] bytes = fileByteMap.get(data.getAck_number());
                                    byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                    System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                    System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                    fileByteMap.put(data.getAck_number(), newByte);
                                } else {
                                    fileByteMap.put(data.getAck_number(), data.getContent_byte());
                                }
                            }
                        }
                    } else {
                        /** 响应多部分 多部分传输 多个响应ack相同
                         根据响应的文件多部分ack_number获取字节
                         接口传输保存结果 */
                        if (interfaceTransfer) {
                            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN) &&
                                    interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                JSONObject reqJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String rescontent = resJson.getString("rescontent");
                                StringBuilder rescontentBuilder = new StringBuilder();
                                rescontentBuilder.append(rescontent).append(data.getContent());
                                resJson.put("rescontent", rescontentBuilder.toString());
                                JSONObject joResult = new JSONObject(true);
                                joResult.put(req, reqJson);
                                joResult.put(res, resJson);
                                joResult.put("@checktime", checktime);
                                httpJsonMap.put(data.getAck_number(), joResult);
                            }
                        }

                        //文件传输
                        if (fileTransfer) {
                            int contentLen = 0;
                            int realityLen = 0;
                            String downFileName = "";

                            // 文件传输实际字节大小
                            if (realityByteMap.containsKey(data.getAck_number())) {
                                Integer len = realityByteMap.get(data.getAck_number());
                                contentLen = data.getContent_byte().length + len;
                                realityByteMap.put(data.getAck_number(), contentLen);
                            }

                            // 组装多部分传输字节数组
                            if (fileByteMap.containsKey(data.getAck_number())) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                fileByteMap.put(data.getAck_number(), newByte);
                            }

                            if (transferByteMap.containsKey(data.getAck_number())) {
                                String[] filenameAndSize = transferByteMap.get(data.getAck_number()).split(Const.AUDIT_SPLIT_JOIN);
                                if (filenameAndSize.length == 2) {
                                    downFileName = filenameAndSize[0];
                                    realityLen = Integer.parseInt(filenameAndSize[1]);
                                }

                            }

                            // 判断拼起来的包长度是否大于文件字节真实长度
                            if (contentLen > realityLen) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                // 开始索引
                                int startIndex = contentLen - realityLen;
                                byte[] fileByte = new byte[realityLen];
                                System.arraycopy(bytes, startIndex, fileByte, 0, fileByte.length);
                                FileUtils.getFileByBytes(fileByte, httprestoreFilePath, downFileName);
                                // 读取文件内容转成str
                                String fileContent = getFileContent(httprestoreFilePath, downFileName);
                                JSONObject reqJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String interfaceuri = reqJson.get("interfaceuri").toString();
                                String resstatuscode = resJson.get("resstatuscode").toString();
                                String rescontent = resJson.get("rescontent").toString();
                                String seqnumber = resJson.get("seqnumber").toString();
                                String sourceip = data.getSourceip();
                                String sourceport = data.getSourceport();
                                String desip = data.getDesip();
                                String desport = data.getDesport();
                                //String resstatus = "";
                                joInfo.put("seqnumber", seqnumber);
                                joInfo.put("interfaceuri", interfaceuri);
                                joInfo.put("sourceip", sourceip);
                                joInfo.put("sourceport", sourceport);
                                joInfo.put("desip", desip);
                                joInfo.put("desport", desport);
                                joInfo.put("endtime", data.getTime_s());
                                joInfo.put("resstatuscode", resstatuscode);
                                rescontent = rescontent.split("\r\n\r\n")[0] + "\r\n\r\n";
                                joInfo.put("rescontent", rescontent + fileContent);
                                joType.put("@checktime", checktime);
                                joType.put(req, reqJson);
                                joType.put(res, joInfo);
                                filetransferMap.remove(data.getAck_number() + Const.REQ_SIGN);
                                filetransferMap.remove(data.getAck_number() + Const.RES_SIGN);
                                httpJsonMap.put(data.getAck_number(), joType);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 清洗HTTP流量
     *
     * <AUTHOR>
     * @date 2020-11-05
     */
    public static void cleanHttpFlow(Packet data, String pcapCleanAfterPath, String filename, String resotreFilePath) {
        JSONObject joInfo = new JSONObject(true);
        JSONObject joType = new JSONObject(true);

        String strContent = data.getContent();
        // 响应第一段、文件传输响应多部分、接口传输响应多部分
        if (strContent.contains(Const.HTTP_SIGN) ||
                filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN) ||
                interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {

            // '\r\n\r\n'截取请求头
            String[] dataArr = strContent.split("\r\n\r\n")[0].split("\r\n");
            boolean type = false;
            boolean interfaceTransfer = true;
            boolean fileTransfer = false;
            for (String s : dataArr) {
                // 判断请求头
                if (s.startsWith("Accept") && !s.startsWith("Accept-")) {
                    type = true;
                    continue;

                }
                // 判断响应头
                if (s.startsWith("Content-Type")) {
                    if (ParamUtil.checkContentTypeIsTxt(s)) {
                        type = true;
                        // 判断多部分文件传输
                        if (s.contains("application/octet-stream")) {
                            fileTransfer = true;
                            interfaceTransfer = false;
                        }
                        continue;
                    }
                }
                // 判断响应头 文件传输
                if (s.startsWith("Content-Disposition:")) {
                    type = true;
                    String[] options = s.split(":")[1].split(";");
                    String downFileName = "";
                    for (String option : options) {
                        if (option.contains("filename")) {
                            downFileName = option.split("=")[1].replaceAll("\\\"", "").trim();
                        }
                    }
                    // 文件名-文件字节大小
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String contentTypeSize = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), downFileName);
                    }
                }
                // 文件名-文件字节大小
                if (fileTransfer == true && s.startsWith("Content-Length")) {
                    String contentTypeSize = s.split(":")[1].trim();
                    if (transferByteMap.containsKey(data.getAck_number())) {
                        String downFileName = transferByteMap.get(data.getAck_number());
                        transferByteMap.put(data.getAck_number(), downFileName + Const.AUDIT_SPLIT_JOIN + contentTypeSize);
                    } else {
                        transferByteMap.put(data.getAck_number(), contentTypeSize);
                    }
                }
            }
            // 判断接口是否多部分传输
            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
            }
            // 判断文件是否多部分传输
            if (filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                type = true;
                fileTransfer = true;
                interfaceTransfer = false;
            }
            if (type) {
                String[] s = strContent.split("GET|POST|PUT|DELETE");
                if (s.length == 2) {
                    //请求
                    String reqmethod = strContent.split("\r\n")[0].split(" ")[0];
                    String[] http = s[1].split(Const.HTTP_SIGN);
                    String acknum = data.getAck_number();
//                    String interfaceuri = http[0].split("\\?")[0].trim();
                    String interfaceuri = http[0].trim();
                    String sourceip = data.getSourceip();
                    String sourceport = data.getSourceport();
                    String desip = data.getDesip();
                    String desport = data.getDesport();
                    String reqcontent = strContent;
                    joInfo.put("seqnumber", acknum);
                    joInfo.put("interfaceuri", interfaceuri);
                    joInfo.put("sourceip", sourceip);
                    joInfo.put("sourceport", sourceport);
                    joInfo.put("desip", desip);
                    joInfo.put("desport", desport);
                    joInfo.put("reqmethod", reqmethod);
                    joInfo.put("starttime", data.getTime_s());
                    joInfo.put("reqcontent", reqcontent);
                    dataMap.put(acknum, joInfo);
                } else {
                    // 响应第一段
                    if (strContent.startsWith(Const.HTTP_SIGN)) {
                        //根据响应的seq_number获取请求信息
                        if (dataMap.containsKey(data.getSeq_number())) {
                            String resstatuscode = strContent.split("\r\n")[0].split(" ")[1];
                            JSONObject reqJson = dataMap.get(data.getSeq_number());
                            String seqnumber = data.getSeq_number();
                            String interfaceuri = reqJson.get("interfaceuri").toString();
                            String sourceip = data.getSourceip();
                            String sourceport = data.getSourceport();
                            String desip = data.getDesip();
                            String desport = data.getDesport();
                            //String resstatus = "";
                            String rescontent = strContent;
                            //支持类型 xml, html, jsonp
                            if (FileTools.isHtml(rescontent)) {
                                rescontent = FileTools.isHtmlJsonTransfer(rescontent);
                            } else if (FileTools.isXML(rescontent)) {
                                rescontent = FileTools.isXmlJsonTransfer(rescontent);
                            } else if (FileTools.isJsonp(rescontent)) {
                                rescontent = FileTools.isJsonpTransfer(rescontent);
                            }
                            joInfo.put("seqnumber", seqnumber);
                            joInfo.put("interfaceuri", interfaceuri);
                            joInfo.put("sourceip", sourceip);
                            joInfo.put("sourceport", sourceport);
                            joInfo.put("desip", desip);
                            joInfo.put("desport", desport);
                            joInfo.put("endtime", data.getTime_s());
                            joInfo.put("resstatuscode", resstatuscode);
                            joInfo.put("rescontent", rescontent);
                            joType.put("req", reqJson);
                            joType.put("res", joInfo);
                            dataMap.remove(seqnumber);

                            // 接口传输map
                            if (interfaceTransfer) {
                                if (!interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    interfaceTransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                    httpJsonMap.put(data.getAck_number(), joType);
                                }
                            }

                            // 文件传输map
                            if (fileTransfer) {
                                if (!filetransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN, reqJson);
                                    filetransferMap.put(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN, joInfo);
                                }
                                // 文件传输实际字节大小
                                if (realityByteMap.containsKey(data.getAck_number())) {
                                    Integer len = realityByteMap.get(data.getAck_number());
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length + len);
                                } else {
                                    realityByteMap.put(data.getAck_number(), data.getContent_byte().length);
                                }
                                // 组装多部分传输字节数组
                                if (fileByteMap.containsKey(data.getAck_number())) {
                                    byte[] bytes = fileByteMap.get(data.getAck_number());
                                    byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                    System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                    System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                    fileByteMap.put(data.getAck_number(), newByte);
                                } else {
                                    fileByteMap.put(data.getAck_number(), data.getContent_byte());
                                }
                            }
                        }
                    } else {
                        /** 响应多部分 多部分传输 多个响应ack相同
                         根据响应的文件多部分ack_number获取字节
                         接口传输保存结果 */
                        if (interfaceTransfer) {
                            if (interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN) &&
                                    interfaceTransferMap.containsKey(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN)) {
                                JSONObject reqJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = interfaceTransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String rescontent = resJson.getString("rescontent");
                                StringBuilder rescontentBuilder = new StringBuilder();
                                rescontentBuilder.append(rescontent).append(data.getContent());
                                resJson.put("rescontent", rescontentBuilder.toString());
                                JSONObject joResult = new JSONObject(true);
                                joResult.put("req", reqJson);
                                joResult.put("res", resJson);
                                httpJsonMap.put(data.getAck_number(), joResult);
                            }
                        }

                        //文件传输
                        if (fileTransfer) {
                            int contentLen = 0;
                            int realityLen = 0;
                            String downFileName = "";

                            // 文件传输实际字节大小
                            if (realityByteMap.containsKey(data.getAck_number())) {
                                Integer len = realityByteMap.get(data.getAck_number());
                                contentLen = data.getContent_byte().length + len;
                                realityByteMap.put(data.getAck_number(), contentLen);
                            }

                            // 组装多部分传输字节数组
                            if (fileByteMap.containsKey(data.getAck_number())) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                byte[] newByte = new byte[bytes.length + data.getContent_byte().length];
                                System.arraycopy(bytes, 0, newByte, 0, bytes.length);
                                System.arraycopy(data.getContent_byte(), 0, newByte, bytes.length, data.getContent_byte().length);
                                fileByteMap.put(data.getAck_number(), newByte);
                            }

                            if (transferByteMap.containsKey(data.getAck_number())) {
                                String[] filenameAndSize = transferByteMap.get(data.getAck_number()).split(Const.AUDIT_SPLIT_JOIN);
                                if (filenameAndSize.length == 2) {
                                    downFileName = filenameAndSize[0];
                                    realityLen = Integer.parseInt(filenameAndSize[1]);
                                }

                            }

                            // 判断拼起来的包长度是否大于文件字节真实长度
                            if (contentLen > realityLen) {
                                byte[] bytes = fileByteMap.get(data.getAck_number());
                                // 开始索引
                                int startIndex = contentLen - realityLen;
                                byte[] fileByte = new byte[realityLen];
                                System.arraycopy(bytes, startIndex, fileByte, 0, fileByte.length);
                                FileUtils.getFileByBytes(fileByte, resotreFilePath, downFileName);
                                // 读取文件内容转成str
                                String fileContent = getFileContent(resotreFilePath, downFileName);
                                JSONObject reqJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.REQ_SIGN);
                                JSONObject resJson = filetransferMap.get(data.getAck_number() + Const.AUDIT_SPLIT_JOIN + Const.RES_SIGN);
                                String interfaceuri = reqJson.get("interfaceuri").toString();
                                String resstatuscode = resJson.get("resstatuscode").toString();
                                String rescontent = resJson.get("rescontent").toString();
                                String seqnumber = resJson.get("seqnumber").toString();
                                String sourceip = data.getSourceip();
                                String sourceport = data.getSourceport();
                                String desip = data.getDesip();
                                String desport = data.getDesport();
                                //String resstatus = "";
                                joInfo.put("seqnumber", seqnumber);
                                joInfo.put("interfaceuri", interfaceuri);
                                joInfo.put("sourceip", sourceip);
                                joInfo.put("sourceport", sourceport);
                                joInfo.put("desip", desip);
                                joInfo.put("desport", desport);
                                joInfo.put("endtime", data.getTime_s());
                                joInfo.put("resstatuscode", resstatuscode);
                                rescontent = rescontent.split("\r\n\r\n")[0] + "\r\n\r\n";
                                joInfo.put("rescontent", rescontent + fileContent);
                                joType.put("req", reqJson);
                                joType.put("res", joInfo);
                                filetransferMap.remove(data.getAck_number() + Const.REQ_SIGN);
                                filetransferMap.remove(data.getAck_number() + Const.RES_SIGN);
                                httpJsonMap.put(data.getAck_number(), joType);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 读取文件获取文件内容
     *
     * @param path
     * @param downFileName
     * @return
     */
    private static String getFileContent(String path, String downFileName) {
        String filePath = path + File.separator + downFileName;
        File file = new File(filePath);
        try {
            log.info("开始对" + filePath + "内容进行读取");
            if (downFileName.endsWith("txt") || !downFileName.contains(".")) {
                String txtContent = FileUtils.getTxtStr(file);
                return txtContent;
            } else if (downFileName.endsWith("xls")) {
                List<String> list = POIUtil.readExcel(file, "");
                String xlsContent = Joiner.on(",").join(list);
                return xlsContent;
            } else if (downFileName.endsWith("xlsx")) {
                List xlsxDataList = null;
                if (file.length() > 5242880) {
                    xlsxDataList = ExcelReader.readerExcel(filePath);
                } else {
                    xlsxDataList = POIUtil.readExcel(file, "");
                }
                String xlsxContent = Joiner.on(",").join(xlsxDataList);
                return xlsxContent;
            } else if (downFileName.endsWith("csv")) {
                List<String> list = CsvReader.readCsv(filePath);
                String csvContent = Joiner.on(",").join(list);
                return csvContent;
            } else {
                log.info("文件格式暂不支持处理" + filePath);
                return null;
            }
        } catch (Exception e) {
            log.info("读取文件内容出现异常：" + filePath);
            e.printStackTrace();
        }
        return null;
    }


    /**
     * @Decription 追加流量内容到文件
     * <AUTHOR>
     * @date 2020-11-05
     */
    public static void append2File(String strLine, String path) {
        // 判断文件夹是否存在
        String[] paths = path.split(Matcher.quoteReplacement(File.separator));
        StringBuilder fullPath = new StringBuilder();
        for (int i = 0; i < paths.length; i++) {
            fullPath.append(paths[i]).append(File.separator);
            File file = new File(fullPath.toString());
            if (paths.length - 1 != i) {
                if (!file.exists()) {
                    file.mkdir();
                }
            }
        }
        FileOutputStream fos;
        try {
            fos = new FileOutputStream(path, true);
            fos.write((strLine + "\n").getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.info("清洗数据保存:" + path + "出现异常!");
        }
    }

    /**
     * @Decription 追加流量内容(和ES集群格式一致)到文件
     * <AUTHOR>
     * @date 2022-03-21
     */
    public static void append3File(String strLine, String path) {
        // 判断文件夹是否存在
        String[] paths = path.split(Matcher.quoteReplacement(File.separator));
        StringBuilder fullPath = new StringBuilder();
        for (int i = 0; i < paths.length; i++) {
            fullPath.append(paths[i]).append(File.separator);
            File file = new File(fullPath.toString());
            if (paths.length - 1 != i) {
                if (!file.exists()) {
                    file.mkdir();
                }
            }
        }
        FileOutputStream fos;
        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();

        JSONObject jobj = null;
        String cleaningContent = null;

        //TODO  <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
        try {
            //获取字符串的值
            jobj = JSONObject.parseObject(strLine);

        } catch (Exception e) {
            log.info("Json转换出现问题" + e);
        }
        //获取响应数据
        JSONObject resObject = jobj.getJSONObject("res");
        if (resObject != null) {
            String resstatus = resObject.getString("resstatuscode");  //服务端错误
            if (resstatus.startsWith(Const.API_STATUSCODE_5PREFIX)) {
                apiCallNetFlow.setResstatus(Const.SERVER_ERROR_STATUS);
            } else if (resstatus.startsWith(Const.API_STATUSCODE_4PREFIX)) {
                apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);
            } else if (resstatus.startsWith(Const.API_STATUSCODE_1PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_2PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_3PREFIX)) {
                apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS); //响应状态   服务端
                apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS); //请求状态   客户端
            }
            //从响应字符串中获取接口响应参数值
            String rescontent = IcLogCheckTaskClean.getResponseParaValue(resObject.getString("rescontent"));
            DataRescontent dataRescontent = new DataRescontent();
            //金华场景乱码问题响应内容返回乱码
            if (rescontent.contains("code")) {
                dataRescontent.setData(rescontent);
            }
            apiCallNetFlow.setRescontent(dataRescontent); //响应内容

        }
        //获取请求数据
        JSONObject reqObject = jobj.getJSONObject("req");
        if (reqObject != null) {
            //从请求字符串中获取reqmethod
            String reqmethod = IcLogCheckTaskClean.getReqmethodParaValue(reqObject.getString("interfaceuri"));
            apiCallNetFlow.setReqmethod(reqmethod); //请求方法
            String interfaceuri = reqObject.getString("interfaceuri");
            apiCallNetFlow.setApiuri(interfaceuri); //接口路径
            apiCallNetFlow.setClientip(reqObject.getString("sourceip")); //客户端IP
            apiCallNetFlow.setApiip(reqObject.getString("desip"));  //接口IP
            apiCallNetFlow.setApiport(reqObject.getString("desport")); //接口服务端口
            try {
                apiCallNetFlow.setApicode(MD5Util.encrypt(interfaceuri)); //接口编码
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            //从请求字符串中获取接口请求参数值
            String reqcontent = IcLogCheckTaskClean.getRequestParaValue(reqObject.getString("reqcontent") == null ? "" : reqObject.getString("reqcontent"));
            Params params = new Params();
            params.setParams(reqcontent);
            apiCallNetFlow.setReqcontent(params); //请求内容
            Integer starttime = reqObject.getInteger("starttime");
            Date date = TimeUtils.stampForDate(starttime);
            String callTime = TimeUtils.dateForString(date);
            apiCallNetFlow.setCalltime(callTime); //请求时间
        }

        apiCallNetFlow.setSystem("共享交换平台"); //系统   // TODO
        apiCallNetFlow.setModule(""); //模块      // TODO
        apiCallNetFlow.setClientmac(""); //客户端MAC  // TODO
        apiCallNetFlow.setAccount(""); //账号       // TODO
        apiCallNetFlow.setDatatype("netflow"); //数据类型  == 固定死的  // TODO
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime()); //清洗时间 当前时间

        cleaningContent = JSON.toJSONString(apiCallNetFlow);

        try {
            fos = new FileOutputStream(path, true);
            fos.write((cleaningContent + "\n").getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.info("清洗数据保存:" + path + "出现异常!");
        }
    }


    /**
     * 追加流量内容到文件(网关代理适配)
     *
     * @param strLine str线
     * @param path    路径
     */
    public static void append2FileByProxy(String strLine, String path) {
        // 判断文件夹是否存在
        String[] paths = path.split(Matcher.quoteReplacement(File.separator));
        StringBuilder fullPath = new StringBuilder();
        for (int i = 0; i < paths.length; i++) {
            fullPath.append(paths[i]).append(File.separator);
            File file = new File(fullPath.toString());
            if (paths.length - 1 != i) {
                if (!file.exists()) {
                    file.mkdir();
                }
            }
        }
        FileOutputStream fos;
        try {
            fos = new FileOutputStream(path, true);
            fos.write((strLine + "\n").getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.info("清洗数据保存:" + path + "出现异常!");
        }
    }

    public static void reverseByteArray(byte[] arr) {
        byte temp;
        int n = arr.length;
        for (int i = 0; i < n / 2; i++) {
            temp = arr[i];
            arr[i] = arr[n - 1 - i];
            arr[n - 1 - i] = temp;
        }
    }

    public static int byteArrayToInt(byte[] b, int offset) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }


    private static short byteArrayToShort(byte[] b, int offset) {
        short value = 0;
        for (int i = 0; i < 2; i++) {
            int shift = (2 - 1 - i) * 8;
            value += (b[i + offset] & 0x000000FF) << shift;
        }
        return value;
    }


    /**
     * 字节数据转字符串处理中文乱码
     *
     * @param content
     * @param encode
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String content2Str(byte[] content, String encode) throws UnsupportedEncodingException {
        return new String(content, encode);
    }

    /**
     * FTP客户端数据传输端口计算
     *
     * @param token
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private static int getPort(String token) {
        String[] portCommand = token.replaceAll("[()]\\.", "").split(",");
        int port = 0;
        if (portCommand.length == 6) {
            port = (Integer.parseInt(portCommand[4]) * 256) + Integer.parseInt(portCommand[5].replaceAll("\r\n", ""));
        }
        return port;
    }

    /**
     * 清洗数组中空字符串
     *
     * @param datas
     * <AUTHOR>
     * @date 2020-11-02
     */
    public static ArrayList<String> convertArrToList(String[] datas) {
        ArrayList<String> list = new ArrayList<>();
        for (String data : datas) {
            if (!data.equals("")) {
                list.add(data);
            }
        }
        return list;
    }

    private static String appendZero(String str) {
        if (str.length() == 1) {
            return "0" + str;
        } else {
            return str;
        }
    }

    public static void main(String[] args) {
//        File file = new File("F:\\pcap\\pcapcleanafterpath\\20201125\\HTTP\\BDCSC2.0接口检测结果概要统计报表_20201125104842.xlsx");
//        byte[] bytesByFile = getBytesByFile("F:\\pcap\\pcapcleanafterpath\\20201125\\HTTP\\BDCSC2.0接口检测结果概要统计报表_20201125104842.xlsx");
        byte[] bytesByFile = {80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 100, 111, 99, 80, 114, 111, 112, 115, 47, 99, 111, 114, 101, 46, 120, 109, 108, -83, -111, -51, 74, -60, 48, 20, 70, -9, 125, -118, -112, 125, -101, -92, 29, 101, 8, 109, 7, 81, 6, 4, -59, 1, 43, -118, -69, -112, 94, -37, 98, -13, 67, 18, -19, -8, -10, 102, 58, 99, 69, 113, -23, -14, -26, 59, -33, -31, 114, 83, 110, -10, 106, 68, -17, -32, -4, 96, 116, -123, 89, 70, 49, 2, 45, 77, 59, -24, -82, -62, 15, -51, 54, 93, -29, 77, -99, 36, -91, 52, 14, 118, -50, 88, 112, 97, 0, -113, 98, 75, -5, 10, -9, 33, 88, 78, -120, -105, 61, 40, -31, -77, 24, -21, -104, -68, 24, -89, 68, -120, -93, -21, -120, 21, -14, 85, 116, 64, 114, 74, -49, -119, -126, 32, 90, 17, 4, 57, -40, 82, -69, -24, -16, -47, -57, -91, -3, 119, 101, 43, 23, -91, 125, 115, -29, 44, 104, 37, -127, 17, 20, -24, -32, 9, -53, 24, -7, 102, 3, 56, -27, -1, 44, -52, -55, 66, -18, -3, -80, 80, -45, 52, 101, 83, 49, 115, 113, 35, 70, -98, 110, 111, -18, -25, -27, -45, 65, -5, 32, -76, 4, 92, 39, 8, -107, 39, 59, -105, 14, 68, -128, 22, 69, 7, 15, 31, 22, 42, -4, -107, 60, 22, -105, 87, -51, 22, -41, 57, -51, 105, -54, 88, -102, -97, 53, 52, -25, -85, 53, 95, 21, -49, 37, -7, -43, 63, 57, -113, -93, 113, -11, 69, 60, 75, 15, 104, 119, 119, 125, 64, -105, -25, -92, 36, 63, -65, -82, 78, 62, 1, 80, 75, 7, 8, 73, 32, 81, -79, 5, 1, 0, 0, -12, 1, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 95, 114, 101, 108, 115, 47, 46, 114, 101, 108, 115, -83, -110, -63, 78, -61, 48, 12, -122, -17, 125, -118, 40, -9, -43, -35, -112, 16, 66, 77, 119, 65, 72, -69, 77, 104, 60, 64, 72, -36, 54, 106, 19, 71, -119, 7, -27, -19, 9, 7, 4, 67, 12, 118, -32, 24, -25, -9, -25, 79, -78, -37, -19, -30, 103, -15, -116, 41, 59, 10, 74, -82, -21, 70, 10, 12, -122, -84, 11, -125, -110, -113, -121, -5, -43, -115, -36, 118, 85, -43, 62, -32, -84, -71, 100, -14, -24, 98, 22, -91, 41, 100, 37, 71, -26, 120, 11, -112, -51, -120, 94, -25, -102, 34, -122, -14, -45, 83, -14, -102, -53, 51, 13, 16, -75, -103, -12, -128, -80, 105, -102, 107, 72, 95, 25, -78, -85, -124, 56, -63, -118, -99, 85, 50, -19, -20, 90, -118, -61, 107, -60, 75, -16, -44, -9, -50, -32, 29, -103, -93, -57, -64, 63, 76, -7, -106, 40, 100, -99, 6, 100, 37, -105, 25, 94, 40, 77, 79, 68, 83, 93, -96, 18, -50, -22, 108, -2, 83, 7, 23, -58, 96, -47, -82, 98, 42, -3, -119, 29, -26, 79, 39, 75, 102, 95, -54, 25, 116, -116, 127, 72, 93, 93, 46, 117, 126, 5, -32, -111, -75, -43, -84, -63, 80, -62, -33, -107, -34, 19, 31, 78, 45, -100, 92, 67, 87, -67, 1, 80, 75, 7, 8, 87, 40, 94, 35, -29, 0, 0, 0, 70, 2, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 91, 67, 111, 110, 116, 101, 110, 116, 95, 84, 121, 112, 101, 115, 93, 46, 120, 109, 108, -83, -109, -53, 78, -61, 48, 16, 69, -9, -7, 10, -53, 91, 20, -69, 101, -127, 16, 74, -46, 5, -113, 37, 84, -94, 124, -128, -79, 39, -115, 85, -65, 100, -69, -91, -3, 123, 38, 41, -27, 37, 90, -118, -24, -54, -78, -26, -34, 123, 110, 70, 113, 53, 89, 91, 67, 86, 16, -109, -10, -82, -90, 99, 54, -94, 4, -100, -12, 74, -69, 121, 77, -97, 102, 119, -27, 37, -99, 52, 69, 81, -51, 54, 1, 18, 65, -79, 75, 53, -19, 114, 14, 87, -100, 39, -39, -127, 21, -119, -7, 0, 14, 39, -83, -113, 86, 100, -68, -58, 57, 15, 66, 46, -60, 28, -8, -7, 104, 116, -63, -91, 119, 25, 92, 46, 115, -97, 65, -101, -126, -112, -22, 6, 90, -79, 52, -103, -36, -82, 113, -78, 101, 71, 48, -119, -110, -21, -83, -74, -57, -43, 84, -124, 96, -76, 20, 25, -25, 124, -27, -44, 55, 80, -7, 6, 97, -24, 28, 52, -87, -45, 33, -99, -95, -128, -14, 125, -112, 126, -72, -97, -15, 97, 125, -64, -107, 68, -83, -128, 76, 69, -52, -9, -62, -94, -112, 43, 47, -89, -47, -121, -60, -47, -62, 14, 7, -3, 80, -42, -73, -83, -106, -128, 25, 75, -117, 22, 6, 125, 39, 5, -86, 12, 24, 9, 49, 107, -8, -36, -4, 32, 94, -6, 8, 127, -25, -17, -106, -43, -69, -113, -121, -82, 13, 79, -99, -120, -96, 30, 115, -60, 95, 34, -3, -5, -69, 83, -120, 32, 84, -22, 0, -78, 53, -20, 75, -10, 17, 85, -14, -58, -64, -55, 59, 12, -95, -65, -61, 95, 124, 92, 60, 123, -65, 56, -7, 10, -16, 100, 86, 104, 119, 92, -123, 65, -97, -8, 112, -116, 79, -36, -27, 61, 127, 87, -91, -30, -61, -69, 111, -118, 87, 80, 75, 7, 8, 37, -32, 8, -50, 56, 1, 0, 0, 40, 4, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 100, 111, 99, 80, 114, 111, 112, 115, 47, 97, 112, 112, 46, 120, 109, 108, 77, -114, -63, 10, -62, 48, 16, 68, -17, 126, 69, -56, -67, -35, -22, 65, 68, -46, -108, -126, 8, -98, -20, 65, 63, 32, -92, -37, 54, -48, 108, 66, -78, 74, 63, -33, -100, -44, -29, -52, 48, -113, -89, -70, -51, -81, -30, -115, 41, -69, 64, -83, -36, -41, -115, 20, 72, 54, -116, -114, -26, 86, 62, 31, -41, -22, 36, 59, -67, 83, 67, 10, 17, 19, 59, -52, -94, 28, 40, -73, 114, 97, -114, 103, -128, 108, 23, -12, 38, -41, 101, -90, -78, 76, 33, 121, -61, 37, -90, 25, -62, 52, 57, -117, -105, 96, 95, 30, -119, -31, -48, 52, 71, -64, -115, -111, 70, 28, -85, -8, 5, 74, -83, -6, 24, 87, 103, 13, 23, 7, -35, 71, 83, -112, 98, -72, -33, 20, -4, -9, 10, 126, 14, -6, 3, 80, 75, 7, 8, -31, 124, 119, -40, -111, 0, 0, 0, -73, 0, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 120, 108, 47, 115, 104, 97, 114, 101, 100, 83, 116, 114, 105, 110, 103, 115, 46, 120, 109, 108, 61, -116, 65, 14, -62, 32, 16, 0, -17, -66, -126, -20, -35, 46, 122, 48, -58, -108, -10, 96, -30, 11, -12, 1, -124, -82, -123, -92, 44, -108, 5, -29, -13, -27, -28, 113, 50, -109, 25, -25, 111, -36, -44, -121, -118, -124, -60, 6, 78, -125, 6, 69, -20, -46, 18, 120, 53, -16, 122, 62, -114, 87, -104, -89, -61, 40, 82, -107, 75, -115, -85, -127, 94, 52, 14, 123, -93, -5, -97, -5, -125, -59, -128, -81, 53, -33, 16, -59, 121, -118, 86, -122, -108, -119, -69, 121, -89, 18, 109, -19, 88, 86, -108, 92, -56, 46, -30, -119, 106, -36, -16, -84, -11, 5, -93, 13, 12, 56, -3, 0, 80, 75, 7, 8, 112, -65, -40, 38, 120, 0, 0, 0, -119, 0, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 120, 108, 47, 115, 116, 121, 108, 101, 115, 46, 120, 109, 108, -99, -109, -63, 78, -61, 48, 12, -122, -17, 60, 69, -108, 59, -21, -42, 3, -102, 80, -37, 29, -112, -122, 56, 51, 36, -82, 89, -21, -74, 17, -119, 93, 37, 25, 106, -9, -12, 56, 77, 7, 67, 2, 33, 113, 105, -20, 63, -10, 103, 91, 78, -117, -35, 104, -115, 120, 7, -25, 53, 97, 41, 55, -85, -75, 20, -128, 53, 53, 26, -69, 82, -66, 28, -10, -73, 91, -71, -85, 110, 10, 31, 38, 3, -49, 61, 64, 16, -100, -128, -66, -108, 125, 8, -61, 125, -106, -7, -70, 7, -85, -4, -118, 6, 64, -66, 105, -55, 89, 21, -40, 117, 93, -26, 7, 7, -86, -15, 49, -55, -102, 44, 95, -81, -17, 50, -85, 52, -54, -86, -64, -109, -35, -37, -32, 69, 77, 39, 12, -91, 92, -53, -84, 42, 90, -62, 47, 37, -105, 73, -88, 10, 127, 22, -17, -54, 112, 103, -79, 53, 14, -85, -55, -112, 19, 26, 27, 24, -95, 41, -27, 54, 106, -88, 44, -92, -88, 7, 101, -12, -47, -23, -103, -89, -84, 54, 83, -110, -13, 40, -52, -99, 46, 113, 86, 35, -71, 40, 102, -87, 74, -6, -2, -56, -7, 108, 32, 79, 13, 28, -109, 27, -36, 9, -82, 0, -13, -31, 25, -92, -115, -7, 62, 5, 11, 85, 49, -88, 16, -64, -31, -98, 29, -79, -40, -121, 105, -128, 82, 34, -31, -126, -103, -29, -2, -120, 110, -108, 123, 123, 116, 106, -70, -54, -104, 15, 46, 124, 36, -41, -16, 22, 47, -91, 55, -14, 34, 85, -123, -127, 54, 112, -126, -45, 93, 31, -49, 64, 67, 28, -125, 66, 32, -53, 70, -93, 85, 71, -88, 76, 68, 94, 50, 22, -125, -79, 53, 24, -13, 28, 119, -1, -38, 126, 99, -113, -83, 72, 75, 124, 106, -30, -2, 68, 28, -1, 98, 114, 67, -117, -103, 48, -55, -119, -4, 107, 90, 98, 95, 97, -13, 127, 97, -59, -40, 126, -14, 127, -53, -34, -4, -99, 45, -44, 48, -104, 105, 79, -79, -111, 121, -75, 85, -63, 79, -96, 67, 11, 24, 68, 79, 78, -97, -7, 42, -18, -67, 102, 1, -46, -45, 25, -37, 101, -92, 121, -102, -20, -21, 23, -87, 62, 0, 80, 75, 7, 8, 70, -61, 101, -35, 126, 1, 0, 0, 86, 3, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 120, 108, 47, 119, 111, 114, 107, 98, 111, 111, 107, 46, 120, 109, 108, -115, -114, 49, 75, -61, 64, 24, -122, 119, 127, -59, -15, -19, -26, -46, 42, -94, -91, -105, -126, 45, 66, 55, -63, -22, 126, 77, -66, 52, 71, 115, 119, -31, -18, 108, 29, -43, 81, 65, 7, 87, 81, 74, 105, -23, -28, -26, -44, -33, 99, -102, -97, 97, -110, 82, 117, 116, -6, 120, 121, 31, -98, -17, 109, 119, 110, 100, 74, 38, 104, -84, -48, -118, 65, -61, -13, -127, -96, 10, 117, 36, -44, -120, -63, -27, -32, 108, -1, 24, 58, -63, 94, 123, -86, -51, 120, -88, -11, -104, -108, -72, -78, 12, 18, -25, -78, 22, -91, 54, 76, 80, 114, -21, -23, 12, 85, -39, -60, -38, 72, -18, -54, 104, 70, -44, 102, 6, 121, 100, 19, 68, 39, 83, -38, -12, -3, 35, 42, -71, 80, -80, 53, -76, -52, 127, 28, 58, -114, 69, -120, 61, 29, 94, 75, 84, 110, 43, 49, -104, 114, 87, -114, -75, -119, -56, 44, 4, 63, -53, -50, 13, -119, -72, -61, -58, -119, 127, -56, 32, -26, -87, 69, -96, 65, -69, 106, -82, 4, 78, -19, 47, 88, 69, -62, 67, 39, 38, 56, -32, 67, 6, 126, -59, -47, 63, 96, -67, 121, 119, -119, -30, 18, 25, -100, -10, -70, 23, -35, -90, -25, -25, 79, -117, -81, -25, 121, 62, -65, -51, 63, 31, 55, -21, -105, -4, -19, 53, 95, -34, 23, -53, -69, -51, -6, -67, -8, -104, -27, 15, -117, 98, -74, 2, 98, 90, 34, 98, 96, -6, -47, 1, -112, -38, -46, 47, 99, -93, -2, -77, -109, -45, -35, -100, -32, 27, 80, 75, 7, 8, -56, 40, -57, -14, 19, 1, 0, 0, -125, 1, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 120, 108, 47, 95, 114, 101, 108, 115, 47, 119, 111, 114, 107, 98, 111, 111, 107, 46, 120, 109, 108, 46, 114, 101, 108, 115, -83, -111, -63, 78, -61, 48, 12, 64, -17, -3, -118, -56, 119, -102, 118, -109, 16, 66, 77, 119, 65, 72, -69, -78, -15, 1, 81, -22, 54, -43, -38, 36, -78, 13, -37, -2, -98, -128, 4, -84, 18, 8, 14, 59, 89, -74, -29, -25, -105, -92, -39, -100, -26, 73, -67, 34, -15, 24, -125, -127, -70, -84, 64, 97, 112, -79, 27, -61, 96, -32, 121, -1, 120, 115, 7, -101, -74, 40, -102, 39, -100, -84, -28, 51, -20, -57, -60, 42, 15, 5, 54, -32, 69, -46, -67, -42, -20, 60, -50, -106, -53, -104, 48, -28, 78, 31, 105, -74, -110, 83, 26, 116, -78, -18, 96, 7, -44, -85, -86, -70, -43, 116, -55, -128, -74, 80, 106, -127, 85, -37, -50, 0, 109, -69, 26, -44, -2, -100, -16, 63, -8, -40, -9, -93, -61, -121, -24, 94, 102, 12, -14, -61, 22, -51, -34, 18, 118, 59, -95, 124, 33, -50, 96, 75, 3, -118, -127, 69, -71, -52, 84, -48, -65, -6, -84, -82, -22, 35, -25, 9, 47, 69, 62, -14, 63, 12, -42, -41, 52, 56, 70, 58, -80, 71, -108, 111, -119, -81, -46, -5, 123, -27, 80, 127, -6, 52, 122, -15, -17, 109, -15, 6, 80, 75, 7, 8, -16, -50, 88, -122, -44, 0, 0, 0, 48, 2, 0, 0, 80, 75, 3, 4, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 120, 108, 47, 119, 111, 114, 107, 115, 104, 101, 101, 116, 115, 47, 115, 104, 101, 101, 116, 49, 46, 120, 109, 108, -83, -100, 75, 111, -29, 54, 20, 70, -9, -3, 21, -126, 11, 116, -105, -104, 111, 73, -98, 36, -125, 38, -58, -96, 93, 20, 40, -102, 62, -106, 3, -57, 81, 18, 99, 98, 59, -80, 53, 73, 127, 126, 73, -7, 17, -103, -71, -9, -14, -118, -24, 34, -29, -57, -112, -6, -88, 35, -117, 58, 36, 45, 95, 124, -2, 119, -7, 92, -68, 54, -101, -19, 98, -67, -70, 28, -55, 115, 49, 42, -102, -43, 124, 125, -65, 88, 61, 94, -114, -2, -6, -13, -53, 89, 53, -6, 124, -11, -61, -59, -37, 122, -13, 109, -5, -44, 52, 109, -31, -53, -81, -74, -105, -93, -89, -74, 125, -103, -116, -57, -37, -7, 83, -77, -100, 109, -49, -41, 47, -51, -54, -1, -49, -61, 122, -77, -100, -75, -2, -27, -26, 113, -68, 125, -39, 52, -77, -5, -82, -46, -14, 121, -84, -124, 112, -29, -27, 108, -79, 26, 93, 93, -36, 47, -106, -51, 42, 4, 22, -101, -26, -31, 114, -12, -77, 28, -115, -81, 46, -70, -126, 127, 47, -102, -73, 109, -17, 121, 17, 114, -17, -42, -21, 111, -31, -59, -81, -9, -105, 35, -33, -68, 118, 118, 119, -37, 60, 55, -13, -74, -15, -81, -37, -51, -9, 38, -44, 30, 127, -88, -2, -91, 107, -54, -17, -101, -30, -66, 121, -104, 125, 127, 110, -1, 88, -65, -3, -46, 44, 30, -97, 90, -65, -105, -42, -17, -90, -81, 52, 95, 63, 111, -69, 127, -117, -27, 34, -20, -4, -88, 88, -50, -2, -19, 30, -33, 22, -9, -19, -45, -27, 72, -55, -13, -86, -76, -93, 98, -2, 125, -37, -82, -105, -1, -20, -34, 60, 68, 30, -21, -87, 125, 61, 117, -84, 39, -85, -13, 116, 53, -67, -81, -90, 7, -58, -103, 125, 61, -61, -85, 55, -34, -19, 101, -57, 100, 58, 107, 103, -2, 96, 110, -42, 111, -59, 38, -20, -89, 127, 62, 15, -49, -4, 17, 40, -74, -35, -114, 123, 58, -117, -43, -13, 98, -43, -36, -74, 27, 127, -92, 22, -66, 102, 123, 117, 61, -67, -71, -67, 81, -25, -30, -89, 31, -107, -75, -94, -2, -28, 31, -91, 41, 109, 120, 116, -107, 116, -31, -79, -84, -53, -16, -66, 86, -58, -107, -35, -5, 86, -103, -18, 125, 33, 69, 120, -33, 42, 33, 119, -1, -65, 43, 103, 75, -45, -43, -73, -54, -22, -16, -38, -44, 74, 124, -70, 24, -73, -66, -67, 33, 115, 60, -9, 127, -66, -103, -17, -115, 85, -121, -35, 59, 28, -60, 110, -1, -118, -94, 59, -98, -75, 63, -98, -57, 125, 81, -28, -66, 124, 108, -77, 18, 74, -18, -10, 73, -6, -74, -100, -74, -95, -37, -30, 53, -71, -59, 112, 66, 76, -74, 47, -77, 121, 115, 57, -14, -97, -8, 109, -77, 121, 109, 70, 87, 5, -112, 19, -77, 51, 78, -17, 24, -44, -82, -126, 114, 111, -2, -81, -36, -80, -3, -16, 104, -84, -23, -27, 106, 83, -43, -118, -54, -97, 14, 38, -23, -92, 8, 91, -44, -107, -82, -30, 45, -98, 30, 77, 77, 29, 77, -41, 63, -102, 26, 73, -9, -3, -50, -4, -37, 109, -24, 70, -38, -59, 107, 115, -77, 94, -75, -51, -86, -3, -22, -113, -91, -112, 82, -6, 63, 35, -123, -128, -114, -27, 110, 123, -95, 23, 122, -67, -14, -67, -35, -59, -8, -75, 79, 28, 75, -13, 27, -125, -74, 54, -59, -54, -121, 118, -100, 73, 121, 38, 69, 33, -27, -60, 72, -118, -123, 97, -77, 48, -125, 89, -120, 74, 85, 56, 11, 67, -78, -64, -46, 48, 22, 88, -7, -114, -123, -88, -50, 84, -107, 102, 97, -39, 44, -20, 112, 22, 78, 105, -100, -123, 37, 89, 96, 105, 24, 11, -84, -4, -114, -123, 59, 83, 58, -51, -62, -79, 89, -72, 12, 22, -78, -58, 89, 56, -110, 5, -106, -122, -79, -64, -54, 31, 88, -56, 58, -51, -94, 100, -77, 40, 115, 88, -8, 115, 68, -106, 6, 100, 81, -110, 44, -80, 52, -116, 5, 86, -2, -56, -94, 59, 71, 100, 73, -79, -88, -40, 44, -86, -31, 44, -62, 117, -70, 84, 10, -2, 92, 84, 125, 22, -30, 3, 12, 44, 46, -64, 0, 105, 96, 21, 118, 52, -20, -103, -14, -67, 103, 57, 81, -118, -94, 81, -77, 105, -44, -71, 52, -32, -42, 95, -41, 52, 13, 44, 14, -91, -127, 85, -120, 104, 8, -118, -122, 20, 108, 28, -95, 104, 22, 15, 89, -63, 60, -10, 27, 68, -127, -96, -127, 40, 17, -76, -58, 41, 18, 89, -111, 72, 36, 31, 9, -90, 58, 73, 36, 14, 65, 34, 19, 72, -80, 64, 28, 9, 86, 35, 66, -30, 72, 36, -92, 76, -97, 34, 81, -71, 72, 12, -126, 68, 37, -112, 96, -127, 56, 18, -84, 70, -124, -60, -112, 72, -8, 70, 42, -121, 43, 105, -121, -60, -40, 26, 65, -94, 19, 72, 40, 43, -123, -111, -112, 94, -70, 71, 98, 38, -74, 38, -111, -16, -59, 84, 102, -104, -87, 21, 126, -16, -128, 93, 105, -92, 73, 32, -95, -28, 20, 70, 66, -21, -87, -11, -122, 90, 72, -105, -72, -40, 72, -66, -97, -54, 12, 65, 13, 3, 23, 63, 72, 4, -127, -40, 4, 16, -54, 80, 97, 32, -76, -93, -18, -58, 46, 118, 18, 55, 39, 2, -62, -105, 84, -103, 97, -87, 29, 16, 99, 65, 32, 46, 1, -124, -46, 84, 24, 8, 45, -86, 7, 32, 113, 115, 34, 32, 124, 83, -107, 25, -86, -38, 1, -47, 26, 4, 66, -101, 42, -102, -122, -87, 42, 90, -31, 20, 71, -36, -104, 8, 7, 95, 86, 101, -122, -83, 118, 56, 84, 9, -30, -88, 104, 28, -108, -84, -126, 56, 104, 89, 61, -32, -120, 27, 19, -31, -32, -37, -86, -52, -48, -43, 110, -30, 35, -18, -63, -10, 56, 106, 26, 7, 101, -85, 32, 14, -38, 86, 15, 51, 31, 116, 119, -86, -8, -74, -86, -122, -38, 106, 24, -33, -86, -32, -86, 18, -62, -95, -6, -82, -86, 62, -32, 64, -45, 36, 4, 119, -118, 22, 15, -51, 8, -45, 64, -86, 44, 100, 53, -119, -101, 18, -63, -32, 123, -86, 26, -22, -87, 123, 24, -91, -83, 64, 24, -110, -122, -127, -91, 57, -112, 5, 46, -88, -17, 44, -54, 73, -36, -110, -120, 5, 95, 80, -43, 80, 65, 61, -80, 48, 48, -117, -66, -98, -22, -113, 44, 80, 59, -123, 63, 24, -72, -102, -10, 97, -60, 77, -119, 96, -16, -43, 84, 13, 85, -45, 35, 12, -24, 88, 94, 43, 77, -61, 64, -67, 20, -122, -127, 75, -23, 9, 12, 114, -24, -94, -8, 82, -86, -122, 74, -23, 30, -122, 67, 62, 25, -122, -122, -127, 26, 41, 12, 3, -41, -47, 119, 24, 46, -11, -55, -32, -21, -88, 26, -86, -93, 71, 24, -96, 125, 41, 75, -61, 64, 109, 20, -122, -127, -85, -24, 9, 12, -46, -68, 20, 95, 69, -43, 80, 21, 61, -64, -128, -51, 75, 57, 26, 6, 106, -94, 48, 12, 92, 67, -5, 48, 104, -17, 82, 124, 13, 85, -104, -25, -35, -51, -74, -117, -7, -41, 14, -55, -35, -67, -22, 81, 80, -16, 71, -126, -10, 79, 52, 6, -66, -118, -32, -14, -39, -121, 16, -73, 36, -126, -64, -105, 79, -123, -39, 29, 6, 1, -111, 112, 69, 91, 39, 26, 3, 67, -64, -107, -13, 29, 66, -54, -64, 21, 95, 57, -43, 80, -27, 60, -80, -120, 39, -32, -10, 44, 106, -6, -76, 64, -107, 19, 62, 45, 112, -31, -20, -61, -96, -25, 2, 53, 95, 56, 117, -90, 112, -38, 120, -14, 126, -65, -20, 40, 72, 24, 104, 26, 12, 3, 45, 30, -63, 32, 7, 35, -102, 47, -100, 26, -109, 58, -12, -12, -112, 96, 31, -95, 37, 121, 122, -96, 49, -32, -23, -127, -106, -114, 32, -112, 125, -124, -26, -101, -90, -58, 100, 14, -121, 0, -10, 17, 90, -47, 16, -80, 24, 24, 2, -57, 48, 61, 4, -78, -113, -48, 3, -106, -29, 49, -119, -61, 32, 24, 120, 120, -82, 19, -21, -16, 88, 12, 12, -127, 99, -106, 38, 49, 54, -41, 124, -77, -44, -103, 102, -87, -31, 37, 70, -35, 55, -53, -113, -29, 47, 52, 13, -23, 27, 56, 102, -87, 39, 113, 83, 34, 24, 124, -77, -44, -103, 102, -23, 105, -128, 48, 44, 13, 99, -104, 89, -94, -59, -5, 48, -28, 36, 110, 74, 4, -125, 111, -106, 58, -45, 44, -91, 2, 7, 96, -38, -47, 48, -122, -103, 37, 90, -4, 20, 70, -36, -108, 8, 6, -33, 44, -11, -48, 9, -50, 35, 12, 112, 10, 75, -105, 52, -116, 65, -126, -119, -106, -114, 88, -112, -13, 87, -102, 47, -104, 122, -24, -20, -26, -127, 5, -20, 86, -70, -94, 89, 12, -14, 76, -76, -12, 41, -117, -124, 90, -15, 61, 83, 103, 122, -90, -116, -105, -16, -10, 44, 106, -102, 5, -106, 6, -77, -32, 104, -90, 76, 44, 38, 26, -66, 102, -102, 76, -51, -108, 2, 116, 11, 67, -49, 107, -94, 105, 32, 11, -76, -12, 41, -117, -72, 37, 17, 11, -66, 101, -102, -52, 105, 77, 97, 65, -59, 48, -12, -76, 38, -102, 6, 125, -56, -90, 104, -23, 62, 11, 49, -119, 91, 18, -79, -32, -53, -90, -55, -100, -42, 20, 22, -68, -112, 24, 69, -77, -64, -46, 96, 22, 28, -25, -12, 44, -56, -21, -120, -31, 59, -89, -55, -100, -43, 20, -16, 68, -98, -47, 52, 11, 44, 13, 102, -63, 81, 79, -111, -104, -57, 51, 3, -66, 2, -102, -91, -98, 78, 90, 13, -97, 35, -76, 122, -94, 105, 48, -117, -92, 121, -70, 110, -66, -126, 62, 71, -8, -26, 105, -78, -52, 51, -80, 0, -81, 35, -122, 54, 79, 52, 13, 102, -111, 20, -49, 29, 11, -6, 58, -62, 23, 79, -109, 37, -98, -98, -123, -86, 65, 22, -12, 87, 64, -47, 52, -48, 59, -15, -46, 39, 44, -30, -106, 68, 44, -8, -34, 105, -78, -68, 51, -80, -128, -5, 11, 122, 98, 19, 77, -125, 89, 36, -67, 115, -57, -126, -18, 47, -8, -34, 105, -78, -68, -45, 97, -85, -22, -122, -10, 78, 52, 13, 102, -111, -12, 78, -105, 94, 82, 55, 124, -17, 52, 89, -34, 25, 88, -128, -109, 89, -122, 94, 82, 71, -45, -96, 5, -23, 41, 90, 58, 98, 65, 78, 102, 89, -66, 119, -38, 44, -17, -12, 44, 4, 120, -114, 88, 65, -78, 64, -45, 64, 22, 104, -23, 83, 22, 113, 75, 34, 22, 124, -17, -76, 89, -34, 25, 88, -128, -29, 84, 75, 79, 114, -94, 105, 48, -117, -92, 119, -18, 88, -112, -29, 84, -53, -9, 78, -101, -27, -99, 46, 124, -71, 17, 100, 65, -49, 117, -94, 105, 48, -117, -92, 119, -70, -12, -73, 26, 45, -33, 59, 109, -106, 119, 6, 22, -96, -125, 91, 122, -54, 19, 77, -125, 89, 36, -67, 115, -57, -126, 116, 112, -53, -9, 78, -101, -23, -99, 6, 94, 63, -74, -12, -83, 71, 104, 26, -52, -126, -29, -99, 38, -79, 124, 108, 7, -36, 122, -108, -23, -99, -58, -64, -3, 69, -30, -42, 35, 116, -58, 19, 100, -63, -15, 78, -49, -126, -18, 47, -8, -34, 105, 51, -67, -45, 104, -8, 58, 66, 123, 39, -102, 6, -77, -32, 120, -89, -103, -60, 45, -119, 88, -16, -67, -45, 102, 122, -89, -47, 112, 127, 65, 123, 39, -102, 6, -77, -32, 120, -89, 103, 65, -9, 23, 124, -17, -76, -103, -34, 105, -32, -79, -103, -91, -41, -43, -47, 52, -104, 5, -57, 59, 77, 98, 108, 102, -7, -34, 105, 51, -67, -45, 104, -72, -65, -96, -67, 19, 77, -125, 89, 112, -68, -45, -77, 32, -5, 11, -57, -9, 78, -105, -23, -99, 70, -61, 119, 42, -46, -34, -119, -90, -127, 44, -48, -46, 17, 11, 114, -27, -52, -15, -67, -45, 101, 122, 39, -78, -92, -22, 104, -17, 68, -45, 96, 22, 28, -17, 76, 45, -87, 58, -66, 119, -70, 92, -17, 84, -32, -9, 123, 29, -19, -99, 104, 26, -52, -126, -25, -99, 113, 75, 34, 22, 124, -17, 116, -103, -34, -87, -29, 123, 104, -9, 44, 104, -17, 68, -45, 96, 22, 28, -17, -44, -87, 59, -101, -7, -34, -23, 50, -67, 83, 90, 112, 125, -60, -47, -34, -119, -90, -63, 44, 56, -34, 41, 39, 113, 75, 34, 22, 124, -17, 116, 57, -34, 41, 42, -20, 58, -30, 104, -17, 68, -45, 32, -80, 83, -76, -12, -127, 69, -72, -67, 42, 121, 29, 25, 112, -53, 123, -114, 119, 6, 22, 2, -18, 59, 19, -73, -68, 99, 105, 48, -117, -108, 119, -18, 88, -60, 45, -119, 88, -16, -67, -45, -27, 120, 103, -57, 2, 28, -101, 57, -38, 59, -47, 52, -104, 69, -54, 59, -9, 44, -56, -79, -103, -29, 123, -89, -53, -15, 78, -49, 66, -61, -73, 70, -72, 19, -17, -84, 62, -62, 64, -59, 83, 42, 112, -76, 55, 69, 107, -12, -127, 104, -20, -2, -120, -15, -5, -49, -8, 92, 44, -101, -51, 99, 115, -45, 60, -121, 95, -9, 57, 62, 63, -4, -94, -46, 100, -38, -3, -88, -46, -72, 95, -24, 101, -10, -40, -4, 54, -37, 60, 46, 86, -37, -30, 110, -35, 122, -100, -105, 35, -47, -3, 80, -47, -61, 122, -35, 54, -101, -16, -54, 119, -17, 79, -51, -20, -2, -8, -30, -71, 121, 104, -69, 82, -93, 98, -77, 67, -33, 61, 111, -41, 47, -5, -70, 33, -28, -8, -29, 80, 87, -1, 1, 80, 75, 7, 8, -72, 61, -112, -24, 24, 9, 0, 0, 79, 74, 0, 0, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 73, 32, 81, -79, 5, 1, 0, 0, -12, 1, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 111, 99, 80, 114, 111, 112, 115, 47, 99, 111, 114, 101, 46, 120, 109, 108, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 87, 40, 94, 35, -29, 0, 0, 0, 70, 2, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 1, 0, 0, 95, 114, 101, 108, 115, 47, 46, 114, 101, 108, 115, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 37, -32, 8, -50, 56, 1, 0, 0, 40, 4, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96, 2, 0, 0, 91, 67, 111, 110, 116, 101, 110, 116, 95, 84, 121, 112, 101, 115, 93, 46, 120, 109, 108, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, -31, 124, 119, -40, -111, 0, 0, 0, -73, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -39, 3, 0, 0, 100, 111, 99, 80, 114, 111, 112, 115, 47, 97, 112, 112, 46, 120, 109, 108, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 112, -65, -40, 38, 120, 0, 0, 0, -119, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -88, 4, 0, 0, 120, 108, 47, 115, 104, 97, 114, 101, 100, 83, 116, 114, 105, 110, 103, 115, 46, 120, 109, 108, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, 70, -61, 101, -35, 126, 1, 0, 0, 86, 3, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 98, 5, 0, 0, 120, 108, 47, 115, 116, 121, 108, 101, 115, 46, 120, 109, 108, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, -56, 40, -57, -14, 19, 1, 0, 0, -125, 1, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 7, 0, 0, 120, 108, 47, 119, 111, 114, 107, 98, 111, 111, 107, 46, 120, 109, 108, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, -16, -50, 88, -122, -44, 0, 0, 0, 48, 2, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 8, 0, 0, 120, 108, 47, 95, 114, 101, 108, 115, 47, 119, 111, 114, 107, 98, 111, 111, 107, 46, 120, 109, 108, 46, 114, 101, 108, 115, 80, 75, 1, 2, 20, 0, 20, 0, 8, 8, 8, 0, 21, 86, 121, 81, -72, 61, -112, -24, 24, 9, 0, 0, 79, 74, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -121, 9, 0, 0, 120, 108, 47, 119, 111, 114, 107, 115, 104, 101, 101, 116, 115, 47, 115, 104, 101, 101, 116, 49, 46, 120, 109, 108, 80, 75, 5, 6, 0, 0, 0, 0, 9, 0, 9, 0, 63, 2, 0, 0, -27, 18, 0, 0, 0, 0};

        System.out.println(Arrays.toString(bytesByFile));
        System.out.println(bytesByFile.length);

        FileUtils.getFileByBytes(bytesByFile, "F:\\pcap\\pcapcleanafterpath\\20201125\\HTTP", "aa.xlsx");


        String strContent = "GET /_nodes/stats HTTP/1.1\n" +
                "Host: **************:9200\n" +
                "Connection: keep-alive\n" +
                "Authorization: Basic ZWxhc3RpYzp3enNlY0AyMDIy\n" +
                "Accept: application/json, text/javascript, */*; q=0.01\n" +
                "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n" +
                "Content-Type: application/json\n" +
                "Accept-Encoding: gzip, deflate\n" +
                "Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7";
//        whetherAccessIsUnauthorized(new Packet(),strContent);
    }
}
