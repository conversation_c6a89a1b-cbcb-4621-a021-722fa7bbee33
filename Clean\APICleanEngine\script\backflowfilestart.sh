#!/bin/sh
     dateTime=`date -d yesterday +%Y_%m_%d`  #pcap  (今天日期)
     dateYesDir=`date -d yesterday +%Y%m%d`  #pcap文件时间目录(昨天日期) 
     dateTomDir=`date -d tomorrow +%Y%m%d`   #pcap文件时间目录(明天日期)
     days=1    #删除1天前的备份数据
     bakData=$dateTime.tar.gz   #备份文件名
     bakSrcDir=/flowdata   #需要备份的文件路径
     bakDescDir=/flowdata/backup  #备份文件到该路径(backup路径需要存在)
     remotePath=/FlowFile/pcaptmp   #远程服务器的路径
     
     mkdir $bakSrcDir/$dateTomDir
     cd $bakDescDir    #进入备份目录
     tar -zcvf $bakData $bakSrcDir/$dateYesDir   #压缩备份文件
    
     rm -rf $bakSrcDir/$dateYesDir
     find $bakDescDir -type f -name "*.tar.gz" -mtime +$days -exec rm -rf {} \; #删除1天前的备份（注意：{} \中间有空格）
     /data/software/tcp/sshpass-1.06/sshpass -p "<EMAIL>.wzsec02" scp  $bakDescDir/$bakData root@*************:$remotePath  #上传到远程服务器(需指定sshpass绝对路径,服务器IP、用户名及密码)
