package com.wzsec.clean.common.rule;


import com.wzsec.clean.common.utils.Const;

import java.util.Map;

public class ProRuleFactory {

    public static boolean checkDataByProRule(Map<String, String> sensitivedataMap, String strData, String prule) {
        // 敏感数据管理配置
        if (sensitivedataMap.containsKey(Const.MOBILENUMBER_SIGN)) {
            if (prule.equalsIgnoreCase("P_MOBILENUMBER")) // 通过程序检测移动手机号码
                return ProRule.p_checkMobileNumber(strData);
            else if (prule.equalsIgnoreCase("P_UNICOMNUMBER"))// 通过程序检测联通手机号码
                return ProRule.p_checkUnicomNumber(strData);
            else if (prule.equalsIgnoreCase("P_TELECOMNUMBER"))// 通过程序检测电信手机号码
                return ProRule.p_checkTelecomNumber(strData);
        }
        if (sensitivedataMap.containsKey(Const.ADDRESS_SIGN)) {
            if (prule.equalsIgnoreCase("P_ADDRESS")) // 通过程序检测地址
                return ProRule.p_checkAddress(strData);
        }
        if (sensitivedataMap.containsKey(Const.IDENTITY_SIGN)) {
            if (prule.equalsIgnoreCase("P_IDCODE"))// 通过程序检测身份证号
                return ProRule.p_checkIdcode(strData);
        }
        if (sensitivedataMap.containsKey(Const.EMAIL_SIGN)) {
            if (prule.equalsIgnoreCase("P_EMAIL")) // 通过程序检测邮箱
                return ProRule.p_checkEmail(strData);
        }
        if (sensitivedataMap.containsKey(Const.AES_SIGN)) {
            if (prule.equalsIgnoreCase("P_AES"))// 通过程序检测AES
                return ProRule.p_checkAES(strData);
        }
        if (sensitivedataMap.containsKey(Const.DES_SIGN)) {
            if (prule.equalsIgnoreCase("P_3DES"))// 通过程序检测3DES
                return ProRule.p_check3DES(strData);
        }
        if (sensitivedataMap.containsKey(Const.MD5_SIGN)) {
            if (prule.equalsIgnoreCase("P_MD5"))// 通过程序检测 MD5
                return ProRule.p_checkMD5(strData);
        }
        if (sensitivedataMap.containsKey(Const.SHA256_SIGN)) {
            if (prule.equalsIgnoreCase("P_SHA256"))// 通过程序检测 sha256
                return ProRule.p_checkSHA256(strData);
        }
        if (sensitivedataMap.containsKey(Const.FIXEDLINE_SIGN)) {
            if (prule.equalsIgnoreCase("P_FIXPHONE"))// 通过程序检测固话
                return ProRule.p_checkFixphone(strData);
        }
        if (sensitivedataMap.containsKey(Const.NAME_SIGN)) {
            if (prule.equalsIgnoreCase("P_NAME")) // 通过程序检测姓名
                return ProRule.p_checkName(strData);
        }
        if (sensitivedataMap.containsKey(Const.IMSI_SIGN)) {
            if (prule.equalsIgnoreCase("P_IMSI")) // 通过程序检测IMSI
                return ProRule.p_checkIMSI(strData);
        }
        if (sensitivedataMap.containsKey(Const.IMEI_SIGN)) {
            if (prule.equalsIgnoreCase("P_IMEI")) // 通过程序检测IMEI
                return ProRule.p_checkIMEI(strData);
        }
        if (sensitivedataMap.containsKey(Const.MEID_SIGN)) {
            if (prule.equalsIgnoreCase("P_MEID")) // 通过程序检测MEID
                return ProRule.p_checkMEID(strData);
        }
        return false;
    }


    public static boolean checkDataByProRule(String strData, String prule) {
        switch (prule.toUpperCase()) {
            case "P_PHONENUMBER":
                return ProRule.p_checkPhoneNumber(strData);
            case "P_MOBILENUMBER":
                return ProRule.p_checkMobileNumber(strData);
            case "P_UNICOMNUMBER":
                return ProRule.p_checkUnicomNumber(strData);
            case "P_TELECOMNUMBER":
                return ProRule.p_checkTelecomNumber(strData);
            case "P_FIXPHONE":
                return ProRule.p_checkFixphone(strData);
            case "P_IDCODE":
                return ProRule.p_checkIdcode(strData);
            case "P_ADDRESS":
                return ProRule.p_checkAddress(strData);
            case "P_NAME":
                return ProRule.p_checkName(strData);
            case "P_LONGITUDEANDLATITUDE":
                return ProRule.p_checkLongitudeAndLatitude(strData);
            case "P_EMAIL":
                return ProRule.p_checkEmail(strData);
            case "P_AES":
                return ProRule.p_checkAES(strData);
            case "P_3DES":
                return ProRule.p_check3DES(strData);
            case "P_MD5":
                return ProRule.p_checkMD5(strData);
            case "P_SHA256":
                return ProRule.p_checkSHA256(strData);
            case "P_CHECKIMEI":
                return ProRule.p_checkIMEI(strData);
            case "P_CHECKIMSI":
                return ProRule.p_checkIMSI(strData);
            case "P_CHECKMEID":
                return ProRule.p_checkMEID(strData);
            case "P_CHECKACCOUNTOPENINGPERMITNO":
                return ProRule.p_checkAccountOpeningPermitNo(strData);
            case "P_CHECKTAXATIONNO":
                return ProRule.p_checkTaxationNo(strData);
            case "P_CHECKHMPASSCHECK":
                return ProRule.p_checkHMPassCheck(strData);
            case "P_CHECKOFFICERCARD":
                return ProRule.p_checkOfficerCard(strData);
            case "P_CHECKPASSPORTCARD":
                return ProRule.p_checkPassPortCard(strData);
            case "P_CHECKUNIFIEDCREDITCODE":
                return ProRule.p_checkUnifiedCreditCode(strData);
            case "P_CHECKBUSINESSLICENSE":
                return ProRule.p_checkBusinesslicense(strData);
            case "P_CHECKBANKCARD":
                return ProRule.p_checkBankCard(strData);
            case "P_CHECKIP":
                return ProRule.p_checkIP(strData);
            case "P_CHECKCARNUMBERNO":
                return ProRule.p_checkCarnumberNO(strData);
            case "P_CHECKVALIDENTPCODE":
                return ProRule.p_checkValidEntpCode(strData);
            case "P_CHECKPROVINCES":
                return ProRule.p_checkProvinces(strData);
            case "P_CHECKNATIONAL":
                return ProRule.p_checkNational(strData);
            case "P_CHECKENTERPRISENAME":
                return ProRule.p_checkEnterpriseName(strData);
            case "P_CHECKQQ":
                return ProRule.p_checkQQ(strData);
            case "P_CHECKVIN":
                return ProRule.p_checkVIN(strData);
            case "P_CHECKIPV6":
                return ProRule.p_checkIPv6(strData);
            case "P_CHECKVINCODE":
                return ProRule.p_checkVin(strData);
            case "P_CHECKICCID":
                return ProRule.p_checkICCID(strData);
            case "P_CHECKMSISDN":
                return ProRule.p_checkMSISDN(strData);
            default:
                return false;
        }
    }

}
