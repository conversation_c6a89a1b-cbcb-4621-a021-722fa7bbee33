/**
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 * <p>
 * [2015] - [2020] China Telecom Corporation Limited,
 * All Rights Reserved.
 * <p>
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 *
 * @Title: POIUtil.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年11月16日
 */
/**
 * @Title: POIUtil.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年11月16日
 */

package com.wzsec.clean.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.record.crypto.Biff8EncryptionKey;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.crypt.Decryptor;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import static org.apache.poi.ss.usermodel.CellType.NUMERIC;
import static org.apache.poi.ss.usermodel.CellType.STRING;

/**
 * <AUTHOR>
 * @ClassName: POIUtil
 * @Description: excel读写工具类
 * @date 2018年11月16日
 */
@Slf4j
public class POIUtil {
    private final static String xls = "xls";
    private final static String xlsx = "xlsx";

    /**
     * 读入excel文件，解析后返回
     *
     * @param file
     * @throws Exception
     */
    public static List<String> readExcel(File file, String pwd) throws Exception {
        //检查文件
        //checkFile(file);
        //获得Workbook工作薄对象
        Workbook workbook = getWorkBook(file, pwd);
        //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
        List<String> list = new ArrayList<String>();
        if (workbook != null) {
            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
                //获得当前sheet工作表
                Sheet sheet = workbook.getSheetAt(sheetNum);
                if (sheet == null) {
                    continue;
                }
                //获得当前sheet的开始行
                int firstRowNum = sheet.getFirstRowNum();
                //获得当前sheet的结束行
                int lastRowNum = sheet.getLastRowNum();
                //循环所有行
                for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
                    //获得当前行
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    //获得当前行的开始列
                    int firstCellNum = row.getFirstCellNum();
                    //获得当前行的最后一列
                    int LastCellNum = row.getLastCellNum();
                    //循环当前行
                    for (int cellNum = firstCellNum; cellNum < LastCellNum; cellNum++) {
                        Cell cell = row.getCell(cellNum);
                        if (cell != null) {
                            String str = getCellValue(cell);
                            for (String s : str.split("。|，|,| |；|;|：|\t|-|=|:")) {
                                if (!"".equals(s.trim())) {
                                    list.add(s.trim());
                                }
                            }
                        }
                    }
                }
            }
            try {
                workbook.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return list;
    }

    /**
     * 判断文件是否是excel文件
     *
     * @param file
     */
    public static void checkFile(File file) {
        //判断文件是否存在
        if (null == file) {
            log.error("文件不存在！");
            try {
                throw new FileNotFoundException("文件不存在！");
            } catch (FileNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        //获得文件名
        String fileName = file.getName();
        //判断文件是否是excel文件
        if (!fileName.endsWith(xls) && !fileName.endsWith(xlsx)) {
            log.error(fileName + "不是excel文件");
            try {
                throw new IOException(fileName + "不是excel文件");
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }
//    /**
//     * 创建Workbook工作薄对象
//     * @param file
//     * @throws Exception
//     */
//	public static Workbook getWorkBook(File file) throws IOException {
//		// 创建Workbook工作薄对象，表示整个excel
//		Workbook workbook = null;
//		FileInputStream is = null;
//		// 获取excel文件的io流
//		is = new FileInputStream(file);
//		// 根据程序判断excel是2007以上版本还是2003版本获得不同的Workbook实现类对象
//		if (OfficeUtil.isOffice2007(file.getAbsolutePath())) {
//			// 2007
//			workbook = new XSSFWorkbook(is);
//		} else {
//			// 2003
//			workbook = new HSSFWorkbook(is);
//		}
//		return workbook;
//	}

    /**
     * 创建Workbook工作薄对象
     *
     * @param file
     * @throws Exception
     */
    public static Workbook getWorkBook(File file, String pwd) throws Exception {
        // 创建Workbook工作薄对象，表示整个excel
        Workbook workbook = null;
        InputStream is = null;
        // 获取excel文件的io流
        try {
            is = new FileInputStream(file);
            // 1.不加密
            // 根据程序判断excel是2007以上版本还是2003版本获得不同的Workbook实现类对象
            if (OfficeUtil.isOffice2007(file.getAbsolutePath())) {
                // 2007
                workbook = new XSSFWorkbook(is);
            } else {
                // 2003
                workbook = new HSSFWorkbook(is);
            }
            is.close();
        } catch (Exception e) {
            // 2.加密
            POIFSFileSystem pfs = null;
            try {
                is = new FileInputStream(file);
                // 2007
                pfs = new POIFSFileSystem(is);
                EncryptionInfo encInfo = new EncryptionInfo(pfs);
                Decryptor decryptor = Decryptor.getInstance(encInfo);
                if (decryptor.verifyPassword(pwd)) {
                    log.info(file + "为2007版本，Excel解密成功");
                    workbook = new XSSFWorkbook(decryptor.getDataStream(pfs));
                } else {
                    throw new RuntimeException("解析xlsx文件失败,解密密码错误");
                }
                is.close();
            } catch (RuntimeException e1) {
                throw new RuntimeException(e1.getMessage());
            } catch (Exception e1) {
                try {
                    is = new FileInputStream(file);
                    // 2003
                    pfs = new POIFSFileSystem(is);
                    Biff8EncryptionKey.setCurrentUserPassword(pwd);
                    workbook = new HSSFWorkbook(pfs);
                    log.info(file + "为2003版本，Excel解密成功");
                    is.close();
                } catch (Exception e2) {
                    log.info(file + "为2003版本，Excel解密失败");
                    throw new RuntimeException("解析xls文件失败,不支持2003版本加密的Excel文件");
                }
            }
        } finally {
            try {
                if (is != null)
                    is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return workbook;
    }

    /**
     * 获取数据类型
     */
    public static String getCellValue(Cell cell) {
        String cellValue = "";
        if (cell == null) {
            return cellValue;
        }
        //把数字当成String来读，避免出现1读成1.0的情况
        if (cell.getCellType() == NUMERIC) {
            cell.setCellType(STRING);
        }
        //判断数据的类型
        switch (cell.getCellType()) {
            case NUMERIC: //数字
                cellValue = String.valueOf(cell.getNumericCellValue());
                break;
            case STRING: //字符串
                cellValue = String.valueOf(cell.getStringCellValue());
                break;
            case BOOLEAN: //Boolean
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA: //公式
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            case BLANK: //空值
                cellValue = "";
                break;
            case ERROR: //故障
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }
        return cellValue;
    }
}
