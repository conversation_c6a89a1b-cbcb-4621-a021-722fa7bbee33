package com.wzsec.clean.common.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class FileWriterUtil {

    /**
     * 将字符串增量写入到指定文件路径，每条字符串占一行
     * @param filePath 文件路径
     * @param line 要写入的字符串
     * @throws IOException 如果写入失败
     */
    public static void appendLineToFile(String filePath, String line) throws IOException {
        File file = new File(filePath);

        // 确保父目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 添加换行符
        String lineWithNewline = line + "\n";

        // 追加写入文件
        try (FileOutputStream fos = new FileOutputStream(file, true)) { // true 表示追加模式
            fos.write(lineWithNewline.getBytes(StandardCharsets.UTF_8));
        }
    }
}