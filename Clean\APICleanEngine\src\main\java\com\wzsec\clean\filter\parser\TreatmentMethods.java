package com.wzsec.clean.filter.parser;

import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.filter.clean.networksession.NetworkSessionAudit;
import com.wzsec.clean.filter.clean.networksession.NetworkSessionStatistics;
import com.wzsec.clean.modules.model.*;
import lombok.extern.slf4j.Slf4j;
import org.pcap4j.packet.IpV4Packet;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 流量文件处理方式
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Slf4j
public class TreatmentMethods {

    private static String fileRecognitionStatus = ConfigurationManager.getProperty("apifilerecognition.status").trim();

    private static String encryptedpcapStatus = ConfigurationManager.getProperty("netflow.encryptedpcap.status").trim();

    private static String networksessionpcapStatus = ConfigurationManager.getProperty("netflow.networksessionpcap.status").trim();

    /**
     * 磁盘读取流量文件处理
     *
     * @param file       文件
     * @param threadName 线程名
     * @throws IOException ioexception
     */
    public static void checkPcapFromDisk(File file, String threadName,
                                         Integer cleanSum,
                                         List<ApiCallNetFlow> esDataList,
                                         String waterMarkInfo,
                                         int waterLineSpacing,
                                         Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                         List<String> apicodeList,
                                         Map<String, FlowCombination> textFileMap,
                                         Map<String, PcapFlowCombination> pcapFlowCombinationMap,
                                         List<String> interfaceDiscoveryList,
                                         String esIndexTime,
                                         Accumulator accumulator,
                                         List<String> cleanAPIServiceList,
                                         Map<String, AccountLoginRecord> accountLoginRecordMap,
                                         List<ApiEncryptedtraffic> apiEncryptedtrafficList) throws Exception {

        String cleanoutputtype = ConfigurationManager.getProperty("netflow.cleanoutputtype").trim(); //流量清洗输出类型
        String collectdatatype = ConfigurationManager.getProperty("netflow.collectdatatype").trim();  //采集的流量类型

        String afterPath = concatenatedOutputPath(file);

        // TODO 清洗,获取完整请求响应包 ,获取 Map<String, PcapFlowCombination> pcapFlowCombinationMap
        GeneralClean.packetReadCleaning(String.valueOf(file), pcapFlowCombinationMap, apiEncryptedtrafficList);

        if (!pcapFlowCombinationMap.isEmpty()) {
            for (String key : pcapFlowCombinationMap.keySet()) {
                //TODO pcap4j清洗清洗完成落盘ElasticSearch
                if (Const.CLEAN_DATA_OUTTYPE_ES.equals(cleanoutputtype)) {
                    GeneralClean.getCleanedSingleLineAsync(pcapFlowCombinationMap.get(key),
                            esDataList, "", icInterfaceInfoMap, apicodeList, interfaceDiscoveryList, cleanAPIServiceList,
                            collectdatatype, accountLoginRecordMap);
                    //TODO pcap4j清洗清洗完成落盘本地文件
                } else if (Const.CLEAN_DATA_OUTTYPE_DISK.equals(cleanoutputtype)) {
                    GeneralClean.appendFile(pcapFlowCombinationMap.get(key), afterPath, "",
                            apicodeList, icInterfaceInfoMap, interfaceDiscoveryList, cleanAPIServiceList, accountLoginRecordMap);
                }
                cleanSum++;
            }

            if (Const.CLEAN_DATA_OUTTYPE_ES.equals(cleanoutputtype)) {
                cleanSum = esDataList.size();
                // 调用异步处理队列数据的方法
                GeneralClean.processQueueAsync(esDataList, esIndexTime, 200, accumulator);
            }
        }

        //TODO 将HTTPS 写入接口加密流量表 进行分析
        if (encryptedpcapStatus.equals(Const.AUTHMETHOD_ON) && !apiEncryptedtrafficList.isEmpty()) {
            ApiCodeGeneration.insertApiEncrypteDtraffic(apiEncryptedtrafficList);
        }

        //TODO 流量清洗网络会话分析 写入networksession索引
        if (networksessionpcapStatus.equals(Const.AUTHMETHOD_ON)) {
            List<NetworkSessionAudit> trafficAuditsList = NetworkSessionStatistics.parsePcap(file.getPath());
            // 调用异步处理队列数据的方法
            GeneralClean.networkSessionAuditQueueAsync(trafficAuditsList, esIndexTime, 200, accumulator);
        }

        //接口文本文件处理,检测结果入库
        if (fileRecognitionStatus.equals(Const.AUTHMETHOD_ON)) {
            try {
                ApiFileRecognition.getTextParameters(pcapFlowCombinationMap, textFileMap, icInterfaceInfoMap);
            } catch (Exception e) {
            }
        }
        // }
        log.info("【当前线程 {}, 读取文件: {}, 写入Elasticsearch {} 条 】", threadName, file, cleanSum);
    }


    /**
     * TODO 获取清洗后的路径
     *
     * @return {@link String}
     */
    private static String concatenatedOutputPath(File file) {
        String parserAfterDir = "";

        //pcap文件清洗处理模式
        String cleaningMode = ConfigurationManager.getProperty("netflow.cleaningmode").trim();
        String pcapCleanAfterPath = ConfigurationManager.getProperty("PcapCleanAfterPath").trim(); //清洗之后的文件路径

        if (cleaningMode.equals(Const.CLEANING_MODE_DAY)) {

            // 指定日期清洗
            String pcapDate = ConfigurationManager.getProperty("PcapCleanBeforeDate").trim();

            boolean validDate = DateUtils.isValidDateFormat(pcapDate, "yyyyMMdd");
            if (!validDate) {
                // TODO 清洗前一天数据
                pcapDate = TimeUtils.getNextDay(new Date(), 1);
            }

            if (pcapCleanAfterPath.endsWith(File.separator)) {
                parserAfterDir = pcapCleanAfterPath + pcapDate;
            } else {
                parserAfterDir = pcapCleanAfterPath + File.separator + pcapDate;
            }

        } else if (cleaningMode.equals(Const.CLEANING_MODE_HOUR)) {
            // TODO 清洗前一个小时数据
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 获取前一个小时的时间
            LocalDateTime previousHourTime = currentTime.minusHours(1);

            DateTimeFormatter firstFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String firstLayerPath = previousHourTime.format(firstFormatter);

            if (pcapCleanAfterPath.endsWith(File.separator)) {
                parserAfterDir = pcapCleanAfterPath + firstLayerPath;
            } else {
                parserAfterDir = pcapCleanAfterPath + File.separator + firstLayerPath;
            }
        }

        return parserAfterDir + File.separator + Const.NETFLOW + "_clean_" +
                file.getName().replace("pcap", "txt");
    }


}
