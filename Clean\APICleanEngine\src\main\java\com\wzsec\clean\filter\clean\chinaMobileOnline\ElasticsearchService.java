package com.wzsec.clean.filter.clean.chinaMobileOnline;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wzsec.clean.common.utils.*;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class ElasticsearchService {
    private final ObjectMapper objectMapper;

    // RestHighLevelClient用于连接Elasticsearch
    private static final RestHighLevelClient targetClient = ES7Util.getClient(
            ConfigurationManager.getProperty("elasticsearch.username").trim(),
            ConfigurationManager.getProperty("elasticsearch.password").trim(),
            ConfigurationManager.getProperty("elasticsearch.hostlist").trim());

    // 构造方法初始化ObjectMapper
    public ElasticsearchService() {
        this.objectMapper = new ObjectMapper();
        // 禁用自动转换字段名为小写的特性
        this.objectMapper.configure(SerializationFeature.WRITE_ENUMS_USING_TO_STRING, true);
    }

    // 批量写入多个文档到 Elasticsearch
    public void indexDocumentsBulk(List<ChinaMobileOnlineSource> documents) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();

        for (ChinaMobileOnlineSource document : documents) {
            // 使用 ObjectMapper 将文档转换为 JSON 字符串
            String jsonString = objectMapper.writeValueAsString(document);

            // 创建 IndexRequest 并指定 JSON 内容类型
            IndexRequest indexRequest = new IndexRequest(getSourceIndex())
                    .id(UUID.randomUUID().toString())
                    .source(jsonString, XContentType.JSON); // 确保指定内容类型

            // 将请求添加到批量请求中
            bulkRequest.add(indexRequest);
        }

        // 执行批量写入请求
        BulkResponse bulkResponse = targetClient.bulk(bulkRequest, RequestOptions.DEFAULT);

        // 检查批量写入响应，输出失败项
        if (bulkResponse.hasFailures()) {
            System.err.println("批量写入错误: " + bulkResponse.buildFailureMessage());
        } else {
            System.out.println("批量写入成功！");
        }
    }

    // 关闭 Elasticsearch 客户端连接
    public void close() throws IOException {
        targetClient.close();
    }

    public static void main(String[] args) {
        try {
            List<ChinaMobileOnlineSource> sourceList = new ArrayList<>();

            ElasticsearchService esService = new ElasticsearchService();
            // 创建一个示例文档对象
            ChinaMobileOnlineSource chinaMobileOnlineSource = new ChinaMobileOnlineSource();
            chinaMobileOnlineSource.setAbilityId("100000203162");
            chinaMobileOnlineSource.setAbilityName("四川_能力开放平台_接触和话单信息查询");
            chinaMobileOnlineSource.setAbilitySys("000128007202");
            chinaMobileOnlineSource.setAbilitySysName("四川统一接口平台");
            chinaMobileOnlineSource.setCustId("C6173");
            chinaMobileOnlineSource.setErrorMsgCntt("");
            chinaMobileOnlineSource.setLogTypecd("4"); // TODO
            chinaMobileOnlineSource.setMonitorType("1");
            // chinaMobileOnlineSource.setMsgCntt("reqHeader=(\"accessSecretkey\":\"UxIghkqDh3x/+i8rUKQQlNDDpNGYorNs+LFZggJro5x9zMhYvCHGAeCFCka2HH48\"\"sendTime\":\"2024-11-02 04:50:17\"),reqMsg=(\"params(' CNTMNG_ID':2024091413331214513213349*])");
            chinaMobileOnlineSource.setMsgCntt("{\\\"taskname\\\":\\\"公共场所卫生许可证延续（告知承诺制）\\\",\\\"xzqhid\\\":\\\"HZ2881f4424539dd0142453c856b0025\\\"}");
            chinaMobileOnlineSource.setRtnTransid("20928-2024110204501743637603");
            chinaMobileOnlineSource.setServiceId("");
            chinaMobileOnlineSource.setServiceName("");
            chinaMobileOnlineSource.setSrvInvkstsCd(0);
            chinaMobileOnlineSource.setTrdngTime("2024-11-14 14:50:17");
            sourceList.add(chinaMobileOnlineSource);

            // 调用批量写入方法
            esService.indexDocumentsBulk(sourceList);

            // 关闭客户端
            esService.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 根据配置动态生成索引名称
    private static String getSourceIndex() {
        String cleaningMode = ConfigurationManager.getProperty("netflow.cleaningmode").trim();
        String sourceIndex = ConfigurationManager.getProperty("netflow.es.index.prefix").trim();
        String pcapDate;

        if (cleaningMode.equals(Const.CLEANING_MODE_DAY)) {
            pcapDate = ConfigurationManager.getProperty("netflow.checkdate").trim();
            boolean validDate = DateUtils.isValidDateFormat(pcapDate, "yyyyMM");
            if (!validDate) {
                pcapDate = TimeUtils.getNextMonth(new Date(), Integer.parseInt(ConfigurationManager.getProperty("netflow.checkintervalday").trim()));
            }
        } else if (cleaningMode.equals(Const.CLEANING_MODE_HOUR)) {
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime previousHourTime = currentTime.minusHours(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
            pcapDate = previousHourTime.format(formatter);
        } else {
            throw new IllegalArgumentException("Unknown cleaning mode: " + cleaningMode);
        }
        return sourceIndex + pcapDate;
    }
}
