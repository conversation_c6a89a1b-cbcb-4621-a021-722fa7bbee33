package com.wzsec.clean.modules.dao;

import com.wzsec.clean.modules.model.PcapTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Title: PcapTaskDao
 * Decription:
 *
 * <AUTHOR>
 * @date 2020/11/11
 */
@Mapper
public interface PcapTaskDao {
    List<PcapTask> getUseTaskInfo();

    List<PcapTask> getTaskByType(@Param("httpType") String httpType, @Param("ftpType") String ftpType);

    PcapTask getTaskById(String id);
}
