package com.wzsec.clean.kafka.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @描述:
 * @文件名:
 * @创建人: gechangwei
 * @创建时间: 2019/11/22 13:09
 * @修改人:
 * @修改备注: Copyright 北京和信金谷科技有限公司 2019/11/22
 */
@Setter
@Getter
public class Message implements Serializable {

    //请求URL
    private String url;
    //请求头
    private String header;
    //请求体
    private String body;
    //请求序号
    private String serialNumber;
    //请求报文大小
    private String sizeX;
    //记录时间
    private long time;
    //类型 REC REQ RESP RET
    private String type;

}
