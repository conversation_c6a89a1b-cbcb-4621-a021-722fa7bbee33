package com.wzsec.clean.filter.parser;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.filter.clean.HainanBigData.HaiNanRequestHeaderProcess;
import com.wzsec.clean.filter.clean.HainanBigData.HaiNanUniversalProcess;
import com.wzsec.clean.filter.clean.Lanxi.LanxiFlowClean;
import com.wzsec.clean.filter.clean.GuanWang.GuanWangFlowClean;
import com.wzsec.clean.filter.clean.ShangFei.ShangFeiFlowClean;
import com.wzsec.clean.filter.clean.ZhiWangKeJi.ZhiWangKeJiFlowClean;
import com.wzsec.clean.filter.clean.networksession.NetworkSessionAudit;
import com.wzsec.clean.modules.dao.ApiDao;
import com.wzsec.clean.modules.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.pcap4j.core.BpfProgram;
import org.pcap4j.core.PcapHandle;
import org.pcap4j.core.Pcaps;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.TcpPacket;
import org.pcap4j.packet.namednumber.IpNumber;
import org.pcap4j.packet.namednumber.TcpPort;

import java.io.EOFException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.Inet4Address;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;


/**
 * 通用PCAP清洗
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Slf4j
public class GeneralClean {

    private static final RestHighLevelClient restHighLevelClient = SpringUtils.getApplicationContext().getBean(RestHighLevelClient.class);

    // TODO ES写库线程池
    private static final ExecutorService esWriteThreadPool = Executors.newFixedThreadPool(1, new ThreadFactory() {
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setName("EsWriteThread-" + threadNumber.getAndIncrement());
            return thread;
        }
    });

    /**
     * 数据包读取清理
     *
     * @param pcapFilePath           pcap文件路径
     * @param pcapFlowCombinationMap pcap流量组合
     */
    public static void packetReadCleaning(String pcapFilePath,
                                          Map<String, PcapFlowCombination> pcapFlowCombinationMap,
                                          List<ApiEncryptedtraffic> apiEncryptedtrafficList) {
        // 存储上一次的端口号，使用端口号作为键，端口号本身作为值
        Map<Integer, Integer> firstPortMap = new HashMap<>();
        try (PcapHandle handle = Pcaps.openOffline(pcapFilePath)) {
            handle.setFilter("tcp", BpfProgram.BpfCompileMode.OPTIMIZE);
            while (true) {
                try {
                    Packet packet = handle.getNextPacketEx();
                    Timestamp timestamp = handle.getTimestamp();
                    String requestTime = DateUtils.timestampToString(timestamp, "yyyy-MM-dd HH:mm:ss");
                    processPacket(packet, requestTime, pcapFlowCombinationMap, apiEncryptedtrafficList, firstPortMap);

                } catch (EOFException e) {
                    break; // 到达文件结尾
                } catch (TimeoutException e) {
                    throw new RuntimeException(e);
                }
            }
            //TODO 执行完成后清空
            firstPortMap.clear();
        } catch (Exception e) {
//            e.printStackTrace();
        }
    }

    /**
     * 处理数据包
     *
     * @param packet                 流量包对象
     * @param requestTime            请求时间
     * @param pcapFlowCombinationMap pcap流量组合图
     */
    private static void processPacket(Packet packet,
                                      String requestTime,
                                      Map<String, PcapFlowCombination> pcapFlowCombinationMap,
                                      List<ApiEncryptedtraffic> apiEncryptedtrafficList,
                                      Map<Integer, Integer> firstPortMap) {

        // TODO 1. VXLAN流量解析(注释,使用开启)
        //VXLANTrafficParsing.vxlanAnalysis(packet, requestTime, pcapFlowCombinationMap);
        // TODO 2. GRE流量解析(注释,使用开启)
        //GRETrafficParsing.greAnalysis(packet, requestTime, pcapFlowCombinationMap);
        // 3.常规检测TCP/HTTP流量
        HTTPTrafficParsing(packet, requestTime, pcapFlowCombinationMap, apiEncryptedtrafficList, firstPortMap);
    }

    /**
     * 常规TCP/HTTP解析
     *
     * @param packet 包
     * @param requestTime 请求时间
     * @param pcapFlowCombinationMap Pcap流量组合图
     * @param apiEncryptedtrafficList API加密流量列表
     * @param firstPortMap 第一张港口图
     */
    private static void HTTPTrafficParsing(Packet packet, String requestTime, Map<String, PcapFlowCombination> pcapFlowCombinationMap, List<ApiEncryptedtraffic> apiEncryptedtrafficList, Map<Integer, Integer> firstPortMap) {
        if (packet.contains(IpV4Packet.class)) {
            IpV4Packet ipV4Packet = packet.get(IpV4Packet.class);
            if (ipV4Packet != null && ipV4Packet.getHeader() != null) {
                Inet4Address srcAddr = ipV4Packet.getHeader().getSrcAddr();
                Inet4Address dstAddr = ipV4Packet.getHeader().getDstAddr();

                IpNumber protocol = ipV4Packet.getHeader().getProtocol();
                if (protocol.equals(IpNumber.TCP)) {
                    TcpPacket tcpPacket = packet.get(TcpPacket.class);

                    if (tcpPacket != null && tcpPacket.getPayload() != null) {

                        TcpPort srcPort = tcpPacket.getHeader().getSrcPort();
                        TcpPort dstPort = tcpPacket.getHeader().getDstPort();

                        byte[] payload = tcpPacket.getPayload().getRawData();

                        String content = new String(payload, StandardCharsets.UTF_8);

                        String seq_number = String.valueOf(tcpPacket.getHeader().getSequenceNumberAsLong());
                        String ack_number = String.valueOf(tcpPacket.getHeader().getAcknowledgmentNumberAsLong());

                        if (content.contains(Const.HTTP_SIGN)) {
                            // 判断请求头
                            if (content.contains("GET") || content.contains("POST") ||
                                    content.contains("PUT") || content.contains("DELETE") || content.contains("OPTIONS") ||
                                    content.contains("TRACE") || content.contains("PRI")) {
                                PcapFlowCombination pcapFlowCombination = new PcapFlowCombination();
                                pcapFlowCombination.setRequest_time(requestTime);
                                pcapFlowCombination.setRequest_data(content); //请求体
                                pcapFlowCombination.setRequest_bytes(payload); //请求体字节

                                // 获取源和目标IP地址
                                pcapFlowCombination.setClient_ip(dstAddr.getHostAddress()); //客户端IP
                                pcapFlowCombination.setServer_ip(srcAddr.getHostAddress()); //服务端IP
                                // 获取源和目标端口
                                pcapFlowCombination.setServer_port(String.valueOf(srcPort.valueAsInt())); //服务端端口
                                pcapFlowCombination.setClient_port(String.valueOf(dstPort.valueAsInt())); //客户端端口

                                // 从HTTP请求头中提取真实IP地址
                                String realIP = RealIPExtractor.extractRealIPFromHeader(content);
                                if (StringUtils.isNotBlank(realIP)) {
                                    // 记录原始IP和真实IP的映射关系
                                    pcapFlowCombination.setClient_ip(realIP); // 更新为真实客户端IP
                                }

                                // 从HTTP请求头中提取真实的服务端IP地址
                                String serverIp = ServerIPExtractor.extractServerIPFromHeader(content);
                                if (StringUtils.isNotBlank(serverIp)) {
                                    pcapFlowCombination.setServer_ip(serverIp); // 更新为真实服务端IP
                                }

                                // TODO 智网科技,客户端及服务端IP从请求头中取值
                                if (ConfigurationManager.getProperty("netflow.collectdatatype").trim().equals(Const.FLOW_DATA_ZhiWangKeJi)) {
                                    ZhiWangKeJiFlowClean.interceptClientServeIPByZhiWangKeJi(pcapFlowCombination);
                                }

                                String request_url = PcapTrafficAnalysis.extractUrl(content);
                                pcapFlowCombination.setRequest_url(request_url);

                                pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                            }
                            // 判断响应头
                            if (content.trim().startsWith("HTTP/1.") || content.trim().startsWith("HTTP/2.")) {
                                // 请求对应响应更新到Map集合
                                if (pcapFlowCombinationMap.containsKey(seq_number)) {
                                    PcapFlowCombination pcapFlowCombination = pcapFlowCombinationMap.get(seq_number);

                                    if (content.contains("Content-Encoding: gzip")) {
                                        try {
                                            content = GzipDecoder.gzipAdaptation(payload, payload, content);
                                        } catch (Exception e) {
                                            content = content;
                                        }
                                    }

                                    pcapFlowCombination.setResponse_data(content); //响应体
                                    pcapFlowCombination.setResponse_bytes(payload); //响应体字节
                                    pcapFlowCombinationMap.remove(seq_number);
                                    pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                                }
                            }
                        } else {
                            //对不完整响应进行组装
                            PcapFlowCombination pcapFlowCombination = pcapFlowCombinationMap.get(ack_number);
                            //TODO 走HTTPS 采集流程
                            if (pcapFlowCombination == null) {
                                HTTPSCollection.checkPort(requestTime, apiEncryptedtrafficList, firstPortMap, srcAddr, dstAddr,
                                        srcPort, dstPort, protocol);
                            } else {
                                if (pcapFlowCombinationMap.containsKey(ack_number)) {
                                    String response_data = pcapFlowCombination.getResponse_data();

                                    if (StringUtils.isNotBlank(response_data)) {
                                        if (response_data.startsWith("HTTP/1.") || response_data.startsWith("HTTP/2.")) {
                                            //如果包含key值,需要对value进行更新,同时将content_byte进行组装
                                            byte[] bytes = combineBytes(pcapFlowCombination.getResponse_bytes(), payload);
                                            content = new String(bytes, StandardCharsets.UTF_8);
                                            pcapFlowCombination.setResponse_bytes(bytes);

                                            if (content.contains("Content-Encoding: gzip")) {
                                                try {
                                                    content = GzipDecoder.gzipAdaptation(bytes, payload, content);
                                                } catch (Exception e) {
                                                    content = content;
                                                }
                                            }

                                            pcapFlowCombination.setResponse_data(content);
                                            pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                                        }
                                    } else {
                                        //对不完整响应进行组装 上传 ask对应
                                        //如果包含key值,需要对value进行更新,同时将content_byte进行组装
                                        byte[] bytes = combineBytes(pcapFlowCombination.getRequest_bytes(), payload);
                                        pcapFlowCombination.setRequest_bytes(bytes);
                                        pcapFlowCombination.setRequest_data(pcapFlowCombination.getRequest_data() + content);
                                        pcapFlowCombinationMap.put(ack_number, pcapFlowCombination);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * TODO 根据已获取数据集直接组装对象
     *
     * @param pcapFlowCombination pcap流量组合
     */
    public static ApiCallNetFlow obtainAssemblyApiCallNetFlow(PcapFlowCombination pcapFlowCombination,
                                                              String waterMarkInfo,
                                                              List<String> apiList,
                                                              Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                                              List<String> interfaceDiscoveryList,
                                                              List<String> cleanAPIServiceList,
                                                              Map<String, AccountLoginRecord> accountLoginRecordMap) throws Exception {

        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();

        String requestUrl = pcapFlowCombination.getRequest_url();  //请求URL
        String clientIp = pcapFlowCombination.getClient_ip(); //客户端IP
        String clientPort = pcapFlowCombination.getClient_port();  // 客户端端口
        String serverIp = pcapFlowCombination.getServer_ip();  //服务端IP
        String serverPort = pcapFlowCombination.getServer_port();  // 服务端端口
        String requestTime = pcapFlowCombination.getRequest_time(); //请求时间
        String requestData = pcapFlowCombination.getRequest_data() == null ? "" : pcapFlowCombination.getRequest_data(); //请求内容
        String responseData = pcapFlowCombination.getResponse_data() == null ? "" : pcapFlowCombination.getResponse_data();  //响应内容

        byte[] request_bytes = pcapFlowCombination.getRequest_bytes();
        byte[] response_bytes = pcapFlowCombination.getResponse_bytes();

        String requestBody = "";
        String responseBodyData = "";
        try {
            requestBody = ApiFileRecognition.extractTextFromMultipart(request_bytes);
            responseBodyData = ApiFileRecognition.extractTextFromBody(response_bytes);
        } catch (Exception e) {
            requestBody = "";
            responseBodyData = "";
        }

        //判断该接口是否属于上传下载操作
        if (PcapTrafficAnalysis.checkStatusApiFile(requestData, responseData)) {
            apiCallNetFlow.setResBody(responseData);
            apiCallNetFlow.setReqBody(requestData);
            apiCallNetFlow.setResByte(responseBodyData);
            apiCallNetFlow.setReqByte(requestBody);
        }


        // 请求响应状态, 请求无值导致截取报错,该处置为空
        String resstatuscode = "";
        try {
            resstatuscode = responseData.split("\r\n")[0].split(" ")[1];
        } catch (Exception e) {
            resstatuscode = "";
        }

        // 请求方法
        String reqmethod = "";
        try {
            reqmethod = requestData.split("\r\n")[0].split(" ")[0];
        } catch (Exception e) {
            reqmethod = "";
        }

        // TODO 从接口流量中截取接口编码标识
        String collectdatatype = ConfigurationManager.getProperty("netflow.collectdatatype").trim();

        // 请求体
        String reqcontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            try {
                reqcontent = PcapTrafficAnalysis.decodeRequestBody(requestData);
                // TODO 金华_兰溪分支
                if (Const.FLOW_DATA_Jinhua.equals(collectdatatype)) {
                    reqcontent = LanxiFlowClean.getRequestParameters(reqcontent);
                }
            } catch (Exception e) {
                reqcontent = requestData;
            }
        }

        // 响应体
        String rescontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            rescontent = PcapTrafficAnalysis.extractResponseBody(responseData);
            rescontent = PcapTrafficAnalysis.extractContent(rescontent);
        }

        //  TODO 获取响应数据
        String resstatus = "";
        String repstatus = "";
        if (resstatuscode.startsWith(Const.API_STATUSCODE_5PREFIX)) {
            resstatus = Const.SERVER_ERROR_STATUS;
            repstatus = Const.CLIENT_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_4PREFIX)) {
            resstatus = Const.CLIENT_ERROR_STATUS;
            repstatus = Const.SERVER_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_1PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_2PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_3PREFIX)) {
            resstatus = Const.SERVER_CORRECT_STATUS; //响应状态   服务端
            repstatus = Const.CLIENT_CORRECT_STATUS; //请求状态   客户端
        }

        //  TODO 2.获取请求数据
        // 请求URI
        String interfaceuri = requestUrl.replace("//", "/");
        if (interfaceuri.contains("http:")) {
            interfaceuri = interfaceuri.split("http:")[1];
        }

        // 获取url及接口编码
        String uri = PcapTrafficAnalysis.getInterfaceURI(interfaceuri);

        pcapFlowCombination.setResponse_body_data(rescontent); // 响应体
        pcapFlowCombination.setRequest_body_data(reqcontent);  // 请求体
        pcapFlowCombination.setReq_method(reqmethod); //请求方法

        apiCallNetFlow.setAccount("");

        if (Const.FLOW_DATA_general.equals(collectdatatype)) {
            writesTheAccountLoginRecord(pcapFlowCombination, requestData, reqcontent, rescontent, uri, accountLoginRecordMap);
        }

        if (interfaceuri.toLowerCase().contains("appkey")) {
            String appKey = PcapTrafficAnalysis.extractAppKey(interfaceuri);
            apiCallNetFlow.setAccount(appKey); //TODO 该处涉及不标准URL导致截取报错,最后时间参数无key值 账号
        } else {
            String client_ip = pcapFlowCombination.getClient_ip();
            String auth = PcapTrafficAnalysis.extractAuthorization(requestData);
            String cliip = accountLoginRecordMap.get(auth) == null ? "" : accountLoginRecordMap.get(auth).getClientip();
            if (cliip.equals(client_ip)) {
                String account = accountLoginRecordMap.get(auth).getAccount();
                apiCallNetFlow.setAccount(account);
            }
        }

        // TODO 4. 获取接口编码,新增接口写入接口信息表
        String apicode = ApiCodeGeneration.getInterfaceCode(interfaceuri, uri, pcapFlowCombination,
                apiList, icInterfaceInfoMap, interfaceDiscoveryList);

        Map<String, String> requestHeardMap = PcapTrafficAnalysis.decodeHeader(requestData); // 请求头
        Map<String, String> responseHeardMap = PcapTrafficAnalysis.decodeHeader(responseData); // 响应头

        apiCallNetFlow.setApicode(apicode); //接口编码
        apiCallNetFlow.setApiuri(interfaceuri); //接口路径

        Params params = new Params();
        params.setParams(reqcontent == null ? "" : reqcontent);
        params.setHead(requestHeardMap);  // 请求头
        // 处理jwt签名算法相关
        String jwtSignatureAlgorithm = JwtSignatureChecker.getJwtSignatureAlgorithm(requestHeardMap);
        if (StringUtils.isNotBlank(jwtSignatureAlgorithm)) {
            params.setJwtSignatureAlgorithm(jwtSignatureAlgorithm);
        }

        String[] reqlines = requestData.split("\r\n");
        if (reqlines.length > 0) {
            params.setReqheader(reqlines[0]); //请求标头
        }

        apiCallNetFlow.setReqcontent(params); // 请求内容
        DataRescontent dataRescontent = new DataRescontent();
        dataRescontent.setStatusCode(resstatuscode); //状态码
        dataRescontent.setHead(responseHeardMap); //响应头
        dataRescontent.setData(rescontent + waterMarkInfo);

        String[] reslines = responseData.split("\r\n");
        if (reslines.length > 0) {
            dataRescontent.setResheader(reslines[0]); // 响应标头
        }

        apiCallNetFlow.setRescontent(dataRescontent);  //响应内容

        if (Const.FLOW_DATA_GuanWang.equals(collectdatatype)) {
            //TODO 管网分支-接口审计采集的接口流量内网IP、弹性IP关联转换
            apiCallNetFlow.setApiip(GuanWangFlowClean.networkSegmentMatching(clientIp));  //服务端IP TODO 该处调整
            apiCallNetFlow.setClientip(GuanWangFlowClean.networkSegmentMatching(serverIp)); //客户端IP TODO 该处调整
        } else {
            apiCallNetFlow.setApiip(clientIp);  //服务端IP TODO 该处调整
            apiCallNetFlow.setClientip(serverIp); //客户端IP TODO 该处调整
        }

        apiCallNetFlow.setClientmac(serverPort); // 客户端端口 TODO 该处调整

        String ipRegion = IPUtils.getIpRegion(serverIp);
        apiCallNetFlow.setClientIpRegion(ipRegion); //客户端IP归属地

        apiCallNetFlow.setApiport(clientPort); //服务端端口 TODO 该处调整

        apiCallNetFlow.setReqmethod(reqmethod); //请求方法
        apiCallNetFlow.setResstatus(resstatus); //响应状态

        apiCallNetFlow.setRepstatus(repstatus); //请求状态
        apiCallNetFlow.setCalltime(requestTime); // 请求时间
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime()); //  清洗时间

        apiCallNetFlow.setSystem(ConfigurationManager.getProperty("netflow.systemFullName").trim()); // 系统标识
        // apiCallNetFlow.setAccount(getParam(interfaceuri, Const.FLOW_CLEANING_ACCOUNT)); // 账号


        apiCallNetFlow.setDatatype("netflow"); // 数据类型(固定值)

        // TODO 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
        ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
        eventDTO.setCategory("netflow");
        apiCallNetFlow.setEvent(eventDTO);

        // 新增字段 接口响应字符数
        long resSize = rescontent.length();
        String contentLength = responseHeardMap.get("Content-Length");
        if (contentLength == null) {
            contentLength = responseHeardMap.get("content-length");
        }
        if (contentLength != null && contentLength.matches("\\d+")) { // 检查是否为数字
            resSize = Long.parseLong(contentLength);
        }
        apiCallNetFlow.setRessize(resSize);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = formatter.parse(requestTime);
        int hour = date.getHours();
        apiCallNetFlow.setReqtimetag(String.valueOf(hour));

        apiCallNetFlow.setAkapicode(serverIp + "," + apicode); //先采用客户端IP+接口编码组合,对应请求和响应标识

        return apiCallNetFlow;

    }

    /**
     * 将账号登录信息写入记录表
     *
     * @param pcapFlowCombination
     * @param requestData
     * @param reqcontent
     * @param rescontent
     * @param uri
     */
    public static void writesTheAccountLoginRecord(PcapFlowCombination pcapFlowCombination,
                                                   String requestData,
                                                   String reqcontent,
                                                   String rescontent,
                                                   String uri,
                                                   Map<String, AccountLoginRecord> accountLoginRecordMap) {
        if (uri.contains("login") && reqcontent.contains("user")) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                JsonNode rootNode = mapper.readTree(rescontent);
                //token
                String token = rootNode.get("token").asText();
                //账号
                String username = rootNode.get("user").get("username").asText();

                String reqTerminal = PcapTrafficAnalysis.acquisitionTerminal(requestData);

                AccountLoginRecord accountLoginRecord = new AccountLoginRecord();
                accountLoginRecord.setAccount(username);
                accountLoginRecord.setClientip(pcapFlowCombination.getClient_ip());
                accountLoginRecord.setClientport(pcapFlowCombination.getClient_port());
                accountLoginRecord.setServerip(pcapFlowCombination.getServer_ip());
                accountLoginRecord.setServerport(pcapFlowCombination.getServer_port());
                accountLoginRecord.setToken(token);
                accountLoginRecord.setRequestterminal(reqTerminal);
                accountLoginRecord.setRequesttime(pcapFlowCombination.getRequest_time());
                accountLoginRecord.setCreationtime(DateUtil.now());
                ApiDao.saveAccountLoginRecord(accountLoginRecord);

                accountLoginRecordMap.put(token, accountLoginRecord);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 获取写ES对象
     *
     * @param pcapFlowCombination pcap流量组合
     * @param esDataList          es数据列表
     * @param waterMarkInfo       水印信息
     * @param icInterfaceInfoMap  ic接口信息图
     * @param apiList             api列表
     * @param apiDiscoveryList    api发现列表
     */
    public static void getCleanedSingleLineAsync(PcapFlowCombination pcapFlowCombination,
                                                 List<ApiCallNetFlow> esDataList,
                                                 String waterMarkInfo,
                                                 Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                                 List<String> apiList,
                                                 List<String> apiDiscoveryList,
                                                 List<String> cleanAPIServiceList,
                                                 String collectdatatype,
                                                 Map<String, AccountLoginRecord> accountLoginRecordMap) {
        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
        try {
            // TODO 海南 [4,8] 清洗获取传输对象
            if (Const.FLOW_DATA_Hainan.equals(collectdatatype)) {
                apiCallNetFlow = HaiNanRequestHeaderProcess.haiNanSceneCleaning(pcapFlowCombination);
            } else if (Const.FLOW_DATA_HAINAN_UNIVERSAL.equals(collectdatatype)) {
                apiCallNetFlow = HaiNanUniversalProcess.hainanUniversalClean(pcapFlowCombination);
            } else if (Const.FLOW_DATA_ShangFei.equals(collectdatatype)) {
                //TODO 商飞 清洗获取传输对象
                if (StringUtils.isNotBlank(pcapFlowCombination.getRequest_url())) {
                    apiCallNetFlow = ShangFeiFlowClean.shangFeiClean(pcapFlowCombination, waterMarkInfo, apiList,
                            icInterfaceInfoMap, apiDiscoveryList, cleanAPIServiceList, accountLoginRecordMap);
                }
            } else {
                if (StringUtils.isNotBlank(pcapFlowCombination.getRequest_url())) {
                    apiCallNetFlow = obtainAssemblyApiCallNetFlow(pcapFlowCombination, waterMarkInfo, apiList,
                            icInterfaceInfoMap, apiDiscoveryList, cleanAPIServiceList, accountLoginRecordMap);
                }
            }
            if (StringUtils.isNotBlank(apiCallNetFlow.getApiip()) && StringUtils.isNotBlank(apiCallNetFlow.getApiport())) {
                String serverIpPort = apiCallNetFlow.getApiip() + ":" + apiCallNetFlow.getApiport();
                if (!cleanAPIServiceList.isEmpty() && cleanAPIServiceList.contains(serverIpPort)) {
                    esDataList.add(apiCallNetFlow);
                }
            }
            // 将 apiCallNetFlow 添加到队列
            if (apiCallNetFlow.getRescontent() != null) {
                if (cleanAPIServiceList.isEmpty() && !apiCallNetFlow.getRescontent().getData().contains("请求超时")
                        && !apiCallNetFlow.getRescontent().getData().contains("严重未知错误")
                        && StringUtils.isNotBlank(apiCallNetFlow.getApicode())) {
                    esDataList.add(apiCallNetFlow);
                }
            }
        } catch (Exception ex) {
            log.error("ES清洗出现异常,异常信息为: {}", ex.getMessage());
            ex.printStackTrace();
        }
    }


    /**
     * 批量,异步写ES(插入操作不需要等待结果,避免阻塞)
     *
     * @param esDataList  es数据列表
     * @param esIndexTime es索引时间
     * @param batchSize   批量大小
     */
    public static void processQueueAsync(List<ApiCallNetFlow> esDataList, String esIndexTime, int batchSize, Accumulator accumulator) {

        CompletableFuture.runAsync(() -> {

            try {
                int dataSize = esDataList.size();

                if (dataSize > 0) {
                    int batchCount = (int) Math.ceil((double) dataSize / batchSize);

                    for (int i = 0; i < batchCount; i++) {
                        int fromIndex = i * batchSize;
                        int toIndex = Math.min((i + 1) * batchSize, dataSize);

                        List<ApiCallNetFlow> batch = esDataList.subList(fromIndex, toIndex);

                        BulkRequest bulkRequest = new BulkRequest();
                        for (ApiCallNetFlow flow : batch) {
                            flow.setTimestamp(Instant.now());
                            String jsonData = JSON.toJSONString(flow);
                            bulkRequest.add(new IndexRequest("netflow-" + esIndexTime).id(flow.getId()).source(jsonData, XContentType.JSON));
                        }

                        BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                        // 处理 bulkResponse
                        handleBulkResponse(bulkResponse);
                    }
                }
                accumulator.addToValue(esDataList.size());
                Console.log("〖 EsWriteThread 开始执行 {} 写入操作,写入ES条数: {}, 累计写入{} 〗",
                        Const.NETFLOW + "-" + esIndexTime,
                        esDataList.size(),
                        accumulator.getValue());
            } catch (IOException e) {
                log.error("批量插入数据出现异常,异常信息为: {}", e.getMessage());
            }
        }, esWriteThreadPool);
    }

    /**
     * 处理 BulkResponse
     *
     * @param bulkResponse 批量响应
     */
    private static void handleBulkResponse(BulkResponse bulkResponse) {
        if (bulkResponse.hasFailures()) {
            for (BulkItemResponse bulkItemResponse : bulkResponse.getItems()) {
                if (bulkItemResponse.isFailed()) {
                    BulkItemResponse.Failure failure = bulkItemResponse.getFailure();
                    String failureMessage = failure.getMessage();
                    String index = failure.getIndex();
                    String type = failure.getType();
                    // 打印失败的操作信息
                    log.error("Bulk operation failed. Index: {}, Type: {}, Message: {}", index, type, failureMessage);
                }
            }
        }
    }

    /**
     * 将两个字节数组组装成一个新的字节数组
     *
     * @param bytes1 字节1
     * @param bytes2 字节2
     * @return {@link byte[]}
     */
    private static byte[] combineBytes(byte[] bytes1, byte[] bytes2) {
        byte[] combinedBytes = new byte[bytes1.length + bytes2.length];
        System.arraycopy(bytes1, 0, combinedBytes, 0, bytes1.length);
        System.arraycopy(bytes2, 0, combinedBytes, bytes1.length, bytes2.length);
        return combinedBytes;
    }



    /**
     * 将清洗内容写入到文件
     *
     * @param path          路径
     * @param waterMarkInfo 水印信息
     * @param apiList       api列表
     * @throws Exception 例外
     */
    public static void appendFile(PcapFlowCombination pcapFlowCombination, String path,
                                  String waterMarkInfo,
                                  List<String> apiList,
                                  Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                  List<String> interfaceDiscoveryList,
                                  List<String> cleanAPIServiceList,
                                  Map<String, AccountLoginRecord> accountLoginRecordMap) throws Exception {
        // 判断文件夹是否存在
        String[] paths = path.split(Matcher.quoteReplacement(File.separator));
        StringBuilder fullPath = new StringBuilder();
        for (int i = 0; i < paths.length; i++) {
            fullPath.append(paths[i]).append(File.separator);
            File file = new File(fullPath.toString());
            if (paths.length - 1 != i) {
                if (!file.exists()) {
                    file.mkdir();
                }
            }
        }

        // TODO 清洗获取传输对象
        ApiCallNetFlow apiCallNetFlow = obtainAssemblyApiCallNetFlow(pcapFlowCombination, waterMarkInfo,
                apiList, icInterfaceInfoMap, interfaceDiscoveryList, cleanAPIServiceList, accountLoginRecordMap);

        if (apiCallNetFlow != null) {
            String cleaningContent = JSON.toJSONString(apiCallNetFlow);

            FileOutputStream fos;
            try {
                fos = new FileOutputStream(path, true);
                fos.write((cleaningContent + "\n").getBytes());
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
                log.info("清洗数据保存:" + path + "出现异常!");
            }
        }
    }


    /**
     * 批量,异步写ES(插入操作不需要等待结果,避免阻塞)
     *
     * @param esDataList  es数据列表
     * @param esIndexTime es索引时间
     * @param batchSize   批量大小
     */
    public static void networkSessionAuditQueueAsync(List<NetworkSessionAudit> esDataList,
                                                     String esIndexTime,
                                                     int batchSize,
                                                     Accumulator accumulator) {
        CompletableFuture.runAsync(() -> {
            try {
                int dataSize = esDataList.size();
                if (dataSize > 0) {
                    int batchCount = (int) Math.ceil((double) dataSize / batchSize);
                    for (int i = 0; i < batchCount; i++) {
                        int fromIndex = i * batchSize;
                        int toIndex = Math.min((i + 1) * batchSize, dataSize);
                        List<NetworkSessionAudit> batch = esDataList.subList(fromIndex, toIndex);
                        BulkRequest bulkRequest = new BulkRequest();
                        for (NetworkSessionAudit flow : batch) {
                            String jsonData = JSON.toJSONString(flow);
                            bulkRequest.add(new IndexRequest("networksession-" + esIndexTime).id("").source(jsonData, XContentType.JSON));
                        }
                        BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                        // 处理 bulkResponse
                        handleBulkResponse(bulkResponse);
                    }
                }
                accumulator.addToValue(esDataList.size());
                Console.log("〖 EsWriteThread 开始执行 {} 写入操作,写入ES条数: {}, 累计写入{} 〗",
                        Const.NetworkSession + "-" + esIndexTime,
                        esDataList.size(),
                        accumulator.getValue());
            } catch (IOException e) {
                log.error("批量插入数据出现异常,异常信息为: {}", e.getMessage());
            }
        }, esWriteThreadPool);
    }
}