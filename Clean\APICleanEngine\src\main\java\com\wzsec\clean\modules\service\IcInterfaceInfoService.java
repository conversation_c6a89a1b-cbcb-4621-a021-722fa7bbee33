package com.wzsec.clean.modules.service;

import com.wzsec.clean.modules.model.IcInterfaceInfo;

import java.util.List;
import java.util.Map;

/**
 * 接口信息
 */
public interface IcInterfaceInfoService {

    /**
     * 查询接口信息
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<IcInterfaceInfo> selectInterfaceInfo();


    List<String> queryApicodeList();

    String cleanAPIServiceFlag(String flag);

    List<String> cleanAPIServiceList(String flag);

}
