package com.wzsec.clean.filter.parser;

import com.wzsec.clean.modules.model.PcapFlowCombination;
import lombok.extern.slf4j.Slf4j;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.UdpPacket;
import org.pcap4j.packet.namednumber.UdpPort;

import java.net.InetAddress;
import java.util.Map;

/**
 * VXLAN流量解析
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
@Slf4j
public class VXLANTrafficParsing {

    /**
     * vxlan分析
     *
     * @param packet 包
     * @param requestTime 请求时间
     * @param pcapFlowCombinationMap Pcap流量组合图
     */
    public static void vxlanAnalysis(Packet packet, String requestTime, Map<String, PcapFlowCombination> pcapFlowCombinationMap) {
        // 检查是否为VXLAN封装包
        if (packet.contains(UdpPacket.class)) {
            UdpPacket udpPacket = packet.get(UdpPacket.class);
            if (udpPacket != null && udpPacket.getHeader() != null) {
                UdpPort dstPort = udpPacket.getHeader().getDstPort();
                // VXLAN默认使用4789端口
                if (dstPort.valueAsInt() == 4789) {
                    log.info("检测到VXLAN封装包: 源端口={}, 目标端口={}",
                            udpPacket.getHeader().getSrcPort().valueAsInt(),
                            dstPort.valueAsInt());

                    // 尝试提取内部封装的包
                    if (udpPacket.getPayload() != null) {
                        byte[] payload = udpPacket.getPayload().getRawData();
                        log.info("VXLAN封装包负载大小: {} 字节", payload.length);
                        // VXLAN头部为8字节
                        if (payload.length > 8) {
                            int vni = extractVxlanVNI(payload);
                            log.info("VXLAN VNI: {}", vni);

                            // 创建PcapFlowCombination对象记录VXLAN信息
                            PcapFlowCombination pcapFlowCombination = new PcapFlowCombination();
                            pcapFlowCombination.setRequest_time(requestTime);

                            // 构建详细的VXLAN信息
                            StringBuilder vxlanInfo = new StringBuilder();
                            vxlanInfo.append("VXLAN封装流量详情:\n")
                                    .append("源端口: ").append(udpPacket.getHeader().getSrcPort().valueAsInt()).append("\n")
                                    .append("目标端口: ").append(dstPort.valueAsInt()).append("\n")
                                    .append("负载大小: ").append(payload.length).append(" 字节\n")
                                    .append(parseVxlanHeader(payload));

                            // 尝试解析内部封装的以太网帧和IP包
                            if (payload.length > 14 + 8) { // 8字节VXLAN头部 + 14字节以太网帧头部
                                try {
                                    // 提取内部MAC地址信息
                                    StringBuilder macInfo = new StringBuilder("内部封装以太网帧:\n");
                                    byte[] innerEthernet = new byte[payload.length - 8];
                                    System.arraycopy(payload, 8, innerEthernet, 0, innerEthernet.length);

                                    // 提取目标MAC和源MAC
                                    String dstMac = String.format("%02X:%02X:%02X:%02X:%02X:%02X",
                                            innerEthernet[0], innerEthernet[1], innerEthernet[2],
                                            innerEthernet[3], innerEthernet[4], innerEthernet[5]);
                                    String srcMac = String.format("%02X:%02X:%02X:%02X:%02X:%02X",
                                            innerEthernet[6], innerEthernet[7], innerEthernet[8],
                                            innerEthernet[9], innerEthernet[10], innerEthernet[11]);

                                    macInfo.append("目标MAC: ").append(dstMac).append("\n")
                                            .append("源MAC: ").append(srcMac).append("\n");

                                    // 提取以太网类型
                                    int etherType = ((innerEthernet[12] & 0xFF) << 8) | (innerEthernet[13] & 0xFF);
                                    macInfo.append("以太网类型: 0x").append(Integer.toHexString(etherType)).append("\n");

                                    vxlanInfo.append(macInfo);

                                    // 如果是IP包，解析内部IP包信息
                                    if (etherType == 0x0800) { // IPv4
                                        vxlanInfo.append("内部封装协议: IPv4\n");
                                        // 以太网帧头部14字节后是IP包
                                        vxlanInfo.append(parseInnerIPPacket(innerEthernet, 14));
                                    } else if (etherType == 0x86DD) { // IPv6
                                        vxlanInfo.append("内部封装协议: IPv6\n");
                                    }
                                } catch (Exception e) {
                                    vxlanInfo.append("解析内部以太网帧失败: ").append(e.getMessage()).append("\n");
                                }
                            }

                            // 将VXLAN信息写入request_data和response_data
                            pcapFlowCombination.setRequest_data(vxlanInfo.toString());
                            pcapFlowCombination.setResponse_data("VXLAN封装流量详情:\n源端口: " + udpPacket.getHeader().getSrcPort().valueAsInt() +
                                    "\n目标端口: " + dstPort.valueAsInt() +
                                    "\nVNI: " + vni +
                                    "\n封装类型: UDP/VXLAN\n负载大小: " + payload.length + " 字节");

                            // 设置源目标IP和端口信息
                            if (packet.contains(IpV4Packet.class)) {
                                IpV4Packet ipV4Packet = packet.get(IpV4Packet.class);
                                if (ipV4Packet != null && ipV4Packet.getHeader() != null) {
                                    pcapFlowCombination.setClient_ip(ipV4Packet.getHeader().getSrcAddr().getHostAddress());
                                    pcapFlowCombination.setServer_ip(ipV4Packet.getHeader().getDstAddr().getHostAddress());
                                    pcapFlowCombination.setClient_port(String.valueOf(udpPacket.getHeader().getSrcPort().valueAsInt()));
                                    pcapFlowCombination.setServer_port(String.valueOf(dstPort.valueAsInt()));
                                }
                            }

                            // 使用时间戳作为唯一标识符
                            String uniqueKey = "VXLAN_" + System.currentTimeMillis() + "_" + vni;
                            pcapFlowCombinationMap.put(uniqueKey, pcapFlowCombination);
                        }
                    }
                }
            }
        }
    }


    /**
     * 从VXLAN包中提取VNI (VXLAN Network Identifier)
     * VXLAN头部结构：
     * - 前4字节为标志位和保留位
     * - 后4字节中的3字节为VNI，最后1字节为保留位
     *
     * @param vxlanPayload VXLAN包的负载
     * @return VNI值
     */
    private static int extractVxlanVNI(byte[] vxlanPayload) {
        if (vxlanPayload.length < 8) {
            return -1; // 无效的VXLAN头部
        }

        // VNI在VXLAN头部的第4-6字节
        return ((vxlanPayload[4] & 0xFF) << 16) |
                ((vxlanPayload[5] & 0xFF) << 8) |
                (vxlanPayload[6] & 0xFF);
    }

    /**
     * 解析VXLAN头部信息
     *
     * @param vxlanPayload VXLAN包的负载
     * @return VXLAN头部信息描述
     */
    private static String parseVxlanHeader(byte[] vxlanPayload) {
        if (vxlanPayload.length < 8) {
            return "VXLAN头部数据不完整";
        }

        StringBuilder info = new StringBuilder();

        // 解析VXLAN标志位
        int flags = vxlanPayload[0] & 0xFF;
        int vni = extractVxlanVNI(vxlanPayload);

        info.append("VXLAN标志位: 0x").append(Integer.toHexString(flags)).append("\n")
                .append("VNI: ").append(vni).append("\n");

        return info.toString();
    }

    /**
     * 解析内部封装的IP包
     *
     * @param payload 封装包的负载
     * @param offset 内部IP包开始的偏移量
     * @return IP包信息描述
     */
    public static String parseInnerIPPacket(byte[] payload, int offset) {
        if (payload.length < offset + 20) { // 最小IPv4头部长度为20字节
            return "内部IP包数据不完整";
        }

        StringBuilder info = new StringBuilder();

        try {
            // 提取版本和头部长度
            int versionAndIhl = payload[offset] & 0xFF;
            int version = (versionAndIhl >> 4) & 0xF;
            int ihl = versionAndIhl & 0xF;
            int headerLength = ihl * 4; // 头部长度，单位为4字节

            info.append("IP版本: ").append(version).append("\n")
                    .append("IP头部长度: ").append(headerLength).append(" 字节\n");

            if (version == 4) { // IPv4
                // 提取服务类型
                int tos = payload[offset + 1] & 0xFF;
                // 提取总长度
                int totalLength = ((payload[offset + 2] & 0xFF) << 8) | (payload[offset + 3] & 0xFF);
                // 提取标识
                int identification = ((payload[offset + 4] & 0xFF) << 8) | (payload[offset + 5] & 0xFF);
                // 提取标志和片偏移
                int flagsAndOffset = ((payload[offset + 6] & 0xFF) << 8) | (payload[offset + 7] & 0xFF);
                // 提取TTL
                int ttl = payload[offset + 8] & 0xFF;
                // 提取协议
                int protocol = payload[offset + 9] & 0xFF;
                // 提取校验和
                int checksum = ((payload[offset + 10] & 0xFF) << 8) | (payload[offset + 11] & 0xFF);

                // 提取源IP和目标IP
                byte[] srcIpBytes = new byte[4];
                byte[] dstIpBytes = new byte[4];
                System.arraycopy(payload, offset + 12, srcIpBytes, 0, 4);
                System.arraycopy(payload, offset + 16, dstIpBytes, 0, 4);

                InetAddress srcIp = InetAddress.getByAddress(srcIpBytes);
                InetAddress dstIp = InetAddress.getByAddress(dstIpBytes);

                info.append("协议: ").append(protocol).append("\n")
                        .append("TTL: ").append(ttl).append("\n")
                        .append("源IP: ").append(srcIp.getHostAddress()).append("\n")
                        .append("目标IP: ").append(dstIp.getHostAddress()).append("\n");

                // 根据协议类型提供更多信息
                if (protocol == 6) { // TCP
                    info.append("传输层协议: TCP\n");
                    if (payload.length >= offset + headerLength + 4) {
                        int srcPort = ((payload[offset + headerLength] & 0xFF) << 8) | (payload[offset + headerLength + 1] & 0xFF);
                        int dstPort = ((payload[offset + headerLength + 2] & 0xFF) << 8) | (payload[offset + headerLength + 3] & 0xFF);
                        info.append("源端口: ").append(srcPort).append("\n")
                                .append("目标端口: ").append(dstPort).append("\n");
                    }
                } else if (protocol == 17) { // UDP
                    info.append("传输层协议: UDP\n");
                    if (payload.length >= offset + headerLength + 4) {
                        int srcPort = ((payload[offset + headerLength] & 0xFF) << 8) | (payload[offset + headerLength + 1] & 0xFF);
                        int dstPort = ((payload[offset + headerLength + 2] & 0xFF) << 8) | (payload[offset + headerLength + 3] & 0xFF);
                        info.append("源端口: ").append(srcPort).append("\n")
                                .append("目标端口: ").append(dstPort).append("\n");
                    }
                } else if (protocol == 1) { // ICMP
                    info.append("传输层协议: ICMP\n");
                } else {
                    info.append("传输层协议: 未知(协议号 ").append(protocol).append(")\n");
                }
            } else if (version == 6) { // IPv6
                info.append("IPv6包解析暂不支持\n");
            } else {
                info.append("未知IP版本: ").append(version).append("\n");
            }
        } catch (Exception e) {
            info.append("解析内部IP包失败: ").append(e.getMessage()).append("\n");
        }

        return info.toString();
    }

}
