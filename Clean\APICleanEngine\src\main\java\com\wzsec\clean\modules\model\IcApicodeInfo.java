package com.wzsec.clean.modules.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName: SAASCheckMethodResult
 * @Description: TODO
 * @date 2020年04月20日
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IcApicodeInfo {

    private Integer id;    //id
    private String custname;  //所属厂商
    private String cust_simplename;  //厂家简称
    private String appid;    //应用id
    private String appname;  //应用名称
    private String logsign;  //日志标识
    private String apicode;  //接口编码
    private String apimethod;   //接口方法
    private String apiname; //接口名称
    private String apides;   //接口描述(接口中文名）
    private String apitype;   //接口类型
    private String url;   //接口地址

    private String inputparams;  //输入参数
    private String inparam_mean;   //输入参数含义
    private String inparam_format;  //输入参数格式举例
    private String inparam_note;   //输入参数说明

    private String outputparams;  //输出参数
    private String outparam_mean;   //输出参数含义
    private String outparam_format;  //输出参数格式举例
    private String outparam_note;   //输出参数说明

    private String successreturn;   //成功返回值示例

    private String failreturn;//失败返回值示例
    private String importance;  //参数重要程度

    private String begindate;//接口开始使用时间
    private String enddate;//接口结束使用时间
    private String createuser;//创建人
    private String createtime;//创建时间
    private String updateuser;//更新人
    private String updatetime;//更新时间
    private String note;  // 备注
    private String sparefield1;
    private String sparefield2;
    private String sparefield3;
    private String sparefield4;
}
