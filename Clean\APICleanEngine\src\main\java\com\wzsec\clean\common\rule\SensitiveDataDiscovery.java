package com.wzsec.clean.common.rule;

import com.wzsec.clean.common.utils.Const;

import java.util.*;

/**
 * 敏感数据发现
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
public class SensitiveDataDiscovery {

    /**
     * 敏感数据分类
     *
     * @param dataToBeIdentifiedList 待识别数据列表
     */
    public static Map<String, List<String>> sensitiveDataClassification(List<String> dataToBeIdentifiedList) {

        Map<String, List<String>> sensitiveDataDecomposition = new HashMap<>();

        //敏感数据集合
        List<String> IDCardList = new ArrayList<>();
        List<String> PassPortCardList = new ArrayList<>();
        List<String> OfficerCardList = new ArrayList<>();
        List<String> MobileNumberList = new ArrayList<>();
        List<String> NameList = new ArrayList<>();
        List<String> HMPassCheckList = new ArrayList<>();
        List<String> BankCardList = new ArrayList<>();
        List<String> AddressList = new ArrayList<>();
        List<String> CarNumberList = new ArrayList<>();
        List<String> IPList = new ArrayList<>();
        List<String> insensitiveList = new ArrayList<>();
        List<String> desensitizedName = new ArrayList<>();
        List<String> phoneMaskList = new ArrayList<>();
        List<String> identitycardMaskList = new ArrayList<>();
        List<String> iccidList = new ArrayList<>();
        List<String> vinList = new ArrayList<>();
        List<String> msisdnList = new ArrayList<>();

        for (String data : dataToBeIdentifiedList) {
            if (ProRuleFactory.checkDataByProRule(data, "P_IDCODE")) {  //检测身份证号   <高>
                IDCardList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKPASSPORTCARD")) { //检测护照号  <高>
                PassPortCardList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKOFFICERCARD")) {  //明文军官证号码  <高>
                OfficerCardList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_PHONENUMBER")) { //检测手机号码  <高>
                MobileNumberList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_NAME")) { //检测姓名  <高>
                if (!data.contains("省") && !data.contains("市") && !data.contains("区") && !data.contains("县")) {
                    NameList.add(data);
                }
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKHMPASSCHECK")) {   //检测港澳通行证  <高>
                HMPassCheckList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKBANKCARD")) { //检测银行卡号  <高>
                BankCardList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_ADDRESS")) {  //检测地址   <中>
                AddressList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKCARNUMBERNO")) {  //检测车牌号  <中>
                CarNumberList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKIP")) {  //检测IP  <中>
                IPList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKNAMEMASK")) {  //检测姓名脱敏不规范  <中>
                desensitizedName.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKPHONEMASK")) {  //检测手机号脱敏不规范  <中>
                phoneMaskList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKIDENTITYCARDMASK")) {  //检测身份证号脱敏不规范  <中>
                identitycardMaskList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKVINCODE")) {  //检测 VIN码  <中>
                vinList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKICCID")) {  //检测 ICCID  <中>
                iccidList.add(data);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKMSISDN")) {  //物联网MSISDN  <中>
                msisdnList.add(data);
            } else {
                insensitiveList.add(data);
            }
        }

        sensitiveDataDecomposition.put("明文身份证号", IDCardList);
        sensitiveDataDecomposition.put("明文护照号", PassPortCardList);
        sensitiveDataDecomposition.put("明文军官证号码", OfficerCardList);
        sensitiveDataDecomposition.put("明文手机号", MobileNumberList);
        sensitiveDataDecomposition.put("明文姓名", NameList);
        sensitiveDataDecomposition.put("明文港澳通行证", HMPassCheckList);
        sensitiveDataDecomposition.put("明文银行卡号", BankCardList);
        sensitiveDataDecomposition.put("明文地址", AddressList);
        sensitiveDataDecomposition.put("明文车牌号", CarNumberList);
        sensitiveDataDecomposition.put("明文IP", IPList);
        sensitiveDataDecomposition.put("姓名脱敏不规范", desensitizedName);
        sensitiveDataDecomposition.put("手机号脱敏不规范", phoneMaskList);
        sensitiveDataDecomposition.put("身份证号脱敏不规范", identitycardMaskList);
        sensitiveDataDecomposition.put("明文ICCID", iccidList);
        sensitiveDataDecomposition.put("明文VIN码", vinList);
        sensitiveDataDecomposition.put("明文物联网MSISDN", msisdnList);
        sensitiveDataDecomposition.put("正常", insensitiveList);

        return sensitiveDataDecomposition;
    }


    /**
     * 报警级别定义
     *
     * @param sensitiveDataMap 敏感数据
     */
    public static String alarmLevelDefinition(Map<String, List<String>> sensitiveDataMap) {
        if (!sensitiveDataMap.isEmpty()) {
            if (sensitiveDataMap.containsKey("明文身份证号") || sensitiveDataMap.containsKey("明文护照号") || sensitiveDataMap.containsKey("明文军官证号码") ||
                    sensitiveDataMap.containsKey("明文手机号") || sensitiveDataMap.containsKey("明文车牌号") || sensitiveDataMap.containsKey("明文港澳通行证") ||
                    sensitiveDataMap.containsKey("明文银行卡号")) {
                return Const.RISK_HIGH;
            } else if (sensitiveDataMap.containsKey("明文地址") || sensitiveDataMap.containsKey("明文姓名") || sensitiveDataMap.containsKey("明文IP")) {
                return Const.RISK_MIDDLE;
            } else if (sensitiveDataMap.containsKey("正常")) {
                return Const.RISK_NOT;
            }
        }
        return Const.RISK_NOT;
    }


    /**
     * 敏感数据分类
     *
     * @param dataToBeIdentifiedList 待识别数据列表
     */
    public static Set<String> sensitiveDataStatistics(List<String> dataToBeIdentifiedList) {

        Set<String> sensitiveDataDecomposition = new HashSet<>();

        for (String data : dataToBeIdentifiedList) {
            if (ProRuleFactory.checkDataByProRule(data, "P_IDCODE")) {  //检测身份证号   <高>
                sensitiveDataDecomposition.add(Const.PAL_IDENTITY_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKPASSPORTCARD")) { //检测护照号  <高>
                sensitiveDataDecomposition.add(Const.IC_PASSPORTCARD_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKOFFICERCARD")) {  //明文军官证号码  <高>
                sensitiveDataDecomposition.add(Const.IC_OffICERCARD_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_PHONENUMBER")) { //检测手机号码  <高>
                sensitiveDataDecomposition.add(Const.PAL_MOBILENUMBER_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_NAME")) { //检测姓名  <高>
                if (!data.contains("省") && !data.contains("市") && !data.contains("区") && !data.contains("县")) {
                    sensitiveDataDecomposition.add(Const.IC_NAME_SIGN);
                }
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKHMPASSCHECK")) {   //检测港澳通行证  <高>
                sensitiveDataDecomposition.add(Const.IC_HMPASSCODE_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKBANKCARD")) { //检测银行卡号  <高>
                sensitiveDataDecomposition.add(Const.IC_BANKCARD_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_ADDRESS")) {  //检测地址   <中>
                sensitiveDataDecomposition.add(Const.IC_ADDRESS_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKCARNUMBERNO")) {  //检测车牌号  <中>
                sensitiveDataDecomposition.add(Const.IC_CARNUMBERNO_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKIP")) {  //检测IP  <中>
                sensitiveDataDecomposition.add(Const.IC_IP_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKVINCODE")) {  //检测 VIN码  <中>
                sensitiveDataDecomposition.add(Const.IC_ICCID_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKICCID")) {  //检测 ICCID  <中>
                sensitiveDataDecomposition.add(Const.IC_VIN_SIGN);
            } else if (ProRuleFactory.checkDataByProRule(data, "P_CHECKMSISDN")) {  //物联网MSISDN  <中>
                sensitiveDataDecomposition.add(Const.IC_MSISDN_SIGN);
            }
        }

        return sensitiveDataDecomposition;
    }


    /**
     * 报警级别定义
     *
     * @param sensitiveDataMap 敏感数据
     */
    public static String alarmLevelDefinition(Set<String> sensitiveDataMap) {
        if (!sensitiveDataMap.isEmpty()) {
            if (sensitiveDataMap.contains("明文身份证号") || sensitiveDataMap.contains("明文护照号") || sensitiveDataMap.contains("明文军官证号码") ||
                    sensitiveDataMap.contains("明文手机号") || sensitiveDataMap.contains("明文车牌号") || sensitiveDataMap.contains("明文港澳通行证") ||
                    sensitiveDataMap.contains("明文银行卡号")) {
                return Const.INTERFACEDISCOVERY_RISK_HIGH;
            } else if (sensitiveDataMap.contains("明文地址") || sensitiveDataMap.contains("明文姓名") || sensitiveDataMap.contains("明文IP")) {
                return Const.INTERFACEDISCOVERY_RISK_LOW;
            } else if (sensitiveDataMap.contains("正常")) {
                return Const.INTERFACEDISCOVERY_RISK_NOT;
            }
        }
        return Const.INTERFACEDISCOVERY_RISK_NOT;
    }

}
