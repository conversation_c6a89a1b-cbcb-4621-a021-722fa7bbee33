package com.wzsec.clean.common.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;


/**
 * pacp文件加解密方法
 */
public class PcapAesEncryption {

    private static final String KEY = "AESCt$<EMAIL>";

    public static void main(String[] args) throws Exception {
        String originalPath = "E:\\pcap\\pcapcleanbeforepath\\111\\1.pcap";
        String writePath = "E:\\pcap\\pcapcleanbeforepath\\111\\encrypted.pcap";
        cryptographicWrite(originalPath, writePath);

    }

    /**
     * 解密加密的PCAP内容
     *
     * @param originalPath 原始路径
     * @param writePath    写入路径
     * @throws Exception
     */
    public static void decryptEncryptedWrite(String originalPath, String writePath) throws Exception {
        byte[] decrypted = decrypt(Files.readAllBytes(Paths.get(originalPath)));
        Files.write(Paths.get(writePath), decrypted);
    }

    /**
     * 加密写入
     *
     * @param originalPath 原始路径
     * @param writePath    写入路径
     * @throws Exception
     */
    public static void cryptographicWrite(String originalPath, String writePath) throws Exception {
        byte[] pcapBytes = Files.readAllBytes(Paths.get(originalPath));

        // 加密PCAP内容
        byte[] encrypted = encrypt(pcapBytes);
        Files.write(Paths.get(writePath), encrypted);
    }

    /**
     * 加密
     *
     * @param value
     * @return
     * @throws Exception
     */
    public static byte[] encrypt(byte[] value) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(KEY.getBytes(StandardCharsets.UTF_8), "AES");

        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);

        return cipher.doFinal(value);
    }

    /**
     * 解密
     *
     * @param encrypted
     * @return
     * @throws Exception
     */
    public static byte[] decrypt(byte[] encrypted) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(KEY.getBytes(StandardCharsets.UTF_8), "AES");

        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);

        return cipher.doFinal(encrypted);
    }

}
