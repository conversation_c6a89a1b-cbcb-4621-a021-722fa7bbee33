package com.wzsec.clean.common.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.clean.common.analysis.thread.PcapCleanThread;
import com.wzsec.clean.common.rule.ProCommonRule;
import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.ES7Util;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.*;
import com.wzsec.clean.modules.service.PcapService;
import com.wzsec.clean.modules.service.PcapStrategyConfigService;
import com.wzsec.clean.modules.service.PcapTaskService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;


/**
 * HTTP、FTP流量检测，清洗后进行检测,多线程方式 ES 7.9.1
 */
@Component
@Transactional
public class PcapParser_v3 {


    private static Logger logger = LoggerFactory.getLogger(PcapParser_v3.class);

    private static Map<String, Integer> countMap = new HashMap<>();
    private static Map<String, HTTPResultDetail> resultMap = new HashMap<>();
    // 敏感数据检测管理查询(key=敏感数据，value=敏感数据英文名)
    public static HashMap<String, String> httpSensitivedataMap = new HashMap<>();
    public static HashMap<String, String> ftpSensitivedataMap = new HashMap<>();
    //统计每行包含结果类型
    public static HashSet<String> resultTypeSet = new HashSet<>();
    // 明文统计次数结果Map
    public static Map<String, Map<String, Integer>> sensitiveDataCountResultMap = new HashMap<>();
    //结果类型
    public static Map<String, Integer> resTypeMap = new HashMap<>();
    //检测总次数
    public static HashMap<String, Integer> totalCountByMethodMap = new HashMap<>();
    //FTP 文件list
    public static List<String> ftpList = new ArrayList<>();
    //FTP 结果Map保存文件信息
    public static Map<String, List<String>> ftpListMap = new HashMap<>();
    //FTP 结果Map对象信息
    public static Map<String, FTPResultDetail> ftpEntityMap = new HashMap<>();
    //策略Map
    public static Map<String, PcapStrategyConfig> strategyConfigMap = new HashMap<>();
    //结果正常id
    private static int normalTypeId = 10;

    private static PcapService pcapService;
    private static PcapStrategyConfigService pcapStrategyConfigService;
    private static PcapTaskService pcapTaskService;

    @Autowired
    public void setService(PcapService pcapService, PcapStrategyConfigService pcapStrategyConfigService, PcapTaskService pcapTaskService) {
        PcapParser_v3.pcapService = pcapService;
        PcapParser_v3.pcapStrategyConfigService = pcapStrategyConfigService;
        PcapParser_v3.pcapTaskService = pcapTaskService;
    }

    /**
     * 从ES检测pcap文件
     */
    public static void checkPcapFromES(PcapTask pcapTask) {
        try {
            // 手机号白名单初始化
            Const.whitePhoneList = pcapService.getFileListInfo(Const.PHONEDETECTION, Const.WHITELIST);
            // 敏感数据检测管理查询
            httpSensitivedataMap = pcapService.selectSensitiveData(Const.INTERFACE_FLOW_SIGN);
            ftpSensitivedataMap = pcapService.selectSensitiveData(Const.FTP_FLOW_SIGN);
            //结果类型
            resTypeMap = pcapService.selectResultType();

            //开始时间
            String startTime = TimeUtils.getNowTime();


            String pcapCleanBeforePath = ConfigurationManager.getProperty("PcapCleanBeforePath").trim();
            String pcapDate = ConfigurationManager.getProperty("PcapCleanBeforeDate").trim();
            int IntervalDay = Integer.parseInt(ConfigurationManager.getProperty("PcapClean.IntervalDay").trim());

            //pcap检测日期
            if ("".equals(pcapDate) || pcapDate == null) {
                //默认检测前一天
                pcapDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", IntervalDay);
            }

            //ES索引
            String index = ConfigurationManager.getProperty("netflow.es.index.prefix");
            String username = ConfigurationManager.getProperty("netflow.es.username");
            String password = ConfigurationManager.getProperty("netflow.es.password");
            // String ip = ConfigurationManager.getProperty("netflow.es.ip");
            // int port = Integer.parseInt(ConfigurationManager.getProperty("netflow.es.port"));

            String hosts = ConfigurationManager.getProperty("netflow.es.hostlist");
            long timeValueMinutes = Long.parseLong(ConfigurationManager.getProperty("netflow.es.scrolltimeValueMinutes"));
            int size = Integer.parseInt(ConfigurationManager.getProperty("netflow.es.scrollsize"));

            //ES检测时间
            String flowcheckdate = "";
            if (StringUtils.isEmpty(pcapDate)) {
                //获取前一天日期
                flowcheckdate = TimeUtils.getYesterdayByCalendar("yyyy-MM-dd", IntervalDay);
            } else {
                //将20201111格式转成2020-11-11格式
                flowcheckdate = TimeUtils.convertStrDateToStr(pcapDate, "yyyyMMdd", "yyyy-MM-dd");
            }

            //清洗前路径
            String parserBeforeDir = "";
            if (pcapCleanBeforePath.endsWith(File.separator)) {
                parserBeforeDir = pcapCleanBeforePath + pcapDate;
            } else {
                parserBeforeDir = pcapCleanBeforePath + File.separator + pcapDate;
            }
            logger.info("清洗前路径：" + parserBeforeDir);

            //pcap文件清洗
            PcapCleanThread.pcapCleanToES(parserBeforeDir, index, flowcheckdate, pcapTask);
            String HttpIndexName = "";
            String FtpIndexName = "";
            if (index.endsWith(Const.AUDIT_SPLIT_JOIN)) {
                HttpIndexName = index + Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + flowcheckdate;
                FtpIndexName = index + Const.FTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + flowcheckdate;
            } else {
                HttpIndexName = index + Const.AUDIT_SPLIT_JOIN + Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + flowcheckdate;
                FtpIndexName = index + Const.AUDIT_SPLIT_JOIN + Const.FTP_SIGN.toLowerCase() + Const.AUDIT_SPLIT_JOIN + flowcheckdate;
            }

            //处理ES中HTTP数据
            searchCheckByPageScroll(flowcheckdate, username, password, hosts, timeValueMinutes, size, HttpIndexName);

            //处理ES中FTP数据
            searchCheckByPageScroll(flowcheckdate, username, password, hosts, timeValueMinutes, size, FtpIndexName);

            //HTTP流量结果统计保存信息
            recordHTTPCheckResultInfo(pcapTask.getTaskname(), startTime);

            //FTP流量结果统计保存信息
            recordFTPCheckResultInfo(pcapTask.getTaskname(), startTime);

            //结束时间
            String endTime = TimeUtils.getNowTime();
            logger.info("开始时间：" + startTime);
            logger.info("结束时间：" + endTime);
            int time = TimeUtils.getTimeSecondsByBothDate(startTime, endTime);
            logger.info("任务号" + pcapTask.getTaskname() + ",检测pcap文件共耗时：" + time + "秒");

            //变量销毁
            destruction();
        } catch (Exception e) {
            logger.info("检测流量结果统计出现异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * FTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * <AUTHOR>
     * @date 2020-11-02
     */
    private static void recordFTPCheckResultInfo(String taskNum, String startTime) {
        if (ftpListMap.size() > 0) {
            Map<String, FTPResultDetail> detailMap = new HashMap<>();
            Map<String, FTPResult> outlineMap = new HashMap<>();
            for (String key : ftpListMap.keySet()) {
                if (ftpListMap.containsKey(key)) {
                    FTPResultDetail ftpEntity = ftpEntityMap.get(key);
                    List<String> list = ftpListMap.get(key);
                    if (list != null) {
                        List<Integer> resultTypeList = checkSensitiveDataByUseRule(Const.FTP_SIGN, key, list);

                        if (resultTypeList.size() == 0) {
                            FTPResultDetail ftpDataNew = new FTPResultDetail();
                            String resKey = key + Const.AUDIT_SPLIT_JOIN + normalTypeId;
                            //根据id查询结果类型
                            Map<String, Integer> resutlMap = pcapService.getResultTypeById(normalTypeId);
                            ftpDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                            if (!ftpDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                                ftpDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype())));
                            }
                            ftpDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
                            ftpDataNew.setChecktime(startTime);
                            ftpDataNew.setDesip(ftpEntity.getDesip());
                            ftpDataNew.setDesport(ftpEntity.getDesport());
                            ftpDataNew.setSourceip(ftpEntity.getSourceip());
                            ftpDataNew.setSourceport(ftpEntity.getSourceport());
                            ftpDataNew.setOperationtime(ftpEntity.getOperationtime());

                            int checkCount = 1;
                            Integer totalCount = 1;
                            ftpDataNew.setResulttypecount(String.valueOf(checkCount));
                            ftpDataNew.setTotalcount(String.valueOf(totalCount));
                            double rate = 100 * ((double) checkCount / totalCount);
                            BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                            double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            ftpDataNew.setRatio(String.valueOf(ratio));
                            ftpDataNew.setSign(ftpEntity.getSign());
                            ftpDataNew.setUsername(ftpEntity.getUsername());
                            ftpDataNew.setPassword(ftpEntity.getPassword());
                            ftpDataNew.setFilename(ftpEntity.getFilename());
                            ftpDataNew.setFilepath(ftpEntity.getFilepath());
                            ftpDataNew.setFilesize(ftpEntity.getFilesize());
                            detailMap.put(resKey, ftpDataNew);


                            //FTP详情统计
                            FTPResult ftpResult = new FTPResult();
                            ftpResult.setTaskname(taskNum);
                            ftpResult.setChecktime(startTime);
                            ftpResult.setProtocol(Const.FTP_SIGN);
                            ftpResult.setSourceip(ftpDataNew.getSourceip());
                            ftpResult.setSourceport(ftpDataNew.getSourceport());
                            ftpResult.setDesip(ftpDataNew.getDesip());
                            ftpResult.setDesport(ftpDataNew.getDesport());

                            String[] keys = key.split(Const.AUDIT_SPLIT_JOIN);

                            String riskKey = "";
                            if (keys.length > 4) {
                                riskKey = keys[0] + Const.AUDIT_SPLIT_JOIN + keys[1] + Const.AUDIT_SPLIT_JOIN +
                                        keys[2] + Const.AUDIT_SPLIT_JOIN + keys[3] + Const.AUDIT_SPLIT_JOIN +
                                        ftpDataNew.getRisk();
                            }

                            if (outlineMap.containsKey(riskKey)) {
                                FTPResult result = outlineMap.get(riskKey);
                                result.setRisk(ftpDataNew.getRisk());
                                result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
                                outlineMap.put(riskKey, result);
                            } else {
                                ftpResult.setRisk(ftpDataNew.getRisk());
                                ftpResult.setRiskcount("1");
                                outlineMap.put(riskKey, ftpResult);
                            }
                        } else {
                            for (Integer resyltTypeNum : resultTypeList) {
                                FTPResultDetail ftpDataNew = new FTPResultDetail();
                                String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                                //根据id查询结果类型
                                Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);
                                ftpDataNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                                if (!ftpDataNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                                    ftpDataNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype())));
                                }
                                ftpDataNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");
                                ftpDataNew.setChecktime(startTime);
                                ftpDataNew.setDesip(ftpEntity.getDesip());
                                ftpDataNew.setDesport(ftpEntity.getDesport());
                                ftpDataNew.setSourceip(ftpEntity.getSourceip());
                                ftpDataNew.setSourceport(ftpEntity.getSourceport());
                                ftpDataNew.setOperationtime(ftpEntity.getOperationtime());

                                Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + ftpDataNew.getResulttype());
                                Collection<Integer> values = checkCountMap.values();
                                int checkCount = 0;
                                for (Integer value : values) {
                                    checkCount += value;
                                }
                                Integer totalCount = totalCountByMethodMap.get(key);
                                ftpDataNew.setResulttypecount(String.valueOf(checkCount));
                                ftpDataNew.setTotalcount(String.valueOf(totalCount));
                                double rate = 100 * ((double) checkCount / totalCount);
                                BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                                double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                ftpDataNew.setRatio(String.valueOf(ratio));
                                ftpDataNew.setSign(ftpEntity.getSign());
                                ftpDataNew.setUsername(ftpEntity.getUsername());
                                ftpDataNew.setPassword(ftpEntity.getPassword());
                                ftpDataNew.setFilename(ftpEntity.getFilename());
                                ftpDataNew.setFilepath(ftpEntity.getFilepath());
                                ftpDataNew.setFilesize(ftpEntity.getFilesize());
                                detailMap.put(resKey, ftpDataNew);


                                //FTP详情统计
                                FTPResult ftpResult = new FTPResult();
                                ftpResult.setTaskname(taskNum);
                                ftpResult.setChecktime(startTime);
                                ftpResult.setProtocol(Const.FTP_SIGN);
                                ftpResult.setSourceip(ftpDataNew.getSourceip());
                                ftpResult.setSourceport(ftpDataNew.getSourceport());
                                ftpResult.setDesip(ftpDataNew.getDesip());
                                ftpResult.setDesport(ftpDataNew.getDesport());

                                String[] keys = key.split(Const.AUDIT_SPLIT_JOIN);

                                String riskKey = "";
                                if (keys.length > 4) {
                                    riskKey = keys[0] + Const.AUDIT_SPLIT_JOIN + keys[1] + Const.AUDIT_SPLIT_JOIN +
                                            keys[2] + Const.AUDIT_SPLIT_JOIN + keys[3] + Const.AUDIT_SPLIT_JOIN +
                                            ftpDataNew.getRisk();
                                }

                                if (outlineMap.containsKey(riskKey)) {
                                    FTPResult result = outlineMap.get(riskKey);
                                    result.setRisk(ftpDataNew.getRisk());
                                    result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
                                    outlineMap.put(riskKey, result);
                                } else {
                                    ftpResult.setRisk(ftpDataNew.getRisk());
                                    ftpResult.setRiskcount("1");
                                    outlineMap.put(riskKey, ftpResult);
                                }
                            }
                        }
                    }
                }
            }

            if (detailMap != null) {
                Collection<FTPResultDetail> pcapDatas = detailMap.values();
                for (FTPResultDetail ftpData : pcapDatas) {
                    pcapService.saveFtpResultDetail(ftpData);
                }
            }
            logger.info("保存FTP流量检测详情统计完成！");
            if (outlineMap != null) {
                Collection<FTPResult> ftpResults = outlineMap.values();
                for (FTPResult ftpResult : ftpResults) {
                    pcapService.saveFTPResult(ftpResult);
                }
            }
            logger.info("保存FTP流量检测概要统计完成！");
        } else {
            //logger.info("FTP流量检测统计结果为空！");
        }

    }


    /**
     * HTTP流量结果统计保存信息
     *
     * @param taskNum
     * @param startTime
     * <AUTHOR>
     * @date 2020-11-02
     */
    private static void recordHTTPCheckResultInfo(String taskNum, String startTime) {

        Map<String, HTTPResultDetail> detailMap = new HashMap<>();
        Map<String, HTTPResult> outlineMap = new HashMap<>();

        //保存pcap文件信息
        if (resultMap != null && resultMap.size() != 0) {

            Set<String> keySet = resultMap.keySet();
            for (String key : keySet) {
                HTTPResultDetail httpResultDetail = resultMap.get(key);

                String checkRule = "p_checkSensitiveData";
                // 响应参数
                String[] datas = httpResultDetail.getRescontent().split(",|:|\"|\\{|}|\\[|]|\r\n");
                ArrayList<String> list = convertArrToList(datas);


                if (list != null) {

                    List<Integer> resultTypeList = checkSensitiveDataByUseRule(Const.HTTP_SIGN, key, list);

                    //概要统计接口调用总数
                    String totalcount = "";
                    String countKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN));
                    if (countMap.containsKey(countKey)) {
                        totalcount = String.valueOf(countMap.get(countKey));
                    }

                    if (resultTypeList != null) {
                        for (Integer resyltTypeNum : resultTypeList) {

                            HTTPResultDetail httpResultDetailNew = new HTTPResultDetail();
                            httpResultDetailNew.setSeqnumber(httpResultDetail.getSeqnumber());
                            httpResultDetailNew.setInterfaceuri(httpResultDetail.getInterfaceuri());
                            httpResultDetailNew.setChecktime(startTime);
                            httpResultDetailNew.setStarttime(httpResultDetail.getStarttime());
                            httpResultDetailNew.setEndtime(httpResultDetail.getEndtime());
                            httpResultDetailNew.setSourceip(httpResultDetail.getSourceip());
                            httpResultDetailNew.setSourceport(httpResultDetail.getSourceport());
                            httpResultDetailNew.setDesip(httpResultDetail.getDesip());
                            httpResultDetailNew.setDesport(httpResultDetail.getDesport());
                            httpResultDetailNew.setRescontent(httpResultDetail.getRescontent());
                            httpResultDetailNew.setReqcontent(httpResultDetail.getReqcontent());
                            httpResultDetailNew.setResstatus(httpResultDetail.getResstatus());
                            httpResultDetailNew.setResstatuscode(httpResultDetail.getResstatuscode());
                            httpResultDetailNew.setReqmethod(httpResultDetail.getReqmethod());
                            httpResultDetailNew.setUserid(httpResultDetail.getUserid());
                            httpResultDetailNew.setUsername(httpResultDetail.getUsername());
                            httpResultDetailNew.setCheckrule(checkRule);

                            String resKey = key + Const.AUDIT_SPLIT_JOIN + resyltTypeNum;
                            //根据id查询结果类型
                            Map<String, Integer> resutlMap = pcapService.getResultTypeById(resyltTypeNum);

                            httpResultDetailNew.setResulttype(resutlMap.keySet().toArray()[0].toString());
                            if (!httpResultDetailNew.getResulttype().equals(Const.NORMAL_SIGN)) {
                                httpResultDetailNew.setSensitivedata(mapSort(sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getResulttype())));
                            }
                            httpResultDetailNew.setRisk(resutlMap.get(resutlMap.keySet().toArray()[0].toString()) != 0 ? "1" : "0");

                            Map<String, Integer> checkCountMap = sensitiveDataCountResultMap.get(key + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getResulttype());
                            Collection<Integer> values = checkCountMap.values();
                            int checkCount = 0;
                            for (Integer value : values) {
                                checkCount += value;
                            }
                            Integer totalCount = totalCountByMethodMap.get(key);
                            httpResultDetailNew.setResulttypecount(String.valueOf(checkCount));
                            httpResultDetailNew.setChecktotalcount(String.valueOf(totalCount));
                            double rate = 100 * ((double) checkCount / totalCount);
                            BigDecimal bDec = new BigDecimal(String.valueOf(rate));
                            double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            httpResultDetailNew.setRatio(String.valueOf(ratio));
                            detailMap.put(resKey, httpResultDetailNew);


                            String riskKey = key.substring(0, key.lastIndexOf(Const.AUDIT_SPLIT_JOIN)) + Const.AUDIT_SPLIT_JOIN + httpResultDetailNew.getRisk();
                            if (outlineMap.containsKey(riskKey)) {
                                HTTPResult result = outlineMap.get(riskKey);
                                result.setRisk(httpResultDetailNew.getRisk());
                                result.setRiskcount(String.valueOf(Integer.parseInt(result.getRiskcount()) + 1));
                                outlineMap.put(riskKey, result);
                            } else {
                                HTTPResult httpResult = new HTTPResult();
                                httpResult.setTaskname(taskNum);
                                httpResult.setChecktime(startTime);
                                httpResult.setInterfaceuri(httpResultDetail.getInterfaceuri());
                                httpResult.setSourceip(httpResultDetail.getSourceip());
                                httpResult.setSourceport(httpResultDetail.getSourceport());
                                httpResult.setDesip(httpResultDetail.getDesip());
                                httpResult.setDesport(httpResultDetail.getDesport());
                                httpResult.setProtocol(Const.HTTP_SIGN);
                                httpResult.setRisk(httpResultDetailNew.getRisk());
                                httpResult.setRiskcount("1");
                                httpResult.setTotalcount(totalcount);
                                outlineMap.put(riskKey, httpResult);
                            }
                        }
                    }

                }
            }
            logger.info("保存接口流量检测概要统计完成！");
            //保存详情结果
            if (detailMap != null) {
                Collection<HTTPResultDetail> httpResultDetails = detailMap.values();

                for (HTTPResultDetail httpResultDetail : httpResultDetails) {
                    pcapService.saveHttpResultDetail(httpResultDetail);
                }
                logger.info("保存接口流量检测详情统计完成！");
            }

            if (outlineMap != null) {
                Collection<HTTPResult> httpResults = outlineMap.values();
                for (HTTPResult httpResult : httpResults) {
                    pcapService.saveHttpResult(httpResult);
                }
            }

        } else {
            //logger.info("接口流量检测统计结果为空！");
        }
    }

    /**
     * 解析清洗后的HTTP JSON文件
     *
     * @param strLine
     * <AUTHOR>
     * @date 2020-11-05
     */
    public static void cleanHttpJsonParse(String strLine) {
        JSONObject jobj = JSONObject.parseObject(strLine);
        String id = jobj.getString("id");
        String apiId = jobj.getString("seqnumber");//序列号/确认号
        String interfaceuri = jobj.getString("interfaceuri");//接口名称
        String sourceip = jobj.getString("sourceip");//原地址 4字节
        String apiuri = jobj.getString("sourceport");//原端口
        String desip = jobj.getString("desip");//目的地址
        String desport = jobj.getString("desport");// 目的端口
        String clientip = jobj.getString("reqmethod");//请求方法
        String code = jobj.getString("resstatuscode");//响应状态码
        String resstatus = jobj.getString("resstatus");//响应状态
        String reqcontent = jobj.getString("reqcontent");//请求内容
        String result = jobj.getString("rescontent");//响应内容
        String checkrule = jobj.getString("checkrule");//检测规则
        String accesscardId = jobj.getString("resulttype");//结果类型
        String datatype = jobj.getString("resulttypecount");//结果类型统计数量
        String createDate = jobj.getString("checktotalcount");//检测总数量
        String ratio = jobj.getString("ratio");//比例
        String sensitivedata = jobj.getString("sensitivedata");//敏感数据及数量
//            String risk = jobj.getString("risk");//风险程度
//            String userid = jobj.getString("userid");//用户id
//            String username = jobj.getString("username");//用户名称
        String starttime = jobj.getString("calltime");//操作时间  == 请求时间
        Long endtime = jobj.getLong("endtime");//操作时间
        String checktime = jobj.getString("checktime");//检测时间
//		JSONObject httpObj = JSONObject.parseObject(strLine);
//		JSONObject reqObj = JSONObject.parseObject(httpObj.getString("req"));
//		JSONObject resObj = JSONObject.parseObject(httpObj.getString("res"));

//		HTTPResultDetail httpReqResult = JSON.toJavaObject(reqObj,HTTPResultDetail.class);
        HTTPResultDetail httpReqResult = new HTTPResultDetail();
//		HTTPResultDetail httpResResult = JSON.toJavaObject(resObj,HTTPResultDetail.class);

//		httpReqResult.setResstatus(httpResResult.getResstatus());
//		httpReqResult.setResstatuscode(httpResResult.getResstatuscode());
//		httpReqResult.setRescontent(httpResResult.getRescontent());
//		httpReqResult.setEndtime(httpResResult.getEndtime());

        httpReqResult.setResstatus(resstatus);
        httpReqResult.setResstatuscode(code);
        httpReqResult.setRescontent(result);

        // reqKey = 客户端IP-客户端端口-服务端IP-服务端端口-接口URI
        String reqKey = httpReqResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpReqResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                httpReqResult.getDesip() + Const.AUDIT_SPLIT_JOIN + httpReqResult.getDesport() + Const.AUDIT_SPLIT_JOIN +
                httpReqResult.getInterfaceuri();
//		String resKey = httpResResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpResResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
//				httpResResult.getDesip() + Const.AUDIT_SPLIT_JOIN + httpResResult.getDesport() + Const.AUDIT_SPLIT_JOIN +
//				httpResResult.getInterfaceuri();

        String resultReqKey = reqKey + Const.AUDIT_SPLIT_JOIN + httpReqResult.getStarttime();
//		String resultResKey = resKey+Const.AUDIT_SPLIT_JOIN+httpResResult.getOperationtime();

//		String reqtime = TimeUtils.TimestampToDateStrNew(starttime);
        httpReqResult.setStarttime(starttime);
//		String restime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResResult.getEndtime()));
//		String restime = TimeUtils.TimestampToDateStrNew(starttime);
        httpReqResult.setEndtime(starttime);
        //统计接口调用次数
        interfaceInfoCount(reqKey);
//		interfaceInfoCount(resKey);

        //保存http结果
        resultMap.put(resultReqKey, httpReqResult);
//		resultMap.put(resultResKey,httpResResult);


//
//		JSONObject httpObj = JSONObject.parseObject(strLine);
//		JSONObject reqObj = JSONObject.parseObject(httpObj.getString("req"));
//		JSONObject resObj = JSONObject.parseObject(httpObj.getString("res"));
//
//		HTTPResultDetail httpReqResult = JSON.toJavaObject(reqObj,HTTPResultDetail.class);
//		HTTPResultDetail httpResResult = JSON.toJavaObject(resObj,HTTPResultDetail.class);
//
//		httpReqResult.setResstatus(httpResResult.getResstatus());
//		httpReqResult.setResstatuscode(httpResResult.getResstatuscode());
//		httpReqResult.setRescontent(httpResResult.getRescontent());
//		httpReqResult.setEndtime(httpResResult.getEndtime());
//
//		// reqKey = 客户端IP-客户端端口-服务端IP-服务端端口-接口URI
//		String reqKey = httpReqResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpReqResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
//				httpReqResult.getDesip() + Const.AUDIT_SPLIT_JOIN + httpReqResult.getDesport() + Const.AUDIT_SPLIT_JOIN +
//				httpReqResult.getInterfaceuri();
////		String resKey = httpResResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpResResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
////				httpResResult.getDesip() + Const.AUDIT_SPLIT_JOIN + httpResResult.getDesport() + Const.AUDIT_SPLIT_JOIN +
////				httpResResult.getInterfaceuri();
//
//		String resultReqKey = reqKey+Const.AUDIT_SPLIT_JOIN+httpReqResult.getStarttime();
////		String resultResKey = resKey+Const.AUDIT_SPLIT_JOIN+httpResResult.getOperationtime();
//
//		String reqtime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpReqResult.getStarttime()));
//		httpReqResult.setStarttime(reqtime);
//		String restime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResResult.getEndtime()));
//		httpReqResult.setEndtime(restime);
//		//统计接口调用次数
//		interfaceInfoCount(reqKey);
////		interfaceInfoCount(resKey);
//
//		//保存http结果
//		resultMap.put(resultReqKey,httpReqResult);
////		resultMap.put(resultResKey,httpResResult);
    }

    /**
     * 解析清洗后的FTP JSON文件
     *
     * @param strLine
     * <AUTHOR>
     * @date 2020-11-05
     */
    private static void cleanFTPJsonParse(String strLine) {

        JSONObject ftpObj = JSONObject.parseObject(strLine);
        FTPResultDetail ftpResult = JSON.toJavaObject(ftpObj, FTPResultDetail.class);

        String key = ftpResult.getSourceip() + Const.AUDIT_SPLIT_JOIN + ftpResult.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                ftpResult.getDesip() + Const.AUDIT_SPLIT_JOIN + ftpResult.getDesport();

        String reskey = "";
        if (Const.FTP_STOR_SIGN.equals(ftpResult.getSign())) {
            reskey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResult.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpResult.getFilename();
        }
        if (Const.FTP_RETR_SIGN.equals(ftpResult.getSign())) {
            reskey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResult.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpResult.getFilename();
        }
        String ftptime = TimeUtils.TimestampToDateStrNew(Long.parseLong(ftpResult.getOperationtime()));
        ftpResult.setOperationtime(ftptime);

		/*//从还原的文件夹下获取文件
		String ftpFileDir = ConfigurationManager.getProperty("ftpfileoutput.dir").trim();
		HashMap<String, String> filenameMap = new HashMap<>();
		getAllFileName(ftpFileDir,filenameMap);
		for (String filePath : filenameMap.keySet()) {
//			String fileKey = key + Const.AUDIT_SPLIT_JOIN +ftpResult.getOperationtime() +Const.AUDIT_SPLIT_JOIN+ ftpResult.getFilename();
			if (filenameMap.get(filePath).equals(ftpResult.getFilename())){
				dealSingleFileReadLine(filePath,ftpList);
			}
		}*/
        ftpEntityMap.put(reskey, ftpResult);
//		ftpListMap.put(reskey,ftpList);
//		ftpList=new ArrayList<>();
    }


    /**
     * 清洗数组中空字符串
     *
     * @param datas
     * <AUTHOR>
     * @date 2020-11-02
     */
    private static ArrayList<String> convertArrToList(String[] datas) {
        ArrayList<String> list = new ArrayList<>();
        for (String data : datas) {
            if (!data.equals("")) {
                list.add(data);
            }
        }
        return list;
    }


    /**
     * 获取一个文件夹下的所有文件全路径和文件名
     *
     * @param path
     * @param fileNameMap
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    public static void getAllFileName(String path, Map<String, String> fileNameMap) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
//					if (f.getAbsolutePath().endsWith("pcap")) {
                    fileNameMap.put(f.getAbsolutePath(), f.getName());
//					}
                }
            }
        }
    }


    /**
     * 统计接口检测总数
     *
     * @param key
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    private static void interfaceInfoCount(String key) {
        if (countMap.containsKey(key)) {
            countMap.put(key, countMap.get(key) + 1);
        } else {
            countMap.put(key, 1);
        }
    }


    /*
     *@Decription 变量销毁
     *<AUTHOR>
     *@date 2020/7/10
     */
    public static void destruction() {
        countMap = new HashMap<>();
        resultMap = new HashMap<>();
        httpSensitivedataMap = new HashMap<>();
        ftpSensitivedataMap = new HashMap<>();
        resultTypeSet = new HashSet<>();
        sensitiveDataCountResultMap = new HashMap<>();
        resTypeMap = new HashMap<>();
        totalCountByMethodMap = new HashMap<>();
        ftpList = new ArrayList<>();
        ftpListMap = new HashMap<>();
        ftpEntityMap = new HashMap<>();
        strategyConfigMap = new HashMap<>();
        PacketParser.dataMap = new HashMap<>();
        PacketParser.ftpUserMap = new HashMap<>();
        PacketParser.ftpPortMap = new HashMap<>();
        PacketParser.ftpDataTransferMap = new HashMap<>();
        PacketParser.ftpTransferMap = new HashMap<>();
        PacketParser.ftpResultMap = new HashMap<>();
        PacketParser.filetransferMap = new HashMap<>();
        PacketParser.transferByteMap = new HashMap<>();
        PacketParser.realityByteMap = new HashMap<>();
        PacketParser.fileByteMap = new HashMap<>();
        PacketParser.interfaceTransferMap = new HashMap<>();
    }


    /**
     * @Description:检测参数数据通过通用检测规则
     * <AUTHOR> by wangqi
     * @date 2020-09-03
     */
    private static List<Integer> checkSensitiveDataByUseRule(String sign, String strKey, List<String> list) {

        resultTypeSet = new HashSet<>();
        List<Integer> resultTypeList = null;
        if (Const.HTTP_SIGN.equals(sign)) {
            // 校验参数是否包含敏感数据
            resultTypeList = ProCommonRule.checkIsClearType(httpSensitivedataMap, sensitiveDataCountResultMap,
                    strKey, list.toArray(new String[list.size()]), resultTypeSet, resTypeMap);
        } else if (Const.FTP_SIGN.equals(sign)) {
            // 校验参数是否包含敏感数据
            resultTypeList = ProCommonRule.checkIsClearType(ftpSensitivedataMap, sensitiveDataCountResultMap,
                    strKey, list.toArray(new String[list.size()]), resultTypeSet, resTypeMap);
        }


        // 统计检测次数
        if (list.size() > 0) {
            if (null == totalCountByMethodMap) {
                totalCountByMethodMap = new HashMap<>();
                totalCountByMethodMap.put(strKey, list.size());
            } else {
                if (totalCountByMethodMap.containsKey(strKey)) {
                    totalCountByMethodMap.put(strKey, totalCountByMethodMap.get(strKey).intValue() + list.size());
                } else {
                    totalCountByMethodMap.put(strKey, list.size());
                }
            }
        }

        return resultTypeList;
    }

    /**
     * @param map:需要根据value排序的map
     * @Description：按照行为时间进行排序，方式采集时时间顺序错误导致审计出错
     * <AUTHOR>
     * @date 2019年12月9日 下午3:13:02
     */
    private static String mapSort(Map<String, Integer> map) {
        if (map == null || map.size() == 0) {
            return null;
        }
        ArrayList<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            // 定义一个比较器
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                Integer num1 = o1.getValue();
                Integer num2 = o2.getValue();
                return num2.compareTo(num1);
            }
        });
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<String, Integer> l : list) {
            jsonObject.put(l.getKey(), l.getValue());
        }
        return jsonObject.toString();
    }


    /**
     * 适合大量数据，根据ScrollId
     *
     * @throws IOException
     */
    public static void searchCheckByPageScroll(String checkdate, String username, String password, String hosts, long timeValueMinutes, int size, String index) {
        try {
            RestHighLevelClient client = ES7Util.getClient(username, password, hosts);
            // 存活时间，当索引数据量特别大时，出现超时可能性大，此值适当调大
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(timeValueMinutes));// 缓存存在的时长
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("@checktime", checkdate)));
            searchSourceBuilder.size(size);// 页大小
            // 设置请求
            SearchRequest searchRequest = new SearchRequest()
                    // ES7已经去掉type，查询时不加type
                    .indices(index).scroll(scroll).source(searchSourceBuilder);
            // 发起请求
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 读取响应
            String scrollId = searchResponse.getScrollId();
            System.out.println("结果总条数:" + searchResponse.getHits().getTotalHits().value);
            SearchHit[] searchHits = searchResponse.getHits().getHits();// 第一页数据
            int dataPageCount = 0, dataLineCount = 0;
            // 遍历搜索命中的数据，直到没有数据
            while (searchHits != null && searchHits.length > 0) {
                dataPageCount++;
                System.out.println("当前页大小：" + searchHits.length);
                if (searchHits != null && searchHits.length > 0) {
                    int i = 0;
                    for (SearchHit searchHit : searchHits) {
                        dataLineCount++;
                        i++;
                        String source = searchHit.getSourceAsString();
                        System.out.println("遍历第" + dataPageCount + "页第" + i + "条===" + source);
                        //判断是HTTP数据还是FTP
                        if (index.contains(Const.HTTP_SIGN.toLowerCase())) {
                            //HTTP
                            dealESHttpJsonData(source);
                        } else if (index.contains(Const.FTP_SIGN.toLowerCase())) {
                            //FTP
                            dealESFtpJsonData(source);
                        }

                    }
                }
                // 再次发送请求,并使用上次搜索结果的ScrollId
                // 设置请求
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                // 发起请求
                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                // 读取响应
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }
            System.out.println("数据总页数：" + dataPageCount);
            System.out.println("数据总数量：" + dataLineCount);

            // 清除缓存的scroll
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            boolean succeeded = clearScrollResponse.isSucceeded();// 是否清除成功
            System.out.println("清除scroll:" + succeeded);
            ES7Util.closeEsClient(client);
            System.out.println("已断开连接");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理ES中FTP数据
     *
     * @param source
     */
    private static void dealESFtpJsonData(String source) {
        FTPResultDetail ftpResultDetail = new FTPResultDetail();
        JSONObject ftpDataJson = JSONObject.parseObject(source);
        //ES字段前缀
        String ftp = Const.FTP_SIGN.toLowerCase() + Const.AUDIT_POINT_JOIN;

        String sourceip = ftpDataJson.getString(ftp + "sourceip");
        String sourceport = ftpDataJson.getString(ftp + "sourceport");
        String desip = ftpDataJson.getString(ftp + "desip");
        String desport = ftpDataJson.getString(ftp + "desport");
        String sign = ftpDataJson.getString(ftp + "sign");
        String operationtime = ftpDataJson.getString(ftp + "operationtime");
        String username = ftpDataJson.getString(ftp + "username");
        String password = ftpDataJson.getString(ftp + "password");
        String filename = ftpDataJson.getString(ftp + "filename");
        String filepath = ftpDataJson.getString(ftp + "filepath");
        String filesize = ftpDataJson.getString(ftp + "filesize");

        ftpResultDetail.setSourceip(sourceip);
        ftpResultDetail.setSourceport(sourceport);
        ftpResultDetail.setDesip(desip);
        ftpResultDetail.setDesport(desport);
        ftpResultDetail.setSign(sign);
        ftpResultDetail.setOperationtime(operationtime);
        ftpResultDetail.setUsername(username);
        ftpResultDetail.setPassword(password);
        ftpResultDetail.setFilename(filename);
        ftpResultDetail.setFilepath(filepath);
        ftpResultDetail.setFilesize(filesize);

        String key = ftpResultDetail.getSourceip() + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                ftpResultDetail.getDesip() + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getDesport();

        String reskey = "";
        if (Const.FTP_STOR_SIGN.equals(ftpResultDetail.getSign())) {
            reskey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_STOR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getFilename();
        }
        if (Const.FTP_RETR_SIGN.equals(ftpResultDetail.getSign())) {
            reskey = key + Const.AUDIT_SPLIT_JOIN + Const.FTP_RETR + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getOperationtime() + Const.AUDIT_SPLIT_JOIN + ftpResultDetail.getFilename();
        }
        String ftptime = TimeUtils.TimestampToDateStrNew(Long.parseLong(ftpResultDetail.getOperationtime()));
        ftpResultDetail.setOperationtime(ftptime);

        ftpEntityMap.put(reskey, ftpResultDetail);
    }

    /**
     * 处理ES中HTTP数据
     *
     * @param source
     */
    private static void dealESHttpJsonData(String source) {
        HTTPResultDetail httpResultDetail = new HTTPResultDetail();
        JSONObject httpDataJson = JSONObject.parseObject(source);
        //ES字段前缀
        String req = Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_POINT_JOIN + Const.REQ_SIGN.toLowerCase();
        String res = Const.HTTP_SIGN.toLowerCase() + Const.AUDIT_POINT_JOIN + Const.RES_SIGN.toLowerCase();

        String reqJson = httpDataJson.getString(req);
        String resJson = httpDataJson.getString(res);
        JSONObject reqDataJson = JSONObject.parseObject(reqJson);
        JSONObject resDataJson = JSONObject.parseObject(resJson);

        String seqnumber = reqDataJson.getString("seqnumber");
        String interfaceuri = reqDataJson.getString("interfaceuri");
        String sourceip = reqDataJson.getString("sourceip");
        String sourceport = reqDataJson.getString("sourceport");
        String desip = reqDataJson.getString("desip");
        String desport = reqDataJson.getString("desport");
        String reqmethod = reqDataJson.getString("reqmethod");
        String starttime = reqDataJson.getString("starttime");
        String reqcontent = reqDataJson.getString("reqcontent");

        String endtime = resDataJson.getString("endtime");
        String resstatuscode = resDataJson.getString("resstatuscode");
        String rescontent = resDataJson.getString("rescontent");

        httpResultDetail.setSeqnumber(seqnumber);
        httpResultDetail.setInterfaceuri(interfaceuri);
        httpResultDetail.setSourceip(sourceip);
        httpResultDetail.setSourceport(sourceport);
        httpResultDetail.setDesip(desip);
        httpResultDetail.setDesport(desport);
        httpResultDetail.setReqmethod(reqmethod);
        httpResultDetail.setStarttime(starttime);
        httpResultDetail.setReqcontent(reqcontent);
        httpResultDetail.setEndtime(endtime);
        httpResultDetail.setResstatuscode(resstatuscode);
        httpResultDetail.setRescontent(rescontent);

        // reqKey = 客户端IP-客户端端口-服务端IP-服务端端口-接口URI
        String reqKey = httpResultDetail.getSourceip() + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getSourceport() + Const.AUDIT_SPLIT_JOIN +
                httpResultDetail.getDesip() + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getDesport() + Const.AUDIT_SPLIT_JOIN +
                httpResultDetail.getInterfaceuri();

        String resultReqKey = reqKey + Const.AUDIT_SPLIT_JOIN + httpResultDetail.getStarttime();

        String reqtime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResultDetail.getStarttime()));
        httpResultDetail.setStarttime(reqtime);
        String restime = TimeUtils.TimestampToDateStrNew(Long.parseLong(httpResultDetail.getEndtime()));
        httpResultDetail.setEndtime(restime);

        //统计接口调用次数
        interfaceInfoCount(reqKey);

        //保存http结果
        resultMap.put(resultReqKey, httpResultDetail);
    }

}
