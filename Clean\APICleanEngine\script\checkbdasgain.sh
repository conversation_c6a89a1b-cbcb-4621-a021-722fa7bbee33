#!/bin/bash
#2023-03-02
#备注：-eq //equals等于; -ne //no equals不等于
#接口审计系统采集清洗运行状况检查脚本
#如果脚本执行报错,使用vim首先查看文件格式并修改: set ff=unix

#1.检查 CleanEngine 服务是否正常
# 需改动地方: CleanEngine-2.4.1.jar , 以实际jar名调整
bdasCleanEngineName=CleanEngine-1.2.1.jar
bdasCleanEngineProNewCount=`ps -ef |grep "$bdasCleanEngineName" |grep -v "grep" |wc -l`
if [ 1 -eq  $bdasCleanEngineProNewCount ]
then
  echo "BDAS CleanEngine Is Running."
else
  echo "BDAS CleanEngine Is Not Running."
fi

#2.检查 CollectTool 服务是否正常
# 需改动地方: CollectTool-0.0.1-SNAPSHOT.jar , 以实际jar名调整
bdasCollectToolName=CollectTool-0.0.1-SNAPSHOT.jar
bdasCollectToolProNewCount=`ps -ef |grep "$bdasCollectToolName" |grep -v "grep" |wc -l`
if [ 1 -eq  $bdasCollectToolProNewCount ]
then
  echo "BDAS CollectTool Is Running."
else
  echo "BDAS CollectTool Is Not Running."
fi