package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2023-09-08
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Apidiscovery implements Serializable {

    private Integer id;

    /** 接口编码 */
    private String apicode;

    /** 接口名称 */
    private String apiname;

    /** IP */
    private String apiip;

    /** 端口 */
    private String apiport;

    /** URL */
    private String url;

    /** 请求示例 */
    private String req_example;

    /** 响应示例 */
    private String res_example;

    /** 类别 */
    private String category;

    /** 级别 */
    private String risk;

    /** 标签 */
    private String labels;

    /** 访问域 */
    private String accessdomain;

    /** 状态 */
    private String apistatus;

    /** 插入时间 */
    private String inserttime;

    /** 更新人 */
    private String updateuser;

    /** 更新时间 */
    private String updatetime;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 请求数据标签 */
    private String reqdatatag;

    /** 响应数据标签 */
    private String resdatatag;

    /** 返回数据格式(json、html、xml、jsonp) */
    private String dataformat;

}
