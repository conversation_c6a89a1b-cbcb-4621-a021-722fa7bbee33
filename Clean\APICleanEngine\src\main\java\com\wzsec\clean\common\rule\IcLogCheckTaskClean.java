package com.wzsec.clean.common.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.wzsec.clean.modules.service.IcAlarmDisposalService;
import com.wzsec.clean.modules.service.IcInterfaceInfoService;
import com.wzsec.clean.modules.service.PcapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Component
@EnableScheduling
@Slf4j
public class IcLogCheckTaskClean {

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private PcapService pcapService;

    @Autowired
    private IcInterfaceInfoService icInterfaceInfoService;

    @Autowired
    private IcAlarmDisposalService icAlarmDisposalService;

    @Resource
    private EsUtils esUtils;

    private static IcLogCheckTaskClean icLogCheckTaskClean;


    @PostConstruct
    public void init() {
        icLogCheckTaskClean = this;
        icLogCheckTaskClean.client = this.client;
        icLogCheckTaskClean.esUtils = this.esUtils;
        icLogCheckTaskClean.jdbcTemplate = this.jdbcTemplate;
        icLogCheckTaskClean.pcapService = this.pcapService;
        icLogCheckTaskClean.icInterfaceInfoService = this.icInterfaceInfoService;
        icLogCheckTaskClean.icAlarmDisposalService = this.icAlarmDisposalService;
    }


    /**
     * es逐行清洗,处理清洗过后的单行数据【索引为:netflow-yyyy.MM.dd】
     *
     * @param strLine es对象转json
     * @throws Exception 异常
     */
    public static void cleanWriteByHaiNan(String strLine) {

        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();

        try {
            JSONObject jobj = null;

            try {
                //获取字符串的值
                jobj = JSONObject.parseObject(strLine);
            } catch (Exception e) {
                log.info("Json转换出现问题" + e);
            }

            String apicode = jobj.getString("apicode");
            String desip = jobj.getString("desip"); //客户端IP
            String interfaceuri = jobj.getString("interfaceuri"); //接口URL
            String sourceip = jobj.getString("sourceip"); //服务端IP
            String reqcontent = jobj.getString("reqcontent");  //请求参数
            String rescontent = jobj.getString("rescontent");   //响应参数
            Integer starttime = Integer.parseInt(jobj.getString("starttime"));  //请求时间
            String ak = jobj.getString("ak");
            String seqnum = jobj.getString("seqnum");

            //获取状态码
            String resstatus = jobj.getString("statuscode");
            if (resstatus.startsWith(Const.API_STATUSCODE_5PREFIX)) {
                apiCallNetFlow.setResstatus(Const.SERVER_ERROR_STATUS);
            } else if (resstatus.startsWith(Const.API_STATUSCODE_4PREFIX)) {
                apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);
            } else if (resstatus.startsWith(Const.API_STATUSCODE_1PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_2PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_3PREFIX)) {
                apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS); //响应状态   服务端
                apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS); //请求状态   客户端
            } else {
                apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS); //响应状态   服务端
                apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS); //请求状态   客户端
            }


            if (StringUtils.isNotBlank(interfaceuri) && StringUtils.isNotBlank(sourceip) &&
                    StringUtils.isNotBlank(rescontent) && StringUtils.isNotBlank(apicode)) {

                //排除无请求接口及请求结果为文件类型的url
                apiCallNetFlow.setApiuri(interfaceuri); //接口路径
                apiCallNetFlow.setReqmethod(""); //请求方法
                apiCallNetFlow.setClientip(desip); //客户端IP
                apiCallNetFlow.setApiip(sourceip);  //接口IP
                apiCallNetFlow.setApiport(""); //接口服务端口
                apiCallNetFlow.setApicode(apicode); //接口编码
                apiCallNetFlow.setAk(ak);
                apiCallNetFlow.setSeqnum(seqnum);

                //从请求字符串中获取接口请求参数值
                Params params = new Params();
                params.setParams(reqcontent);
                apiCallNetFlow.setReqcontent(params); //请求内容

                DataRescontent dataRescontent = new DataRescontent();
                dataRescontent.setData(rescontent);
                apiCallNetFlow.setRescontent(dataRescontent); //响应内容

                apiCallNetFlow.setCalltime(TimeUtils.dateForString(TimeUtils.stampForDate(starttime))); //请求时间

                String systemFullName = ConfigurationManager.getProperty("netflow.systemFullName").trim();
                apiCallNetFlow.setSystem(systemFullName); //系统   // TODO
                apiCallNetFlow.setModule(" "); //模块      // TODO
                apiCallNetFlow.setClientmac(" "); //客户端MAC  // TODO
                apiCallNetFlow.setAccount(" "); //账号       // TODO
                apiCallNetFlow.setDatatype("netflow"); //数据类型  == 固定死的  // TODO
                apiCallNetFlow.setCleantime(TimeUtils.getReqTime()); //清洗时间 当前时间

                //log.info(".........开始往es中添加数据...............");
                String data = JSON.toJSONString(apiCallNetFlow);
                //推送ES索引日期设置为前一天
                IndexRequest request = new IndexRequest("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1)).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
                IndexResponse response = icLogCheckTaskClean.client.index(request, RequestOptions.DEFAULT);
                //Console.log("....成功添加ID.....: {}", response.getId());

            }
        } catch (Exception ex) {
            //ex.printStackTrace();
            log.info("ES清洗出现异常,异常信息为: {}", ex.getMessage());
        }
    }

//    static String dataProvider = ConfigurationManager.getProperty("dataProvider").trim();
//    static String dataUse = ConfigurationManager.getProperty("dataUse").trim();

    /**
     * es逐行清洗,处理清洗过后的单行数据(TODO 未使用)
     *
     * @param strLine      es对象转json
     * @param downFileName 下载文件名
     * @throws Exception 异常
     */
//     public static ApiCallNetFlow getCleanedSingleLine(String strLine, List<ApiCallNetFlow> esDataList, String waterMarkInfo, List<String> apiList) {
//
//         ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
//         try {
//
//             String sceneStatus = ConfigurationManager.getProperty("scene.status").trim();
//
//
//             JSONObject jobj = null;
//
//             try {
//                 //获取字符串的值
//                 jobj = JSONObject.parseObject(strLine);
//
//             } catch (Exception e) {
//                 log.info("Json转换出现问题" + e);
//             }
//             //获取响应数据
//             JSONObject resObject = jobj.getJSONObject("res");
//             if (resObject != null) {
//                 String resstatus = resObject.getString("resstatuscode");  //服务端错误
//                 if (resstatus.startsWith(Const.API_STATUSCODE_5PREFIX)) {
//                     apiCallNetFlow.setResstatus(Const.SERVER_ERROR_STATUS);
//                     apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS);
//                 } else if (resstatus.startsWith(Const.API_STATUSCODE_4PREFIX)) {
//                     apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);
//                     apiCallNetFlow.setRepstatus(Const.SERVER_CORRECT_STATUS);
//                 } else if (resstatus.startsWith(Const.API_STATUSCODE_1PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_2PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_3PREFIX)) {
//                     apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS); //响应状态   服务端
//                     apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS); //请求状态   客户端
//                 }
//                 //从响应字符串中获取接口响应参数值
//                 String rescontent = IcLogCheckTaskClean.getResponseParaValue(resObject.getString("rescontent"));
//                 DataRescontent dataRescontent = new DataRescontent();
//                 //金华场景乱码问题响应内容返回乱码
//                 if (Const.scene_zero.equals(sceneStatus)) {
//                     if (rescontent.contains("code")) {
//                         dataRescontent.setData(rescontent + waterMarkInfo);
//                     }
//                 } else {
//                     dataRescontent.setData(rescontent + waterMarkInfo);
//                 }
//                 apiCallNetFlow.setRescontent(dataRescontent); //响应内容
//
//             }
//             //获取请求数据
//             JSONObject reqObject = jobj.getJSONObject("req");
//             if (reqObject != null) {
//                 //从请求字符串中获取reqmethod
// //                String reqmethod = IcLogCheckTaskClean.getReqmethodParaValue(reqObject.getString("interfaceuri"));
//                 String interfaceuri = reqObject.getString("interfaceuri").replace("//", "/");
//                 if (interfaceuri.contains("http:")) {
//                     interfaceuri = interfaceuri.split("http:")[1];
//                 }
//
//                 //排除无请求接口及请求结果为文件类型的url
// //                if (!interfaceuri.equals("/") && !interfaceuri.contains(".") && StringUtils.isNotEmpty(interfaceuri)) {
//                 //金华场景url上会携带 /gateway/api/001008007012001/dataSharing/uaVafn1351Jdn18b.htm (接口编码 = uaVafn1351Jdn18b)
//                 if (!interfaceuri.equals("/") && StringUtils.isNotEmpty(interfaceuri)) {
//                     apiCallNetFlow.setApiuri(interfaceuri); //接口路径
// //                    apiCallNetFlow.setReqmethod(reqmethod); //请求方法
//                     apiCallNetFlow.setReqmethod(""); //请求方法
//                     apiCallNetFlow.setClientip(reqObject.getString("sourceip")); //客户端IP
//                     apiCallNetFlow.setApiip(reqObject.getString("desip"));  //接口IP
//                     apiCallNetFlow.setApiport(reqObject.getString("desport")); //接口服务端口
//
// //                    apiCallNetFlow.setApicode(MD5Util.encrypt(interfaceuri)); //接口编码
//
//                     if (Const.scene_two.equals(sceneStatus)) {
//                         //通过uri生成接口编码插入更新接口信息表
//                         if (interfaceuri.contains("?") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")
//                                 && !interfaceuri.contains(".php") && !interfaceuri.contains(".db")
//                                 && !interfaceuri.contains(".sql") && !interfaceuri.contains(".key")
//                                 && !interfaceuri.contains(".com") && !interfaceuri.contains(".tar.z")
//                                 && !interfaceuri.contains(".gz") && !interfaceuri.contains(".cgj")
//                                 && !interfaceuri.contains(".tar.Z") && !interfaceuri.contains("http")) {
//                             IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//                             String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
//                             //通过生成好的MD5值(接口标识)去接口信息表匹配  sdd_ic_interfaceinfo
// //                            int interfaceIdentification = icLogCheckTaskClean.pcapService.queryNumberInterfaces(MD5Util.encrypt(uri));
//                             String apicode = MD5Util.encrypt(uri);
//                             if (!apiList.contains(apicode)) {
//                                 apiList.add(apicode);
//                                 icInterfaceInfo.setApicode(apicode);//接口编码
//                                 icInterfaceInfo.setSparefield4(uri);//URL
//                                 icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
//                                 icInterfaceInfo.setSparefield2(reqObject.getString("reqmethod"));//请求类似 get post
//                                 icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//                                 icInterfaceInfo.setUrl(interfaceuri);//接口地址
//                                 icLogCheckTaskClean.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
//                             }
//                         } else if
//                         (!interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")
//                                         && !interfaceuri.contains(".php") && !interfaceuri.contains(".db")
//                                         && !interfaceuri.contains(".sql") && !interfaceuri.contains(".key")
//                                         && !interfaceuri.contains(".com") && !interfaceuri.contains(".tar.z")
//                                         && !interfaceuri.contains(".gz") && !interfaceuri.contains(".cgj")
//                                         && !interfaceuri.contains(".tar.Z") && !interfaceuri.contains("http")) {
//                             IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//                             //通过生成好的MD5值(接口标识)去接口信息表匹配  sdd_ic_interfaceinfo
// //                            int interfaceIdentification = icLogCheckTaskClean.pcapService.queryNumberInterfaces(MD5Util.encrypt(interfaceuri));
//                             String apicode = MD5Util.encrypt(interfaceuri);
//                             if (!apiList.contains(apicode)) {
//                                 apiList.add(apicode);
//                                 icInterfaceInfo.setApicode(apicode);//接口编码
//                                 icInterfaceInfo.setSparefield4(interfaceuri);//URL
//                                 icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
//                                 icInterfaceInfo.setSparefield2(reqObject.getString("reqmethod"));//请求类似 get post
//                                 icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//                                 icInterfaceInfo.setUrl(interfaceuri);//接口地址
//                                 icLogCheckTaskClean.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
//                             }
//                         }
//                     }
//
//                     //金华场景apicode直接通过pcap捕获(uaVafn1351Jdn18b)
//                     //  /gateway/api/001008007012001/dataSharing/uaVafn1351Jdn18b.htm
//                     String apicode = "";
//                     if (interfaceuri.contains(".htm") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")) {
//                         String apicodeList = interfaceuri.substring(interfaceuri.lastIndexOf("/") + 1);
//                         apicode = apicodeList.substring(0, apicodeList.indexOf("."));
//                         apiCallNetFlow.setApicode(apicode); //接口编码
//                     } else if (interfaceuri.contains("?") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")) {
//                         //永康场景接口编码,根据生成的uri加密后匹配其他信息
//                         ///oauth/token?client_id=ad8e5e787cab462e88d01c90b75b1dfc&client_secret=5f843afd23214895b851036c954b95c5
//                         //0:统建 1:永康
//                         if (Const.scene_one.equals(sceneStatus)) {
//                             String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
//                             /**
//                              * 接口里面是 <接口路径>
//                              * 捕获流量是 <完整路径= 基础路径+接口路径>
//                              */
//                             String yongKangUri = uri.replaceFirst(Const.FLOW_CLEANING_FOUNDATIONPATH, "").trim();
//                             //通过生成好的MD5值(接口标识)去接口信息表匹配  sdd_ic_interfaceinfo
//                             apicode = icLogCheckTaskClean.pcapService.queryInterfaceCoding(MD5Util.encrypt(yongKangUri));
//                             if (StringUtils.isNotEmpty(apicode)) {
//                                 apiCallNetFlow.setApicode(apicode); //接口编码
//                             } else {
//                                 apiCallNetFlow.setApicode(MD5Util.encrypt(yongKangUri)); //接口编码
//                             }
//                         } else if (Const.scene_zero.equals(sceneStatus)) {
//                             String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
//                             apiCallNetFlow.setApicode(MD5Util.encrypt(uri)); //接口编码
//                         } else {
//                             String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
//                             apiCallNetFlow.setApicode(MD5Util.encrypt(uri)); //接口编码
//                         }
//                     } else if (!interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")) {
//                         apiCallNetFlow.setApicode(MD5Util.encrypt(interfaceuri)); //接口编码
//                     }
//
//                     //从请求字符串中获取接口请求参数值
//                     String reqcontent = IcLogCheckTaskClean.getRequestParaValue(reqObject.getString("reqcontent") == null ? "" : reqObject.getString("reqcontent"));
//                     Params params = new Params();
//                     params.setParams(reqcontent);
//                     apiCallNetFlow.setReqcontent(params); //请求内容
//                     Integer starttime = reqObject.getInteger("starttime");
//                     Date date = TimeUtils.stampForDate(starttime);
//                     String callTime = TimeUtils.dateForString(date);
//                     apiCallNetFlow.setCalltime(callTime); //请求时间
//
//                     String systemFullName = ConfigurationManager.getProperty("netflow.systemFullName").trim();
//                     apiCallNetFlow.setSystem(systemFullName); //系统   // TODO
//                     apiCallNetFlow.setModule(" "); //模块      // TODO
//                     apiCallNetFlow.setClientmac(reqObject.getString("sourceport")); //客户端MAC  TODO 请求端口
// //                    apiCallNetFlow.setAccount(" "); //账号       // TODO
//                     apiCallNetFlow.setAccount(getParam(interfaceuri, Const.FLOW_CLEANING_ACCOUNT)); //账号
//                     apiCallNetFlow.setDatatype("netflow"); //数据类型  == 固定死的  // TODO
//                     apiCallNetFlow.setCleantime(TimeUtils.getReqTime()); //清洗时间 当前时间
//                     esDataList.add(apiCallNetFlow);
//                 }
//             }
//         } catch (Exception ex) {
//             log.error("ES清洗出现异常,异常信息为: {}", ex.getMessage());
//             ex.printStackTrace();
//         }
//
//         return apiCallNetFlow;
//     }

    /**
     * es逐行清洗,处理清洗过后的单行数据(TODO 未使用)
     *
     * @param strLine      es对象转json
     * @param downFileName 下载文件名
     * @throws Exception 异常
     */
//     public static ApiCallNetFlow getYongkangLine(String strLine, List<ApiCallNetFlow> esDataList, String waterMarkInfo, List<String> apiList) {
//
//         ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
//         try {
//
//             String sceneStatus = ConfigurationManager.getProperty("scene.status").trim();
//
//
//             JSONObject jobj = null;
//
//             try {
//                 //获取字符串的值
//                 jobj = JSONObject.parseObject(strLine);
//
//             } catch (Exception e) {
//                 log.info("Json转换出现问题" + e);
//             }
//             //获取响应数据
//             JSONObject resObject = jobj.getJSONObject("res");
//             if (resObject != null) {
//                 String resstatus = resObject.getString("resstatuscode");  //服务端错误
//                 if (resstatus.startsWith(Const.API_STATUSCODE_5PREFIX)) {
//                     apiCallNetFlow.setResstatus(Const.SERVER_ERROR_STATUS);
//                     apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS);
//                 } else if (resstatus.startsWith(Const.API_STATUSCODE_4PREFIX)) {
//                     apiCallNetFlow.setRepstatus(Const.CLIENT_ERROR_STATUS);
//                     apiCallNetFlow.setRepstatus(Const.SERVER_CORRECT_STATUS);
//                 } else if (resstatus.startsWith(Const.API_STATUSCODE_1PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_2PREFIX) || resstatus.startsWith(Const.API_STATUSCODE_3PREFIX)) {
//                     apiCallNetFlow.setResstatus(Const.SERVER_CORRECT_STATUS); //响应状态   服务端
//                     apiCallNetFlow.setRepstatus(Const.CLIENT_CORRECT_STATUS); //请求状态   客户端
//                 }
//                 //从响应字符串中获取接口响应参数值
//                 String rescontent = IcLogCheckTaskClean.getResponseParaValue(resObject.getString("rescontent"));
//                 DataRescontent dataRescontent = new DataRescontent();
//                 //金华场景乱码问题响应内容返回乱码
//                 if (Const.scene_zero.equals(sceneStatus)) {
//                     if (rescontent.contains("code")) {
//                         dataRescontent.setData(rescontent + waterMarkInfo);
//                     }
//                 } else {
//                     dataRescontent.setData(rescontent + waterMarkInfo);
//                 }
//                 apiCallNetFlow.setRescontent(dataRescontent); //响应内容
//
//             }
//             //获取请求数据
//             JSONObject reqObject = jobj.getJSONObject("req");
//             if (reqObject != null) {
//                 //从请求字符串中获取reqmethod
// //                String reqmethod = IcLogCheckTaskClean.getReqmethodParaValue(reqObject.getString("interfaceuri"));
//                 String interfaceuri = reqObject.getString("interfaceuri").replace("//", "/");
//                 if (interfaceuri.contains("http:")) {
//                     interfaceuri = interfaceuri.split("http:")[1];
//                 }
//
//                 //排除无请求接口及请求结果为文件类型的url
// //                if (!interfaceuri.equals("/") && !interfaceuri.contains(".") && StringUtils.isNotEmpty(interfaceuri)) {
//                 //金华场景url上会携带 /gateway/api/001008007012001/dataSharing/uaVafn1351Jdn18b.htm (接口编码 = uaVafn1351Jdn18b)
//                 if (!interfaceuri.equals("/") && StringUtils.isNotEmpty(interfaceuri)) {
//                     apiCallNetFlow.setApiuri(interfaceuri); //接口路径
// //                    apiCallNetFlow.setReqmethod(reqmethod); //请求方法
//                     apiCallNetFlow.setReqmethod(""); //请求方法
//                     apiCallNetFlow.setClientip(reqObject.getString("sourceip")); //客户端IP
//                     apiCallNetFlow.setApiip(reqObject.getString("desip"));  //接口IP
//                     apiCallNetFlow.setApiport(reqObject.getString("desport")); //接口服务端口
//
// //                    apiCallNetFlow.setApicode(MD5Util.encrypt(interfaceuri)); //接口编码
//
//                     if (Const.scene_two.equals(sceneStatus)) {
//                         //通过uri生成接口编码插入更新接口信息表
//                         if (interfaceuri.contains("?") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")
//                                 && !interfaceuri.contains(".php") && !interfaceuri.contains(".db")
//                                 && !interfaceuri.contains(".sql") && !interfaceuri.contains(".key")
//                                 && !interfaceuri.contains(".com") && !interfaceuri.contains(".tar.z")
//                                 && !interfaceuri.contains(".gz") && !interfaceuri.contains(".cgj")
//                                 && !interfaceuri.contains(".tar.Z") && !interfaceuri.contains("http")) {
//                             IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//                             String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
//                             //通过生成好的MD5值(接口标识)去接口信息表匹配  sdd_ic_interfaceinfo
// //                            int interfaceIdentification = icLogCheckTaskClean.pcapService.queryNumberInterfaces(MD5Util.encrypt(uri));
//                             String apicode = MD5Util.encrypt(uri);
//                             if (!apiList.contains(apicode)) {
//                                 apiList.add(apicode);
//                                 icInterfaceInfo.setApicode(apicode);//接口编码
//                                 icInterfaceInfo.setSparefield4(uri);//URL
//                                 icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
//                                 icInterfaceInfo.setSparefield2(reqObject.getString("reqmethod"));//请求类似 get post
//                                 icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//                                 icInterfaceInfo.setUrl(interfaceuri);//接口地址
//                                 icLogCheckTaskClean.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
//                             }
//                         } else if
//                         (!interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")
//                                         && !interfaceuri.contains(".php") && !interfaceuri.contains(".db")
//                                         && !interfaceuri.contains(".sql") && !interfaceuri.contains(".key")
//                                         && !interfaceuri.contains(".com") && !interfaceuri.contains(".tar.z")
//                                         && !interfaceuri.contains(".gz") && !interfaceuri.contains(".cgj")
//                                         && !interfaceuri.contains(".tar.Z") && !interfaceuri.contains("http")) {
//                             IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();
//                             //通过生成好的MD5值(接口标识)去接口信息表匹配  sdd_ic_interfaceinfo
// //                            int interfaceIdentification = icLogCheckTaskClean.pcapService.queryNumberInterfaces(MD5Util.encrypt(interfaceuri));
//                             String apicode = MD5Util.encrypt(interfaceuri);
//                             if (!apiList.contains(apicode)) {
//                                 apiList.add(apicode);
//                                 icInterfaceInfo.setApicode(apicode);//接口编码
//                                 icInterfaceInfo.setSparefield4(interfaceuri);//URL
//                                 icInterfaceInfo.setInterface_status(Const.INTERFACE_INFORMATION_STATUS_RUN);//状态
//                                 icInterfaceInfo.setSparefield2(reqObject.getString("reqmethod"));//请求类似 get post
//                                 icInterfaceInfo.setData_format(Const.AUTHMETHOD_JSON);//返回数据格式
//                                 icInterfaceInfo.setUrl(interfaceuri);//接口地址
//                                 icLogCheckTaskClean.pcapService.saveIcInterfaceinfo(icInterfaceInfo);
//                             }
//                         }
//                     }
//
//                     //金华场景apicode直接通过pcap捕获(uaVafn1351Jdn18b)
//                     //  /gateway/api/001008007012001/dataSharing/uaVafn1351Jdn18b.htm
//                     String apicode = "";
//                     if (interfaceuri.contains(".htm") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")) {
//                         String apicodeList = interfaceuri.substring(interfaceuri.lastIndexOf("/") + 1);
//                         apicode = apicodeList.substring(0, apicodeList.indexOf("."));
//                         apiCallNetFlow.setApicode(apicode); //接口编码
//                     } else if (interfaceuri.contains("?") && !interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")) {
//                         //永康场景接口编码,根据生成的uri加密后匹配其他信息
//                         ///oauth/token?client_id=ad8e5e787cab462e88d01c90b75b1dfc&client_secret=5f843afd23214895b851036c954b95c5
//                         String uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
//                         /**
//                          * 接口里面是 <接口路径>
//                          * 捕获流量是 <完整路径= 基础路径+接口路径>
//                          */
//                         String yongKangUri = uri.replaceFirst(Const.FLOW_CLEANING_FOUNDATIONPATH, "").trim();
//                         //通过生成好的MD5值(接口标识)去接口信息表匹配  sdd_ic_interfaceinfo
//                         apicode = icLogCheckTaskClean.pcapService.queryInterfaceCoding(MD5Util.encrypt(yongKangUri));
//                         if (StringUtils.isNotEmpty(apicode)) {
//                             apiCallNetFlow.setApicode(apicode); //接口编码
//                         } else {
//                             apiCallNetFlow.setApicode(MD5Util.encrypt(yongKangUri)); //接口编码
//                         }
//                     } else if (!interfaceuri.contains(".css") && !interfaceuri.contains(".js") && !interfaceuri.contains(".woff")) {
//                         apiCallNetFlow.setApicode(MD5Util.encrypt(interfaceuri)); //接口编码
//                     }
//
//                     //从请求字符串中获取接口请求参数值
//                     String reqcontent = IcLogCheckTaskClean.getRequestParaValue(reqObject.getString("reqcontent") == null ? "" : reqObject.getString("reqcontent"));
//                     Params params = new Params();
//                     params.setParams(reqcontent);
//                     apiCallNetFlow.setReqcontent(params); //请求内容
//                     Integer starttime = reqObject.getInteger("starttime");
//                     Date date = TimeUtils.stampForDate(starttime);
//                     String callTime = TimeUtils.dateForString(date);
//                     apiCallNetFlow.setCalltime(callTime); //请求时间
//
//                     String systemFullName = ConfigurationManager.getProperty("netflow.systemFullName").trim();
//                     apiCallNetFlow.setSystem(systemFullName); //系统   // TODO
//                     apiCallNetFlow.setModule(" "); //模块      // TODO
//                     apiCallNetFlow.setClientmac(reqObject.getString("sourceport")); //客户端MAC  TODO 请求端口
// //                    apiCallNetFlow.setAccount(" "); //账号       // TODO
//                     apiCallNetFlow.setAccount(getParam(interfaceuri, Const.FLOW_CLEANING_ACCOUNT)); //账号
//                     apiCallNetFlow.setDatatype("netflow"); //数据类型  == 固定死的  // TODO
//                     apiCallNetFlow.setCleantime(TimeUtils.getReqTime()); //清洗时间 当前时间
//                     esDataList.add(apiCallNetFlow);
//                 }
//             }
//         } catch (Exception ex) {
//             log.error("ES清洗出现异常,异常信息为: {}", ex.getMessage());
//             ex.printStackTrace();
//         }
//
//         return apiCallNetFlow;
//     }



    public static void pushData(String waterMarkInfo, List<ApiCallNetFlow> addEsDataMapList) {
        List<IndexRequest> indexRequests = new ArrayList<>();
        addEsDataMapList.forEach(data -> {
            IndexRequest request = new IndexRequest();
            request.id(data.getId() + "");
            request.index("netflow-" + DateUtils.getBeforeOneDay(new Date(), 1));
            request.source(JSON.toJSONString(data), XContentType.JSON);
            request.type("_doc");
            indexRequests.add(request);
        });
        indexRequests.forEach(icLogCheckTaskClean.esUtils.createBulkProcessor()::add);
    }



    /**
     * 从请求字符串中截取鉴权信息示例
     *
     * @param authenticate
     * @return
     */
    public static String getValue(String authenticate, String url) {
        String type = "";

        String cookie = Const.AUTHMETHOD_COOKIE.replace(":", "");
        String authorization = Const.AUTHMETHOD_AUTHORIZATION.replace(":", "");
        String sign = Const.AUTHMETHOD_SIGN.replace(":", "");
        String token = Const.AUTHMETHOD_TOKEN.replace(":", "");

        try {
            if (url.contains(cookie)) {
                type = getUrlValue(url, cookie);
            } else if (url.contains(authorization)) {
                type = getUrlValue(url, authorization);
            } else if (url.contains(sign)) {
                type = getUrlValue(url, sign);
            } else if (url.contains(token)) {
                type = getUrlValue(url, token);
                //cookie
            } else if (authenticate.indexOf(Const.AUTHMETHOD_COOKIE) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_COOKIE);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
                //Authorization
            } else if (authenticate.indexOf(Const.AUTHMETHOD_AUTHORIZATION) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_AUTHORIZATION);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
                //sign
            } else if (authenticate.indexOf(Const.AUTHMETHOD_SIGN) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_SIGN);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
                //token
            } else if (authenticate.indexOf(Const.AUTHMETHOD_TOKEN) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_TOKEN);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
            }
        } catch (Exception e) {

        }
        return type;
    }


    /**
     * 判断鉴权方式
     *
     * @param authenticate 请求体
     * @param url          url
     * @return
     */
    public static String whetherAuthenticate(String authenticate, String url) {
        String type = "";
        String cookie = Const.AUTHMETHOD_COOKIE.replace(":", "");
        String authorization = Const.AUTHMETHOD_AUTHORIZATION.replace(":", "");
        String sign = Const.AUTHMETHOD_SIGN.replace(":", "");
        String token = Const.AUTHMETHOD_TOKEN.replace(":", "");
        if (url.contains(cookie)) {
            type = cookie;
        } else if (url.contains(authorization)) {
            type = authorization;
        } else if (url.contains(sign)) {
            type = sign;
        } else if (url.contains(token)) {
            type = token;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_COOKIE) > 0) {
            type = cookie;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_AUTHORIZATION) > 0) {
            type = authorization;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_SIGN) > 0) {
            type = sign;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_TOKEN) > 0) {
            type = token;
        }
        return type;
    }


    /**
     * 从请求字符串中判断请求格式
     *
     * @param format
     * @return
     */
    public static String getFormat(String format) {
        String[] strArr = format.split("/");
        String reqmethod = "";
        try {
            reqmethod = strArr[strArr.length - 2];
            if (format.indexOf(Const.AUTHMETHOD_XML) > 0) {
                reqmethod = Const.AUTHMETHOD_XML;
            } else if (format.indexOf(Const.AUTHMETHOD_JSON) > 0) {
                reqmethod = Const.AUTHMETHOD_JSON;
            }
        } catch (Exception e) {
        }
        return reqmethod;
    }


    /**
     * 从请求字符串中获取接口请求参数值
     *
     * @param strData
     * @return
     */
    public static String getRequestParaValue(String strData) {
        String data = "";
        if (StringUtils.isNotBlank(strData) || strData.contains("\r\n")) {
            if (strData.contains("\r\n")) {
                String[] strArr = strData.split("\r\n");
                data = strArr[strArr.length - 1];
            } else {
                data = strData;
            }
        }
        return data;
    }

    /**
     * 从请求字符串中获取reqmethod
     *
     * @param strData
     * @return
     */
    public static String getReqmethodParaValue(String strData) {
        String[] strArr = strData.split("/");
        String reqmethod = "";
        try {
            reqmethod = strArr[strArr.length - 2];
        } catch (Exception e) {
            //Console.log("获取reqmethod :{} ,出现异常,异常信息为:{}", strData, e.getMessage());
        }
        return reqmethod;
    }


    /**
     * 从响应字符串中获取接口响应参数值
     *
     * @param strData
     * @return
     */
    public static String getResponseParaValue(String strData) {
        String[] strArr = new String[0];
        String s = "";
        try {
            strArr = strData.split("\r\n");
            s = strArr[strArr.length - 2];
        } catch (Exception e) {
            try {
                return strArr[strArr.length - 1];
            } catch (Exception ex) {
                s = "";
            }
        }
        return s;
    }


    /**
     * 逐行清洗入库
     *
     * @param strLine
     */
    public static void getCleanedSingleLineByHaiNan(String strLine) {

        try {
            //推送ES索引日期设置为前一天
            IndexRequest request = new IndexRequest("csbflow-" + DateUtils.getBeforeOneDay(new Date(), 1)).source(strLine, XContentType.JSON);
            IndexResponse response = icLogCheckTaskClean.client.index(request, RequestOptions.DEFAULT);
            //Console.log("....成功添加ID.....: {}", response.getId());
        } catch (Exception ex) {
            //ex.printStackTrace();
            log.info("ES清洗出现异常,异常信息为: {}", ex.getMessage());
        }
    }


    public static void addDoc2(ApiCallNetFlow apiCallNetFlow, RestHighLevelClient client) throws IOException {
        String data = JSON.toJSONString(apiCallNetFlow);
        IndexRequest request = new IndexRequest("netflow-" + DateUtils.getDateToday()).id(apiCallNetFlow.getId()).source(data, XContentType.JSON);
        IndexResponse response = icLogCheckTaskClean.client.index(request, RequestOptions.DEFAULT);
        System.out.println(response.getId());
    }


    /**
     * 获取URL中参数信息
     *
     * @param url  url
     * @param name 参数
     * @return
     */
    public static String getParam(String url, String name) {
        Map<String, String> split = null;
        if (url.contains(name)) {
            String params = url.substring(url.indexOf("?") + 1);
            split = Splitter.on("&").withKeyValueSeparator("=").split(params);
            return split.get(name);
        } else {
            return "";
        }
    }

    /* 获取URL中的参数值
     * @param url
     * @return
     */
    public static String getUrlValue(String url, String val) {
        String regEx = "(\\?|&+)(.+?)=([^&]*)";//匹配参数名和参数值的正则表达式
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(url);
        Map<String, String> paramMap = new LinkedHashMap<String, String>();
        while (m.find()) {
            String paramName = m.group(2);//获取参数名
            String paramVal = m.group(3);//获取参数值
            paramMap.put(paramName, paramVal);
        }
        return paramMap.get(val);
    }

//    public static void main(String[] args) {
//        System.out.println(Math.max(1, 2));
//        System.out.println(Math.min(1, 2));
//    }

}
