package com.wzsec.clean.modules.model;

/**
 * <AUTHOR>
 * @ClassName: SMSensitiveData
 * @Description: TODO
 * @date 2020年04月22日
 */
public class SMSensitiveData {

    private int id;    //id
    private String sensitivedata;  //敏感数据
    private String sensitivedataename;  //敏感数据英文名
    private String sensitivelevel;   //敏感级别
    private String sensitivedatarule;  //敏感数据规则
    private String testscenario;   //试用检测场景
    private String remark;  //备注
    private String createuser;   //创建人
    private String createtime;   //创建时间
    private String updateuser;   //更新人
    private String updatetime;   //更新时间
    private String sparefield1;
    private String sparefield2;
    private String sparefield3;
    private String sparefield4;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSensitivedata() {
        return sensitivedata;
    }

    public void setSensitivedata(String sensitivedata) {
        this.sensitivedata = sensitivedata;
    }

    public String getSensitivedataename() {
        return sensitivedataename;
    }

    public void setSensitivedataename(String sensitivedataename) {
        this.sensitivedataename = sensitivedataename;
    }

    public String getSensitivelevel() {
        return sensitivelevel;
    }

    public void setSensitivelevel(String sensitivelevel) {
        this.sensitivelevel = sensitivelevel;
    }

    public String getSensitivedatarule() {
        return sensitivedatarule;
    }

    public void setSensitivedatarule(String sensitivedatarule) {
        this.sensitivedatarule = sensitivedatarule;
    }

    public String getTestscenario() {
        return testscenario;
    }

    public void setTestscenario(String testscenario) {
        this.testscenario = testscenario;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateuser() {
        return createuser;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getUpdateuser() {
        return updateuser;
    }

    public void setUpdateuser(String updateuser) {
        this.updateuser = updateuser;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getSparefield1() {
        return sparefield1;
    }

    public void setSparefield1(String sparefield1) {
        this.sparefield1 = sparefield1;
    }

    public String getSparefield2() {
        return sparefield2;
    }

    public void setSparefield2(String sparefield2) {
        this.sparefield2 = sparefield2;
    }

    public String getSparefield3() {
        return sparefield3;
    }

    public void setSparefield3(String sparefield3) {
        this.sparefield3 = sparefield3;
    }

    public String getSparefield4() {
        return sparefield4;
    }

    public void setSparefield4(String sparefield4) {
        this.sparefield4 = sparefield4;
    }

}
