package com.wzsec.clean.common.utils;


import com.alibaba.fastjson.JSONObject;
import com.wzsec.clean.modules.model.IcAlarmDisposal;
import org.graylog2.syslog4j.Syslog;
import org.graylog2.syslog4j.SyslogIF;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Date;
import java.util.UUID;

import static com.wzsec.clean.common.utils.DateUtils.formatDate;
import static com.wzsec.clean.common.utils.DateUtils.getSecondTimestamp;


@Component
public class MonitorRiskAlarmData {

    String HOST = ConfigurationManager.getProperty("syslog.host").trim();
    String protocol = ConfigurationManager.getProperty("syslog.protocol").trim();
    int PORT = ConfigurationManager.getInteger("syslog.port");

    /**
     * 将告警信息推送到syslog
     *
     * @param icAlarmdisposal
     */
    public void sendIcExample(IcAlarmDisposal icAlarmdisposal) {
        JSONObject joInfo = new JSONObject(true);
        String ID = UUID.randomUUID().toString().replace("-", "").toLowerCase();
        //告警推送syslog字段
        joInfo.put("EVENT_ID", ID);//事件ID（需保证 ID 唯一性），使用 32 位 UUID
        joInfo.put("EVENT_TIME", getSecondTimestamp(formatDate(icAlarmdisposal.getChecktime())));//事件产生时间，使用秒级时间戳，例如 **********
        joInfo.put("EVENT_TYPE", Const.EVENT_TYPE);//事件类型   默认:共享安全
        joInfo.put("EVENT_CITY", Const.EVENT_CITY);//事件市级名称  默认:金华市
        joInfo.put("EVENT_REGION", icAlarmdisposal.getArea() == null ? " " : icAlarmdisposal.getArea());//区县
        joInfo.put("SRC_IP", icAlarmdisposal.getSourceip() == null ? " " : icAlarmdisposal.getSourceip());//源IP
        joInfo.put("SRC_PORT", icAlarmdisposal.getSourceport() == null ? " " : icAlarmdisposal.getSourceport());//源端口
        joInfo.put("DST_IP", icAlarmdisposal.getDestinationip() == null ? " " : icAlarmdisposal.getDestinationip());//目标IP
        joInfo.put("DST_PORT", icAlarmdisposal.getDestinationport() == null ? " " : icAlarmdisposal.getDestinationport());//目标端口
        joInfo.put("USERNAME", icAlarmdisposal.getAccount() == null ? " " : icAlarmdisposal.getAccount());//事件相关用户名 TODO 永康有appKey 金华没有
        joInfo.put("EVENT_LEVEL", icAlarmdisposal.getRisk() == null ? " " : icAlarmdisposal.getRisk());//事件等级，3-高，2-中，1-低,0-无分级
        joInfo.put("DEVICE_TYPE", Const.DEVICE_TYPE);//产生事件的设备类型  默认:API
        joInfo.put("EVENT_NAME", icAlarmdisposal.getEventrule() == null ? " " : icAlarmdisposal.getEventrule());//事件名字
        joInfo.put("EVENT_DETAIL", icAlarmdisposal.getCircumstantiality() == null ? " " : icAlarmdisposal.getCircumstantiality());//事件详情

        joInfo.put("EVENT_DEAL_TIME", getSecondTimestamp(new Date()));//事件处置时间，使用秒级时间戳，例如 **********
        joInfo.put("EVENT_DEAL_ID", ID);//处置告警事件编号，需与 EVENT_ID（事件 ID）对应
        joInfo.put("EVENT_WORKFLOW", icAlarmdisposal.getTreatmentstate());//事件处置状态， 未处置：为空或者 0 处置中：1 已处置：2
        joInfo.put("EVENT_DEAL_OPER", " ");//事件处置人员
        joInfo.put("EVENT_DEAL_MARK", icAlarmdisposal.getNote() == null ? " " : icAlarmdisposal.getNote());//事件处置记录描述
        //发送syslog日志信息
        generate(joInfo.toJSONString());

    }

    /**
     * 客户端
     *
     * @param message
     */
    public void generate(String message) {
        SyslogIF syslog = Syslog.getInstance(protocol);
        syslog.getConfig().setHost(HOST);
        syslog.getConfig().setPort(PORT);
        try {
            syslog.log(0, URLDecoder.decode(message, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            System.out.println("generate log get exception " + e);
        }
    }
}
