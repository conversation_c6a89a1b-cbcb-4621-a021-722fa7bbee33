<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wzsec.clean.modules.dao.PcapTaskDao">

    <select id="getUseTaskInfo" resultType="com.wzsec.clean.modules.model.PcapTask">
        select * from if_task where status='0';
    </select>

    <select id="getTaskByType" resultType="com.wzsec.clean.modules.model.PcapTask">
        select * from if_task where type=#{httpType} or type=#{ftpType};
    </select>

    <select id="getTaskById" resultType="com.wzsec.clean.modules.model.PcapTask">
        select * from if_task where id=#{id};
    </select>
</mapper>
