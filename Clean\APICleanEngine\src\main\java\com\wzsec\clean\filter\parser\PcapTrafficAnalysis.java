package com.wzsec.clean.filter.parser;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Pcap流量解析通用方法抽取[其他非通用请查看单独分支]
 *
 * <AUTHOR>
 * @date 2025/05/20
 */
@Slf4j
public class PcapTrafficAnalysis {

    private static final long REQUEST_RESPONSE_PACKET_RESTRICTION = 1024 * 1024 * 2; //请求响应包写入ES大小限制

    /**
     * 获取接口URI
     *
     * @param interfaceuri 接口
     * @return {@link String}
     */
    public static String getInterfaceURI(String interfaceuri) {
        String uri = interfaceuri;
        //通过uri生成接口编码插入更新接口信息表
        if (interfaceuri.contains("?")) {
            uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
        }
        return uri;
    }

    /**
     * 提取url
     *
     * @param httpRequest http请求
     * @return {@link String}
     */
    public static String extractUrl(String httpRequest) {
        String[] lines = httpRequest.split("\r\n");
        if (lines.length != 0) {
            String requestLine = lines[0];
            String[] parts = requestLine.split(" ");
            if (parts.length > 1) {
                if (parts[1].startsWith("/")) {
                    return parts[1];
                }
            }
        }
        return null;
    }

    /**
     * 获取appKey
     *
     * @param url url
     * @return {@link String}
     */
    public static String extractAppKey(String url) {
        String appKey = "";
        try {
            Pattern pattern = Pattern.compile("\\bappKey=([^&]+)");
            Matcher matcher = pattern.matcher(url);

            if (matcher.find()) {
                appKey = matcher.group(1);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return appKey;
    }


    /**
     * 提取用户代理
     *
     * @param httpRequestHeaders HTTP请求标头
     * @return {@code String }
     */
    public static String extractUserAgent(String httpRequestHeaders) {
        String[] lines = httpRequestHeaders.split("\n");

        for (String line : lines) {
            if (line.startsWith("User-Agent:")) {
                return line.substring("User-Agent:".length()).trim();
            }
        }
        return "";
    }


    /**
     * 提取授权
     *
     * @param httpRequestHeaders HTTP请求标头
     * @return {@code String }
     */
    public static String extractAuthorization(String httpRequestHeaders) {
        String[] lines = httpRequestHeaders.split("\n");

        for (String line : lines) {
            if (line.startsWith("Authorization:")) {
                return line.substring("Authorization:".length()).trim();
            }
        }
        return "";
    }

    /**
     * 判断该接口是否属于上传下载操作
     *
     * @param requestData  请求体
     * @param responseData 响应体
     * @return
     */
    public static Boolean checkStatusApiFile(String requestData, String responseData) {
        boolean apiFileStatus = false;
        if (requestData.contains("Content-Type") && requestData.contains("boundary=----")) {
            apiFileStatus = true;
        } else if (responseData.contains("Content-Disposition: attachment;")) {
            apiFileStatus = true;
        }
        return apiFileStatus;
    }

    /**
     * 优化响应数据内容
     *
     * @param input
     * @return
     */
    public static String extractContent(String input) {
        // 如果没有找到JSON对象，尝试提取JSON数组
        int startIndexArr = input.indexOf('[');
        int startIndexObj = input.indexOf('{');

        if (startIndexArr < 0 || startIndexArr > startIndexObj) {
            // 尝试提取JSON对象
            int endIndexObj = input.lastIndexOf('}');
            if (startIndexObj != -1 && endIndexObj != -1 && endIndexObj > startIndexObj) {
                return input.substring(startIndexObj, endIndexObj + 1).trim();
            }
        } else {
            int endIndexArr = input.lastIndexOf(']');
            if (endIndexArr != -1 && endIndexArr > startIndexArr) {
                return input.substring(startIndexArr, endIndexArr + 1).trim();
            }
        }

        if (startIndexObj < 0) {
            int endIndexArr = input.lastIndexOf(']');
            if (startIndexArr != -1 && endIndexArr != -1 && endIndexArr > startIndexArr) {
                return input.substring(startIndexArr, endIndexArr + 1).trim();
            }
        }

        // 如果没有找到JSON对象或数组，返回空字符串
        return input;
    }

    /**
     * 从完整的HTTP请求字符串中提取请求体，并对其进行URL解码
     *
     * @param httpRequestString 完整的HTTP请求字符串
     * @return 解码后的请求体字符串
     */
    public static String decodeRequestBody(String httpRequestString) throws Exception {
        int bodyStartIndex = 0;
        String requestBody = httpRequestString;
        if (httpRequestString.contains("\r\n\r\n") || httpRequestString.contains("\n\n")) {
            if (httpRequestString.contains("\r\n\r\n")) {
                bodyStartIndex = httpRequestString.indexOf("\r\n\r\n");
                if (bodyStartIndex == -1) {
                    return "";
                }
                // 截取请求体部分
                requestBody = httpRequestString.substring(bodyStartIndex + 4);
            } else {
                bodyStartIndex = httpRequestString.indexOf("\n\n");
                if (bodyStartIndex == -1) {
                    return "";
                }
                requestBody = httpRequestString.substring(bodyStartIndex + 2);
            }
        }
        return decodeRequestBodyIfNeeded(requestBody);
    }

    /**
     * 处理请求头及响应头Header
     *
     * @param httpRequestString http请求字符串
     * @return {@link String }
     * @throws Exception 例外
     */
    public static Map<String, String> decodeHeader(String httpRequestString) throws Exception {
        int bodyStartIndex = httpRequestString.indexOf("\r\n\r\n");
        if (bodyStartIndex == -1) {
            bodyStartIndex = httpRequestString.indexOf("\n\n");
        }

        if (bodyStartIndex != -1) {
            String headers = httpRequestString.substring(0, bodyStartIndex);
            String[] lines = headers.split("\r\n|\n");

            Map<String, String> headerMap = new LinkedHashMap<>();
            for (int i = 1; i < lines.length; i++) {
                String[] keyValue = lines[i].split(": ", 2);
                if (keyValue.length == 2) {
                    headerMap.put(keyValue[0], keyValue[1]);
                }
            }
            return headerMap;
        }
        return new LinkedHashMap<>();
    }

    /**
     * 解码请求正文（如果需要） 例: param1=value%20&param2=value%21
     *
     * @param requestBody 请求主体
     * @return {@link String}
     */
    public static String decodeRequestBodyIfNeeded(String requestBody) {
        // 判断请求体中是否包含百分号编码
        if (containsPercentEncoding(requestBody)) {
            // 需要解码
            try {
                return URLDecoder.decode(requestBody, StandardCharsets.UTF_8.toString());
            } catch (UnsupportedEncodingException e) {
                return requestBody;
            }
        } else {
            // 不需要解码
            return requestBody;
        }
    }

    /**
     * 使用HTML实体转义字符串中的字符
     *
     * @param requestBody 请求主体
     * @return boolean
     */
    private static boolean containsPercentEncoding(String requestBody) {
        return requestBody != null && (requestBody.contains("%") || !StringEscapeUtils.escapeHtml4(requestBody).equals(requestBody));
    }

    /**
     * 从完整的 HTTP 响应字符串中提取响应体
     * 如果响应体过大，则直接返回。
     *
     * @param httpResponseString 完整的 HTTP 响应字符串
     * @return 提取的响应体字符串，或者如果数据量过大则直接返回空字符串
     */
    public static String extractResponseBody(String httpResponseString) {
        int bodyStartIndex = httpResponseString.indexOf("\r\n\r\n");
        if (bodyStartIndex == -1) {
            return "";
        }
        // 判断响应体大小，如果大于某个阈值，则直接返回(50MB 为最大限制)
        if (httpResponseString.length() > REQUEST_RESPONSE_PACKET_RESTRICTION) {
            return "";
        } else {
            // TODO 处理响应体
            return httpResponseString.substring(bodyStartIndex + 4);
        }
    }


    /**
     * 采集终端
     *
     * @param requestData 请求数据
     * @return {@code String }
     */
    public static String acquisitionTerminal(String requestData) {
        if (requestData.contains("User-Agent")) {
            String userAgent = PcapTrafficAnalysis.extractUserAgent(requestData);
            String regex = "([A-Za-z]+)/(\\d+(\\.\\d+)*)";

            // 编译正则表达式
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(userAgent);
            while (matcher.find()) {
                // 请求终端
                return matcher.group(1);
            }
        }
        return "";
    }

    /**
     * 获取URL中参数信息
     *
     * @param url  url
     * @param name 参数
     * @return
     */
    public static String getParam(String url, String name) {
        Map<String, String> split = null;
        if (url.contains(name)) {
            String params = url.substring(url.indexOf("?") + 1);
            split = Splitter.on("&").withKeyValueSeparator("=").split(params);
            return split.get(name);
        } else {
            return "";
        }
    }


}
