package com.wzsec.clean.kafka.service;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.kafka.utils.ConsumerHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.concurrent.ListenableFuture;

@EnableScheduling
@SpringBootApplication
public class KafkaApplication {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Autowired
    private ConsumerHandler consumers;

    private static String KafkaMessageConsumeStatus = ConfigurationManager.getProperty("KafkaMessageConsume.status").trim();

    //消费kafka写入es定时任务
    @Scheduled(cron = "${KafkaMessageConsume.cron}")
    public void KafkaMessageConsume() {
        if (Const.KAKFA_CLEANING_ON.equals(KafkaMessageConsumeStatus)) {
            System.err.println("开始从kafka消费数据");
            consumers.execute(10000);
        }
    }

    //TODO 真实日志 大量造测试数据
//    @Scheduled(cron = "20 41 17 * * ? ")
    public void test01() {
        System.err.println("定时生产");
        for (int i = 0; i < 100000; i++) {
            try {
                String message = "{\n" +
                        "    \"apiCode\":\"b2TTLM\",\n" +
                        "    \"apiName\":\"能耗数据接入\",\n" +
                        "    \"busiType\":\"in\",\n" +
                        "    \"callTime\":129,\n" +
                        "    \"componentHost\":\"**************\",\n" +
                        "    \"componentKey\":\"opnc\",\n" +
                        "    \"componentPort\":\"9400\",\n" +
                        "    \"consumerAppId\":\"be5853f9-28ab-48dd-8641-38debf18fb7e\",\n" +
                        "    \"consumerAppIpListType\":\"white\",\n" +
                        "    \"consumerAppName\":\"山东-能源管理系统\",\n" +
                        "    \"consumerAppSecret\":\"********************************\",\n" +
                        "    \"consumerId\":\"2280\",\n" +
                        "    \"externalTime\":124,\n" +
                        "    \"fromIp\":\"**************\",\n" +
                        "    \"httpMethod\":\"POST\",\n" +
                        "    \"logId\":\"4992b689814d4c6bb605284f6889e5ff\",\n" +
                        "    \"logTime\":1685674780046,\n" +
                        "    \"messageList\":[\n" +
                        "        {\n" +
                        "            \"body\":\"{\\\"working_condition\\\":\\\"28.0\\\",\\\"read_time\\\":\\\"2023-06-02 09:00:00\\\",\\\"read_value\\\":\\\"162234.39\\\",\\\"meter_code\\\":\\\"140035370000000002921863\\\",\\\"create_user\\\":\\\"sdadmin\\\"}\",\n" +
                        "            \"header\":\"{\\\"X-Forwarded-For\\\":[\\\"**************\\\"],\\\"Host\\\":[\\\"***********\\\"],\\\"X-Real-IP\\\":[\\\"**************\\\"],\\\"Content-Length\\\":[\\\"151\\\"],\\\"token\\\":[\\\"eyJhbGciOiJIUzI1NiJ9.eyJwcm92aW5jZUlkIjoiMzciLCJleHAiOjE2ODU3NTg2NDgsInN1YiI6InN1YmplY3QiLCJmdW5jTmFtZSI6InByb2dyYW1zIiwiZnVuY0VsZW1lbnRzIjoicHJvZ3JhbTpjaGVjayIsImF1ZCI6ImRhdGEiLCJpc3MiOiJlbXMiLCJqdGkiOiIxIiwiZnVuY1VybCI6Im9kcHMvZW1zX3Byb2Nlc3MiLCJpYXQiOjE2NzAyMDY2NDh9.wLaMN7oS1_EnZXkaCVT2ruvWfN_HxUbPuPfLbxqFf9g\\\"],\\\"Content-Type\\\":[\\\"application/json; charset=utf-8\\\"],\\\"authentication\\\":[\\\"eyJBUFBfSUQiOiJiZTU4NTNmOS0yOGFiLTQ4ZGQtODY0MS0zOGRlYmYxOGZiN2UiLCJUSU1FU1RBTVAiOiIyMDIzLTA2LTAyIDEwOjU1OjUwIDM2NCIsIlRPS0VOIjoiZTQ3N2I1NzA2MTE1YzhhMTg1YWU2MWNhZjFhNDk4Y2EiLCJUUkFOU19JRCI6IjIzMDYwMjEwNTU1MDM2NDk0OTEifQ==\\\"],\\\"User-Agent\\\":[\\\"Apache-HttpClient/4.5.5 (Java/1.8.0_321)\\\"],\\\"Accept-Encoding\\\":[\\\"gzip,deflate\\\"]}\",\n" +
                        "            \"serialNumber\":\"1\",\n" +
                        "            \"sizeX\":\"151\",\n" +
                        "            \"time\":1685674780046,\n" +
                        "            \"type\":\"REC\",\n" +
                        "            \"url\":\"http://***********/api/middlleplatform/b2TTLM/vItQS7/v1.0\"\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"body\":\"{\\n\\t\\\"UNI_NET_BODY\\\":\\\"{\\\\\\\"status\\\\\\\":\\\\\\\"true\\\\\\\",\\\\\\\"code\\\\\\\":\\\\\\\"success\\\\\\\",\\\\\\\"message\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"data\\\\\\\":{\\\\\\\"collect_id\\\\\\\":\\\\\\\"4140412\\\\\\\",\\\\\\\"meter_id\\\\\\\":\\\\\\\"2009833\\\\\\\",\\\\\\\"meter_code\\\\\\\":\\\\\\\"140035370000000002921863\\\\\\\",\\\\\\\"result_state\\\\\\\":\\\\\\\"Y\\\\\\\",\\\\\\\"error_message\\\\\\\":\\\\\\\"\\\\\\\"}}\\\",\\n\\t\\\"UNI_NET_HEAD\\\":{\\n\\t\\t\\\"RESP_CODE\\\":\\\"0000\\\",\\n\\t\\t\\\"RESP_DESC\\\":\\\"成功\\\",\\n\\t\\t\\\"TIMESTAMP\\\":\\\"2023-06-02 10:59:40 044\\\"\\n\\t}\\n}\",\n" +
                        "            \"serialNumber\":\"4\",\n" +
                        "            \"sizeX\":\"0\",\n" +
                        "            \"time\":1685674780046,\n" +
                        "            \"type\":\"RET\",\n" +
                        "            \"url\":\"http://***********/api/middlleplatform/b2TTLM/vItQS7/v1.0\"\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"body\":\"{\\\"working_condition\\\":\\\"28.0\\\",\\\"read_time\\\":\\\"2023-06-02 09:00:00\\\",\\\"read_value\\\":\\\"162234.39\\\",\\\"meter_code\\\":\\\"140035370000000002921863\\\",\\\"create_user\\\":\\\"sdadmin\\\"}\",\n" +
                        "            \"header\":\"{\\\"ncopp_app_name\\\":\\\"5bGx5LicLeiDvea6kOeuoeeQhuezu+e7nw==\\\",\\\"capability_id\\\":\\\"2108262222428590752\\\",\\\"capability_key\\\":\\\"vItQS7\\\",\\\"ncopp_app_id\\\":\\\"be5853f9-28ab-48dd-8641-38debf18fb7e\\\",\\\"app_id\\\":\\\"be5853f9-28ab-48dd-8641-38debf18fb7e\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzI1NiJ9.eyJwcm92aW5jZUlkIjoiMzciLCJleHAiOjE2ODU3NTg2NDgsInN1YiI6InN1YmplY3QiLCJmdW5jTmFtZSI6InByb2dyYW1zIiwiZnVuY0VsZW1lbnRzIjoicHJvZ3JhbTpjaGVjayIsImF1ZCI6ImRhdGEiLCJpc3MiOiJlbXMiLCJqdGkiOiIxIiwiZnVuY1VybCI6Im9kcHMvZW1zX3Byb2Nlc3MiLCJpYXQiOjE2NzAyMDY2NDh9.wLaMN7oS1_EnZXkaCVT2ruvWfN_HxUbPuPfLbxqFf9g\\\"}\",\n" +
                        "            \"serialNumber\":\"2\",\n" +
                        "            \"sizeX\":\"151\",\n" +
                        "            \"time\":1685674780044,\n" +
                        "            \"type\":\"REQ\",\n" +
                        "            \"url\":\"http://10.244.4.159:7007/odps/service/data/process\"\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"body\":\"\\\"{\\\\\\\"status\\\\\\\":\\\\\\\"true\\\\\\\",\\\\\\\"code\\\\\\\":\\\\\\\"success\\\\\\\",\\\\\\\"message\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"data\\\\\\\":{\\\\\\\"collect_id\\\\\\\":\\\\\\\"4140412\\\\\\\",\\\\\\\"meter_id\\\\\\\":\\\\\\\"2009833\\\\\\\",\\\\\\\"meter_code\\\\\\\":\\\\\\\"140035370000000002921863\\\\\\\",\\\\\\\"result_state\\\\\\\":\\\\\\\"Y\\\\\\\",\\\\\\\"error_message\\\\\\\":\\\\\\\"\\\\\\\"}}\\\",\\n\\t\\\"UNI_NET_HEAD\\\":{\\n\\t\\t\\\"RESP_CODE\\\":\\\"0000\\\",\\n\\t\\t\\\"RESP_DESC\\\":\\\"成功\\\",\\n\\t\\t\\\"TIMESTAMP\\\":\\\"2023-06-02 10:59:40 044\\\"\\n\\t}\\n\",\n" +
                        "            \"header\":\"{\\\"date\\\":[\\\"Fri, 02 Jun 2023 02:59:51 GMT\\\"],\\\"server\\\":[\\\"nginx/1.22.0\\\"],\\\"set-cookie\\\":[\\\"JSESSIONID=EFD93E650FD40FE559EF0327353DEE35; Path=/odps; HttpOnly\\\"],\\\"content-length\\\":[\\\"178\\\"],\\\"connection\\\":[\\\"keep-alive\\\"]}\",\n" +
                        "            \"serialNumber\":\"3\",\n" +
                        "            \"sizeX\":\"178\",\n" +
                        "            \"time\":1685674780044,\n" +
                        "            \"type\":\"RESP\",\n" +
                        "            \"url\":\"http://10.244.4.159:7007/odps/service/data/process\"\n" +
                        "        }\n" +
                        "    ],\n" +
                        "    \"platformTime\":5,\n" +
                        "    \"receiveSize\":151,\n" +
                        "    \"receiveTime\":1685674779917,\n" +
                        "    \"requestSize\":151,\n" +
                        "    \"responseSize\":178,\n" +
                        "    \"returnSize\":0,\n" +
                        "    \"servCode\":\"vItQS7\",\n" +
                        "    \"statusCode\":\"0000\",\n" +
                        "    \"statusDesc\":\"成功\",\n" +
                        "    \"timestamp\":\"2023-06-02 10:55:50 364\",\n" +
                        "    \"token\":\"e477b5706115c8a185ae61caf1a498ca\",\n" +
                        "    \"transId\":\"2306021055503649491\",\n" +
                        "    \"version\":\"v1.0\"\n" +
                        "}";
                ListenableFuture<SendResult<String, String>> topic_test = kafkaTemplate.send("test-kafka", message);
                System.err.println(message);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
