package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-09-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountLoginRecord implements Serializable {

    private Integer id;

    /**
     * 账号
     */
    private String account;

    /**
     * 请求IP
     */
    private String clientip;

    /**
     * 请求端口
     */
    private String clientport;

    /**
     * 服务IP
     */
    private String serverip;

    /**
     * 服务端口
     */
    private String serverport;

    /**
     * token
     */
    private String token;

    /**
     * 请求终端
     */
    private String requestterminal;

    /**
     * 请求时间
     */
    private String requesttime;

    /**
     * 创建时间
     */
    private String creationtime;

    /**
     * 备用字段1
     */
    private String sparefield1;

    /**
     * 备用字段2
     */
    private String sparefield2;

    /**
     * 备用字段3
     */
    private String sparefield3;

    /**
     * 备用字段4
     */
    private String sparefield4;


}
