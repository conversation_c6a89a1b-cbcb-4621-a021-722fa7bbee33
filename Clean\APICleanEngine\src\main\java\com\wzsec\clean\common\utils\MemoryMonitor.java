package com.wzsec.clean.common.utils;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

public class MemoryMonitor {

//    private static final long MEMORY_THRESHOLD = 1024 * 1024 * 1024; // 设置内存阈值，例如50MB

    private static final String cleanRestartCommand = ConfigurationManager.getProperty("netflow.cleanRestartCommand");
    private static final long memoryThreshold = ConfigurationManager.getLong("netflow.memoryThreshold");

    private static String restartSwitchStatus = ConfigurationManager.getProperty("netflow.restartSwitch").trim();

    public static void main(String[] args) {
        // 启动监控线程
        new Thread(MemoryMonitor::monitorMemory).start();

        // 应用程序逻辑
        System.out.println("运行其他逻辑");
    }

    public static void monitorMemory() {
        if (restartSwitchStatus.equals(Const.AUTHMETHOD_ON)) {
            MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
            while (true) {
                MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
                long usedMemory = heapMemoryUsage.getUsed();
//            System.out.println("正在监控java内存情况");
                if (usedMemory > memoryThreshold) {
                    System.out.println("Memory threshold exceeded! Restarting application...");
                    try {
                        //启动java程序
                        System.out.println("执行命令:" + cleanRestartCommand);
                        Runtime.getRuntime().exec(cleanRestartCommand);
                        // 退出当前Java程序的进程
                        Runtime.getRuntime().exit(0);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                }
                try {
                    // 休眠一段时间再次检查
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}