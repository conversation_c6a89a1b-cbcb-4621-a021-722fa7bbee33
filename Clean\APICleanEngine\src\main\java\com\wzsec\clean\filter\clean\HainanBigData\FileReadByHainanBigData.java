package com.wzsec.clean.filter.clean.HainanBigData;

import cn.hutool.core.lang.Console;
import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.DateUtils;
import com.wzsec.clean.common.utils.FileUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 海南大数据读取文件
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public class FileReadByHainanBigData {

    private static final String pcapTmpPath = ConfigurationManager.getProperty("PcapTmpPath").trim();  //[海南大数据] 存放清洗前压缩文件路径

    /**
     * 海南文件按小时筛选
     *
     * @param cleanAdvancementHours 清洁推进时间
     * @param pattern               格式化
     * @return {@link String }
     */
    public static String hainanFileFilterByHour(Long cleanAdvancementHours, String pattern) {
        // 获取当前时间的上一个小时
        LocalDateTime lastHour = LocalDateTime.now().minusHours(cleanAdvancementHours);
        // 定义目标时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化时间
        return lastHour.format(formatter);
    }

    /**
     * 海南环境解压文件到待审文件夹(按天解压)
     *
     * @param parserBeforeDir 解压前路径
     */
    public static void hainanEnvironmentalDecompressionToDay(String parserBeforeDir) {
        HashMap<String, String> map = new HashMap<>();
        FileUtils.getAllFileName(pcapTmpPath, map);
        for (String key : map.keySet()) {
            if (map.get(key).contains(DateUtils.findBeforeOneDay(new Date(), 1, "yyyyMMdd"))) {
                Console.log("开始解压文件：" + key);
                boolean unZipResult = FileUtils.unTarGz(new File(key), parserBeforeDir + "/" + new Date().getTime());
                if (unZipResult) {
                    Console.log("[解压] {} 文件完毕", key);
                }
            }
        }
    }

    /**
     * 海南环境解压文件到待审文件夹(按小时解压)
     *
     * @param parserBeforeDir dir之前解析器
     */
    public static void hainanEnvironmentalDecompressionToHour(String parserBeforeDir) {
        Map<String, String> filePathNameMap = new HashMap<>();
        FileUtils.getAllFileName(pcapTmpPath, filePathNameMap);
        for (String filePath : filePathNameMap.keySet()) {
            // 根据 "_" 分割字符串
            String[] parts = filePathNameMap.get(filePath).split("_");
            // 提取日期和小时部分
            if (parts.length == 3) {
                // 截取文件时间 yyyyMMdd_RandomValue_HH.tar.gz
                String dateTime = parts[0] + "_" + parts[2];
                dateTime = dateTime.replace(".tar.gz", "").trim();

                // 获取截取时间 yyyyMMdd_HH
                String oneHourAgo = FileReadByHainanBigData.hainanFileFilterByHour(1L, "yyyyMMdd_HH");
                if (dateTime.equalsIgnoreCase(oneHourAgo)) {

                    if (!parserBeforeDir.endsWith(File.separator)) {
                        parserBeforeDir = parserBeforeDir + File.separator;
                    }

                    String outputDir = parserBeforeDir + new Date().getTime() + File.separator +
                            FileReadByHainanBigData.hainanFileFilterByHour(1L, "yyyy_MM_dd_HH");

                    boolean unZipResult = FileUtils.unTarGz(new File(filePath), outputDir);
                    if (unZipResult) {
                        Console.log("[解压] {} 文件完毕", filePath);
                    }
                }
            }
        }
    }


    /**
     * 清理非每日压缩文件
     */
    public static void cleanNonDailyCompressedFiles() {
        // TODO 根据解压路径下文件遍历,不属于当天的进行删除操作
        HashMap<String, String> map = new HashMap<>();
        FileUtils.getAllFileName(pcapTmpPath, map);
        for (String key : map.keySet()) {
            if (!map.get(key).contains(DateUtils.findBeforeOneDay(new Date(), 0, "yyyyMMdd"))) {
                boolean deleteFileResult = FileUtils.deleteFile(key);
                if (deleteFileResult) {
                    Console.log("[删除] {} 文件成功", key);
                }
            }
        }
    }


}
