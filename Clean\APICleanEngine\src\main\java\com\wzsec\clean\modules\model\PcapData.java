package com.wzsec.clean.modules.model;


public class PcapData {
    private String id;
    private int time_s;//时间戳（秒）
    private int time_ms;//时间戳（微秒）
    private String time;//调用时间
    private String checktime;//检测时间
    private int pLength;//抓包长度
    private int length;//实际长度
    private byte[] content_byte;//数据包
    private String content;//数据内容
    private String protocol;//协议类型 1字节
    private String source_address;//原地址 4字节
    private String des_address;//目的地址  4字节
    private String interface_uri;//接口名称
    private String sign;//行为标识
    private String tasknum;//任务号
    private String totalcount;//检测总数
    private String seq_number;// 序号  大小端原因，高低位4个8bit的存放顺序是反的，intel使用小端模式 4字节
    private String ack_number;//确认号，大小端原因，高低位4个8bit的存放顺序是反的，intel使用小端模式 4字节
    private String example;//样例
    private String resulttype;//结果类型
    private String risk;//风险程度
    private String sensitivedata;//敏感数据及数量
    private String contentcheckcount;//内容统计数量
    private String contenttotalcount;//内容检测总数量
    private String ratio;//比例
    private String userid;//用户id
    private String username;//用户名称
    private String sparefield1;
    private String sparefield2;
    private String sparefield3;
    private String sparefield4;


    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getContentcheckcount() {
        return contentcheckcount;
    }

    public void setContentcheckcount(String contentcheckcount) {
        this.contentcheckcount = contentcheckcount;
    }

    public String getContenttotalcount() {
        return contenttotalcount;
    }

    public void setContenttotalcount(String contenttotalcount) {
        this.contenttotalcount = contenttotalcount;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSensitivedata() {
        return sensitivedata;
    }

    public void setSensitivedata(String sensitivedata) {
        this.sensitivedata = sensitivedata;
    }

    public String getExample() {
        return example;
    }

    public void setExample(String example) {
        this.example = example;
    }

    public String getResulttype() {
        return resulttype;
    }

    public void setResulttype(String resulttype) {
        this.resulttype = resulttype;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public String getChecktime() {
        return checktime;
    }

    public void setChecktime(String checktime) {
        this.checktime = checktime;
    }

    public byte[] getContent_byte() {
        return content_byte;
    }

    public void setContent_byte(byte[] content_byte) {
        this.content_byte = content_byte;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSeq_number() {
        return seq_number;
    }

    public void setSeq_number(String seq_number) {
        this.seq_number = seq_number;
    }

    public String getAck_number() {
        return ack_number;
    }

    public void setAck_number(String ack_number) {
        this.ack_number = ack_number;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(String totalcount) {
        this.totalcount = totalcount;
    }

    public String getTasknum() {
        return tasknum;
    }

    public void setTasknum(String tasknum) {
        this.tasknum = tasknum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public int getTime_s() {
        return time_s;
    }

    public void setTime_s(int time_s) {
        this.time_s = time_s;
    }

    public int getTime_ms() {
        return time_ms;
    }

    public void setTime_ms(int time_ms) {
        this.time_ms = time_ms;
    }

    public int getpLength() {
        return pLength;
    }

    public void setpLength(int pLength) {
        this.pLength = pLength;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getSource_address() {
        return source_address;
    }

    public void setSource_address(String source_address) {
        this.source_address = source_address;
    }

    public String getDes_address() {
        return des_address;
    }

    public void setDes_address(String des_address) {
        this.des_address = des_address;
    }

    public String getInterface_uri() {
        return interface_uri;
    }

    public void setInterface_uri(String interface_uri) {
        this.interface_uri = interface_uri;
    }

    public String getSparefield1() {
        return sparefield1;
    }

    public void setSparefield1(String sparefield1) {
        this.sparefield1 = sparefield1;
    }

    public String getSparefield2() {
        return sparefield2;
    }

    public void setSparefield2(String sparefield2) {
        this.sparefield2 = sparefield2;
    }

    public String getSparefield3() {
        return sparefield3;
    }

    public void setSparefield3(String sparefield3) {
        this.sparefield3 = sparefield3;
    }

    public String getSparefield4() {
        return sparefield4;
    }

    public void setSparefield4(String sparefield4) {
        this.sparefield4 = sparefield4;
    }

    @Override
    public String toString() {
        return "PcapData{" +
                "id=" + id +
                ", time_s=" + time_s +
                ", time_ms=" + time_ms +
                ", time='" + time + '\'' +
                ", pLength=" + pLength +
                ", length=" + length +
                ", content=" + content +
                ", protocol='" + protocol + '\'' +
                ", source_address='" + source_address + '\'' +
                ", des_address='" + des_address + '\'' +
                ", interfaceInfo='" + interface_uri + '\'' +
                '}';
    }
}
