package com.wzsec.clean.common.utils;

import com.alibaba.fastjson.JSON;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ParamUtil {


    private static String fileSeparator = System.getProperty("file.separator");

    public static String getFilePathName(String filePath, String fileName) {
        StringBuffer sb = new StringBuffer(filePath);
        sb.append(fileSeparator).append(getFormatDate("yyyy-MM-dd", new Date())).append(fileSeparator);
        if (fileName.contains(".")) {
            String[] fileNameArr = fileName.split("\\.");
            sb.append(fileNameArr[0]).append("_").append(getFormatDate("yyyy-MM-dd-HH", new Date())).append(".").append(fileNameArr[1]);
        } else {
            sb.append(fileName).append("_").append(getFormatDate("yyyy-MM-dd-HH", new Date()));
        }
        return sb.toString();
    }

    /*
     * @return
     * @description 获取当天时间
     * <AUTHOR>
     */
    private static String getFormatDate(String formatStr, Date date) {
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        return format.format(date);
    }


    /**
     * @param filePath
     * @param printData
     * @description 追加到文件末尾
     * <AUTHOR>
     */
    public static void appendFile(String filePath, String printData) {
        File file = new File(filePath);
        FileOutputStream fos = null;
        OutputStreamWriter osw = null;
        try {
            if (!file.exists()) {
                while (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                file.createNewFile();
                fos = new FileOutputStream(file);
            } else {
                fos = new FileOutputStream(file, true);
            }
            osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
            osw.write(printData); //写入内容
            osw.write("\r\n");  //换行
        } catch (Exception e) {
            e.printStackTrace();
        } finally {   //关闭流
            try {
                if (osw != null)
                    osw.close();
                if (fos != null)
                    fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @param map
     * @return
     * @description 对于需要打印的map处理，确保响应结束
     * <AUTHOR>
     */
    public static Map<String, Map<String, Map<String, String>>> getPrintMap(Map<String, Map<String, Map<String, String>>> map) {
        if (map == null || map.size() <= 0) {
            return null;
        }
        List<String> keyList = new ArrayList<>(map.keySet());
        Map<String, Map<String, Map<String, String>>> printMap = new HashMap<>();
        for (String key : keyList) {
            //对于响应时间已经过去10秒的记录，确保此条纪录响应完成
            if (System.currentTimeMillis() - Long.parseLong(map.get(key).get("response").get("responseTimestamp")) >= 10 * 1000) {
                printMap.put(key, map.get(key));//需要打印的
                map.remove(key);//从原有中删除
            }
        }
        return printMap;
    }

    public static void clearNoUseMap(Map<String, Map<String, Map<String, String>>> map) {
        if (map == null || map.size() <= 0) {
            return;
        }
        List<String> keyList = new ArrayList<String>(map.keySet());
        for (String key : keyList) {
            //对于请求时间已经过去5分钟的临时请求记录删除
            if (System.currentTimeMillis() - Long.parseLong(map.get(key).get("request").get("requestTimestamp")) >= 30 * 1000) {
                map.remove(key);//从原有中删除
            }
        }

    }

    /**
     * @param content
     * @return
     * @description 拆分检测响应内容
     * <AUTHOR>
     */
    public static boolean checkHttpContent(String content) {
        boolean result = false;
        String[] dataArr = content.split("[\\pP\\p{Punct}]");
        for (String data : dataArr) {
            if (p_checkMobileNumber(data)) {
                result = true;
                break;
            }
        }
        return result;
    }

    /**
     * @param parameter
     * @return
     * @description 检测手机号
     * <AUTHOR>
     */
    public static boolean p_checkMobileNumber(String parameter) {
        if (parameter.startsWith("1") && parameter.length() == 11) {
            return checkingRule(parameter,
                    "^1(3[0-9]|4[5-9]|5[0-3,5-9]|6[5,6]|7[0-8]|8[0-9]|9[1,8,9])\\d{8}$");// 符合手机有问题,不符合手机号正确
        }
        return false;
    }

    /**
     * @param parameter
     * @param regexps
     * @return
     * @description 根据正则匹配
     * <AUTHOR>
     */
    public static boolean checkingRule(String parameter, String regexps) {
        boolean paraValid = false;
        Pattern pattern = Pattern.compile(regexps);
        Matcher matcher = pattern.matcher(parameter.trim());
        //字符串是否与正则表达式相匹配
        if (matcher.matches()) {
            paraValid = true;
        }
        return paraValid;
    }

    private static String[] contentTypeTxTArr = {"text/plain", " text/xml", "application/xml", "application/json", "application/octet-stream"};

    /**
     * @param contentType
     * @return
     * @description 根据响应头类型判断响应内容是否是文本格式
     * <AUTHOR>
     */
    public static boolean checkContentTypeIsTxt(String contentType) {
        boolean result = false;
        if (contentType != null) {
            for (String contentTypeTemp : contentTypeTxTArr) {
                if (contentType.contains(contentTypeTemp)) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }


    /**
     * @return
     * @description 获取请求参数-总
     * <AUTHOR>
     */
    public static String getParamsStr(String method, String url, String data) {
        String paramsStr = null;
        if (method.equals("GET")) {
            paramsStr = getGetParamsStr(url);
        } else if (method.equals("POST")) {
            paramsStr = getPostParamsStr(data);
        }
        return getParamsJsonStr(paramsStr);
    }

    /**
     * @return
     * @description 获取请求参数-GET
     * <AUTHOR>
     */
    private static String getGetParamsStr(String url) {
        String paramsStr = null;
        int index = url.indexOf("?");
        if (index == -1) {
            return paramsStr;
        }
        paramsStr = url.substring(index + 1);
        return paramsStr;
    }

    /**
     * @return
     * @description 获取请求参数-POST
     * <AUTHOR>
     */
    private static String getPostParamsStr(String paramsStr) {
        try {
            paramsStr = URLDecoder.decode(paramsStr, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return paramsStr;
    }


    /**
     * @param paramsStr
     * @return
     * @description 将请求参数格式化成json格式
     * <AUTHOR>
     */
    private static String getParamsJsonStr(String paramsStr) {
        Map<String, String> params = new HashMap<>();
        if (paramsStr == null) {
            return JSON.toJSONString(params);
        }
        String[] paramsStrArr = paramsStr.split("&");
        for (String paramStr : paramsStrArr) {
            String[] paramArr = paramStr.split("=");
            if (paramArr.length == 1)
                params.put(paramArr[0], null);
            else if (paramArr.length == 2)
                params.put(paramArr[0], paramArr[1]);
        }
        return JSON.toJSONString(params);
    }


    /**
     * 提取和解码参数
     *
     * @param url url
     * @return {@link String }
     */
    public static String extractAndDecodeParams(String url) {
        int questionMarkIndex = url.indexOf('?');
        if (questionMarkIndex != -1 && questionMarkIndex < url.length() - 1) {
            String params = url.substring(questionMarkIndex + 1);
            try {
                StringBuilder decodedParams = new StringBuilder();
                String[] pairs = params.split("&");
                for (String pair : pairs) {
                    int idx = pair.indexOf("=");
                    if (idx != -1) {
                        String key = URLDecoder.decode(pair.substring(0, idx), "UTF-8");
                        String value = URLDecoder.decode(pair.substring(idx + 1), "UTF-8");
                        decodedParams.append(key).append("=").append(value).append("&");
                    } else {
                        decodedParams.append(URLDecoder.decode(pair, "UTF-8")).append("&");
                    }
                }
                if (decodedParams.length() > 0) {
                    decodedParams.setLength(decodedParams.length() - 1);
                }
                return decodedParams.toString();
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return "";
    }
}
