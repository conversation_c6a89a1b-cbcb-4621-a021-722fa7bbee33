#!/bin/bash
#备注：-eq //equals等于; -ne //no equals不等于
#接口审计系统启动脚本

#1.检查mysql服务是否正常,如果未启动则进行启动
mysqlprocount=`ps -ef|grep mysql|grep -v grep|wc -l`
if [ $mysqlprocount -ne 1 ]
then
  /usr/sbin/mysqld start
else
  echo "MySQL Is Running."
fi

#2.检查redis服务是否正常,如果未启动则进行启动,需修改redis指定路径
# 需修改路径: data/software/redis/redis/bin/redis-server 调整为实际路径
redisprocount=`ps -ef|grep redis|grep -v grep|wc -l`
if [ $redisprocount -ne 1 ]
then
  /data/software/redis/redis/bin/redis-server /data/software/redis/redis/bin/redis.conf
else
 echo "Redis Is Running."
fi

#3.检查Nginx服务是否正常,如果未启动则进行启动  -ne 不大于,需修改Nginx指定路径
# 需修改路径: /data/software/nginx/nginx20/sbin/nginx 调整为实际路径
nginxprocount=`ps -ef|grep nginx|grep -v grep|wc -l`
if [ $nginxprocount -ne 2 ]
then
  /data/software/nginx/nginx20/sbin/nginx
else
  echo "Nginx Is Running."
fi

#4.检查ds_bdas_system服务是否正常
# 需改动地方: ds_bdas_system-2.4.1.jar , 以实际jar名调整
bdasSystemName=ds_bdas_system-2.4.1.jar
# 需改动地方: 服务所在路径, /data/zhengh/ds_bdas/system/ , 已实际jar包所在路径调整
bdasSystemDir=/data/zhengh/ds_bdas/system/
bdasSystemProCount=`ps -ef |grep "$bdasSystemName" |grep -v "grep" |wc -l`
if [ $bdasSystemProCount -eq 0 ]
then
  nohup java -jar $bdasSystemDir/$bdasSystemName > nohup.out 2>&1 &
  sleep 20s
  bdasSystemProNewCount=`ps -ef |grep "$bdasSystemName" |grep -v "grep" |wc -l`
  if [ $bdasSystemProNewCount  -eq  1 ];then
        echo "接口审计系统${bdasSystemName}程序成功启动"
   fi
else
  echo "接口审计系统${bdasSystemName}进程存在，已在运行"
fi

#5.检查ds_bdas_engine服务是否正常
# 需改动地方: ds_bdas_engine-2.4.1.jar , 以实际jar名调整
bdasEngineName=ds_bdas_engine-2.4.1.jar
# 需改动地方: 服务所在路径, /data/zhengh/ds_bdas/engine/ , 已实际jar包所在路径调整
bdasEngineDir=/data/zhengh/ds_bdas/engine/
bdasEngineProCount=`ps -ef |grep "$bdasEngineName" |grep -v "grep" |wc -l`
if [ $bdasEngineProCount -eq 0 ]
then
  nohup java -jar $bdasEngineDir/$bdasEngineName > nohup.out 2>&1 &
  sleep 20s
  bdasEngineProNewCount=`ps -ef |grep "$bdasEngineName" |grep -v "grep" |wc -l`
  if [ $bdasEngineProNewCount  -eq  1 ];then
        echo "接口审计系统${bdasEngineName}程序成功启动"
   fi
else
  echo "接口审计系统${bdasEngineName}进程存在，已在运行"
fi
