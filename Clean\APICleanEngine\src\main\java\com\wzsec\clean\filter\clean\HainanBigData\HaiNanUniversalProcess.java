package com.wzsec.clean.filter.clean.HainanBigData;

import com.wzsec.clean.common.utils.ConfigurationManager;
import com.wzsec.clean.common.utils.Const;
import com.wzsec.clean.common.utils.MD5Util;
import com.wzsec.clean.common.utils.TimeUtils;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.DataRescontent;
import com.wzsec.clean.modules.model.Params;
import com.wzsec.clean.modules.model.PcapFlowCombination;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 海南[通用]对象处理
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Slf4j
public class HaiNanUniversalProcess {

    /**
     * 海南场景清洗
     *
     * @param pcapFlowCombination pcap流量组合图
     */
    public static ApiCallNetFlow hainanUniversalClean(PcapFlowCombination pcapFlowCombination) throws ParseException, UnsupportedEncodingException, NoSuchAlgorithmException {

        String requestUrl = pcapFlowCombination.getRequest_url();  //请求URL
        String clientIp = pcapFlowCombination.getClient_ip(); //客户端IP
        String clientPort = pcapFlowCombination.getClient_port();  // 客户端端口
        String serverIp = pcapFlowCombination.getServer_ip();  //服务端IP
        String serverPort = pcapFlowCombination.getServer_port();  // 服务端端口
        String requestTime = pcapFlowCombination.getRequest_time(); //请求时间
        String requestData = pcapFlowCombination.getRequest_data() == null ? "" : pcapFlowCombination.getRequest_data(); //请求内容
        String responseData = pcapFlowCombination.getResponse_data() == null ? "" : pcapFlowCombination.getResponse_data();  //响应内容

        // TODO 请求响应状态, 请求无值导致截取报错,该处置为空
        String resstatuscode = "";
        try {
            resstatuscode = responseData.split("\r\n")[0].split(" ")[1];
        } catch (Exception e) {
            resstatuscode = "";
        }

        // TODO 请求方法
        String reqmethod = "";
        try {
            reqmethod = requestData.split("\r\n")[0].split(" ")[0];
        } catch (Exception e) {
            reqmethod = "";
        }

        // TODO 请求体
        String reqcontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            try {
                reqcontent = decodeRequestBody(requestData);
            } catch (Exception e) {
                reqcontent = requestData;
            }
        }

        // TODO 响应体
        String rescontent = "";
        if (StringUtils.isNotBlank(requestData)) {
            rescontent = extractResponseBody(responseData);
            rescontent = extractContent(rescontent);
        }

        //  TODO 获取响应数据
        String resstatus = "";
        String repstatus = "";
        if (resstatuscode.startsWith(Const.API_STATUSCODE_5PREFIX)) {
            resstatus = Const.SERVER_ERROR_STATUS;
            repstatus = Const.CLIENT_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_4PREFIX)) {
            resstatus = Const.CLIENT_ERROR_STATUS;
            repstatus = Const.SERVER_CORRECT_STATUS;
        } else if (resstatuscode.startsWith(Const.API_STATUSCODE_1PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_2PREFIX)
                || resstatuscode.startsWith(Const.API_STATUSCODE_3PREFIX)) {
            resstatus = Const.SERVER_CORRECT_STATUS; //响应状态   服务端
            repstatus = Const.CLIENT_CORRECT_STATUS; //请求状态   客户端
        }

        //  TODO 2.获取请求数据
        // 请求URI
        String interfaceuri = requestUrl.replace("//", "/");
        if (interfaceuri.contains("http:")) {
            interfaceuri = interfaceuri.split("http:")[1];
        }

        // 获取url及接口编码
        String uri = getInterfaceURI(interfaceuri);
        pcapFlowCombination.setResponse_body_data(rescontent); // 响应体
        pcapFlowCombination.setRequest_body_data(reqcontent);  // 请求体
        pcapFlowCombination.setReq_method(reqmethod); //请求方法
        String system = ConfigurationManager.getProperty("netflow.systemFullName").trim();

        // TODO 根据实际情况取值
        String ak = MD5Util.encrypt(serverIp);  // TODO appid根据ip生成编码
        String apiCode = MD5Util.encrypt(uri);  // TODO apiCode根据uri生成编码

        ApiCallNetFlow apiCallNetFlow = new ApiCallNetFlow();
        apiCallNetFlow.setAk(ak);  //AK
        apiCallNetFlow.setApicode(apiCode); //接口编码
        apiCallNetFlow.setApiuri(uri); //请求URL
        apiCallNetFlow.setApiip(clientIp); //服务器IP
        apiCallNetFlow.setApiport(clientPort); //服务端端口
        apiCallNetFlow.setClientip(serverIp); //客户端IP
        apiCallNetFlow.setClientport(serverPort); //客户端端口
        apiCallNetFlow.setRepstatus(repstatus); //请求状态
        apiCallNetFlow.setResstatus(resstatus); //响应状态
        apiCallNetFlow.setReqmethod(reqmethod);  //请求方法(GET、POST)
        apiCallNetFlow.setSystem(system); //系统名称

        Params params = new Params();
        params.setParams(reqcontent);
        apiCallNetFlow.setReqcontent(params); //请求体数据

        DataRescontent dataRescontent = new DataRescontent();
        dataRescontent.setData(rescontent);
        apiCallNetFlow.setRescontent(dataRescontent); //响应体数据

        apiCallNetFlow.setDatatype("netflow");
        apiCallNetFlow.setCalltime(requestTime);
        apiCallNetFlow.setCleantime(TimeUtils.getReqTime());

        // TODO 要运行 EQL 搜索，搜索到的数据流或索引必须包含时间戳和事件类别字段 @timestamp 表示时间戳  event.category 表示事件分类
        ApiCallNetFlow.EventDTO eventDTO = new ApiCallNetFlow.EventDTO();
        eventDTO.setCategory("netflow");
        apiCallNetFlow.setEvent(eventDTO);

        // 新增字段 接口响应字符数
        apiCallNetFlow.setRessize((long) rescontent.length());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = formatter.parse(requestTime);
        int hour = date.getHours();
        apiCallNetFlow.setReqtimetag(String.valueOf(hour));

        apiCallNetFlow.setAkapicode(ak + "," + apiCode + "," + system);

        return apiCallNetFlow;

    }

    /**
     * 获取接口URI
     *
     * @param interfaceuri 接口
     * @return {@link String}
     */
    private static String getInterfaceURI(String interfaceuri) {
        String uri = interfaceuri;
        //通过uri生成接口编码插入更新接口信息表
        if (interfaceuri.contains("?")) {
            uri = interfaceuri.substring(0, interfaceuri.indexOf("?"));
        }
        return uri;
    }


    /**
     * 从完整的HTTP请求字符串中提取请求体，并对其进行URL解码
     *
     * @param httpRequestString 完整的HTTP请求字符串
     * @return 解码后的请求体字符串
     */
    public static String decodeRequestBody(String httpRequestString) throws Exception {
        int bodyStartIndex = 0;
        String requestBody = httpRequestString;
        if (httpRequestString.contains("\r\n\r\n") || httpRequestString.contains("\n\n")) {
            if (httpRequestString.contains("\r\n\r\n")) {
                bodyStartIndex = httpRequestString.indexOf("\r\n\r\n");
                if (bodyStartIndex == -1) {
                    return "";
                }
                // 截取请求体部分
                requestBody = httpRequestString.substring(bodyStartIndex + 4);
            } else {
                bodyStartIndex = httpRequestString.indexOf("\n\n");
                if (bodyStartIndex == -1) {
                    return "";
                }
                requestBody = httpRequestString.substring(bodyStartIndex + 2);
            }
        }
        return decodeRequestBodyIfNeeded(requestBody);
    }

    /**
     * 解码请求正文（如果需要） 例: param1=value%20&param2=value%21
     *
     * @param requestBody 请求主体
     * @return {@link String}
     * @throws UnsupportedEncodingException 不支持编码异常
     */
    public static String decodeRequestBodyIfNeeded(String requestBody) throws UnsupportedEncodingException {
        // 判断请求体中是否包含百分号编码
        if (containsPercentEncoding(requestBody)) {
            // 需要解码
            return URLDecoder.decode(requestBody, StandardCharsets.UTF_8.toString());
        } else {
            // 不需要解码
            return requestBody;
        }
    }

    /**
     * 包含百分比编码
     *
     * @param requestBody 请求主体
     * @return boolean
     */
    private static boolean containsPercentEncoding(String requestBody) {
        // 使用正则表达式检查是否包含百分号编码
        // 这里的正则表达式可以根据你的需求进行调整
        return requestBody.matches(".*%[0-9a-fA-F]{2}.*");
    }

    /**
     * 从完整的 HTTP 响应字符串中提取响应体
     *
     * @param httpResponseString 完整的 HTTP 响应字符串
     * @return 提取的响应体字符串
     */
    public static String extractResponseBody(String httpResponseString) {
        int bodyStartIndex = httpResponseString.indexOf("\r\n\r\n");
        if (bodyStartIndex == -1) {
            return "";
        }
        return httpResponseString.substring(bodyStartIndex + 4);
    }

    /**
     * 优化响应数据内容
     *
     * @param input
     * @return
     */
    public static String extractContent(String input) {
        // 如果没有找到JSON对象，尝试提取JSON数组
        int startIndexArr = input.indexOf('[');
        int startIndexObj = input.indexOf('{');

        if (startIndexArr < 0 || startIndexArr > startIndexObj) {
            // 尝试提取JSON对象
            int endIndexObj = input.lastIndexOf('}');
            if (startIndexObj != -1 && endIndexObj != -1 && endIndexObj > startIndexObj) {
                return input.substring(startIndexObj, endIndexObj + 1).trim();
            }
        } else {
            int endIndexArr = input.lastIndexOf(']');
            if (endIndexArr != -1 && endIndexArr > startIndexArr) {
                return input.substring(startIndexArr, endIndexArr + 1).trim();
            }
        }

        if (startIndexObj < 0) {
            int endIndexArr = input.lastIndexOf(']');
            if (startIndexArr != -1 && endIndexArr != -1 && endIndexArr > startIndexArr) {
                return input.substring(startIndexArr, endIndexArr + 1).trim();
            }
        }

        // 如果没有找到JSON对象或数组，返回空字符串
        return input;
    }


}
