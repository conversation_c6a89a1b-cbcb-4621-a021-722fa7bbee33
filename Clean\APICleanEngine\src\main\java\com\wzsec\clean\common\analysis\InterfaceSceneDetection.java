package com.wzsec.clean.common.analysis;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wzsec.clean.common.utils.*;
import com.wzsec.clean.modules.model.ApiAuthcheckresult;
import com.wzsec.clean.modules.model.ApiCallNetFlow;
import com.wzsec.clean.modules.model.IcAlarmDisposal;
import com.wzsec.clean.modules.model.IcInterfaceInfo;
import com.wzsec.clean.modules.service.IcAlarmDisposalService;
import com.wzsec.clean.modules.service.PcapService;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 接口即时场景检测(随清洗流程嵌入执行)
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
public class InterfaceSceneDetection {

    private static PcapService pcapService = SpringUtils.getApplicationContext().getBean(PcapService.class);

    private static IcAlarmDisposalService icAlarmDisposalService = SpringUtils.getApplicationContext().getBean(IcAlarmDisposalService.class);

    /**
     * 接口鉴权检测
     *
     * @param icInterfaceInfoMap ic接口信息图
     * @param apiCallNetFlow     api调用网络流量
     */
    public static void interfaceAuthenticationDetection(Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                                        ApiCallNetFlow apiCallNetFlow) {
        //接口鉴权开关
        String sceneStatus = ConfigurationManager.getProperty("netflow.collectdatatype").trim();


        String apicode = apiCallNetFlow.getApicode();
        // 获取接口名称
        String interfaceName = "";
        try {
            interfaceName = icInterfaceInfoMap.get(apiCallNetFlow.getApicode()).getApiname();
        } catch (Exception e) {
            //e.printStackTrace();
        }
        if (Const.FLOW_DATA_general.equals(sceneStatus) || StringUtils.isNotBlank(interfaceName)) {
            String reqcontents = apiCallNetFlow.getReqcontent().getParams();
            //获取鉴权方式
            String authenticate = whetherAuthenticate(reqcontents, apiCallNetFlow.getApiuri());
            //获取鉴权信息示例
            String authenticationExample = getValue(reqcontents, apiCallNetFlow.getApiuri());
            //判断请求格式
            String format = getFormat(apiCallNetFlow.getRescontent().getData());
            //采用协议
            String protocolList = apiCallNetFlow.getReqcontent().getParams();
            String[] protocol = protocolList.split("/");
            //未鉴权 开始入库
            ApiAuthcheckresult apiAuthcheckresult = new ApiAuthcheckresult();
            apiAuthcheckresult.setApicode(apiCallNetFlow.getApicode());  //接口编码
            apiAuthcheckresult.setApiname(interfaceName); //接口名称
            apiAuthcheckresult.setUrl(apiCallNetFlow.getApiuri()); //接口路径
            apiAuthcheckresult.setServiceip(apiCallNetFlow.getApiip()); //接口服务端IP
            apiAuthcheckresult.setServiceport(apiCallNetFlow.getClientip()); //接口服务端端口
            apiAuthcheckresult.setProtocol(protocol[0]); //采用协议 http,https
            apiAuthcheckresult.setReqmethod(apiCallNetFlow.getReqmethod());//请求方式 get.post
            apiAuthcheckresult.setReqformat(format);//请求格式xml,json,表单
            apiAuthcheckresult.setAuthmethod(authenticate);//鉴权方式,sign,cookie,token,Authorization
            apiAuthcheckresult.setAuthinfo(authenticationExample);//鉴权信息示例
            //根据鉴权方式判断级别
            if (authenticate.equals("") && Const.AUTHMETHOD_HTTP.equals(protocol[0])) {
                apiAuthcheckresult.setRisk(Const.RISK_HIGH);//高级别
            } else if (authenticate.equals("") && Const.AUTHMETHOD_HTTPS.equals(protocol[0])) {
                apiAuthcheckresult.setRisk(Const.RISK_MIDDLE);//中级别
            } else if (!authenticate.equals("") && Const.AUTHMETHOD_HTTPS.equals(protocol[0])) {
                apiAuthcheckresult.setRisk(Const.RISK_NOT);//无级别
            } else if (!authenticate.equals("") && Const.AUTHMETHOD_HTTP.equals(protocol[0])) {
                apiAuthcheckresult.setRisk(Const.RISK_LOW);//低级别
            } else {
                apiAuthcheckresult.setRisk(Const.RISK_LOW);//低级别
            }
            apiAuthcheckresult.setInserttime(DateUtils.formatDateTime(new Date()));
            //去重，入库
            List<String> authcheckresultApicode = pcapService.getAuthcheckresultApicode();
            if (!authcheckresultApicode.contains(apiAuthcheckresult.getApicode())) {

                pcapService.saveAuthcheckresult(apiAuthcheckresult);

                //插入接口告警处置表
                if (!apiAuthcheckresult.getRisk().equals(Const.RISK_NOT) && StringUtils.isNotBlank(apiAuthcheckresult.getApicode())) {
                    IcAlarmDisposal icAlarmdisposal = new IcAlarmDisposal();
                    // TODO 事件详情定义
                    String str = "该接口:{}(接口名称:{})无鉴权信息";
                    String eventDetails = StrUtil.format(str, apiAuthcheckresult.getApicode(), interfaceName);

                    icAlarmdisposal.setApicode(apiAuthcheckresult.getApicode()); //接口编码
                    icAlarmdisposal.setApiname(interfaceName); //接口名称
                    icAlarmdisposal.setDetectionmodel(Const.DICT_API_INTERFACE_AUTHENTICATION); //检测模型
                    icAlarmdisposal.setCircumstantiality(eventDetails); //事件详情
                    icAlarmdisposal.setRisk(apiAuthcheckresult.getRisk()); //风险程度
                    icAlarmdisposal.setReservefield1(apiAuthcheckresult.getRisk()); //调整级别
                    icAlarmdisposal.setChecktime(DateUtil.now());
                    icAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态

                    icAlarmdisposal.setSourceip(apiCallNetFlow.getClientip());//源ip
                    icAlarmdisposal.setDestinationip(apiCallNetFlow.getApiip());//目标IP
                    icAlarmdisposal.setDestinationport(apiCallNetFlow.getApiport());//目标端口
                    icAlarmdisposal.setEventrule(Const.DICT_API_INTERFACE_AUTHENTICATION_CHARS);// 规则(接口鉴权)
                    icAlarmdisposal.setAccount(apiCallNetFlow.getAccount());//事件相关用户名
                    icAlarmdisposal.setArea(icInterfaceInfoMap.get(apicode) == null ? "" : icInterfaceInfoMap.get(apicode).getArea());//获取区县信息
                    icAlarmdisposal.setDepartment(icInterfaceInfoMap.get(apicode) == null ? "" : icInterfaceInfoMap.get(apicode).getData_source());//获取部门信息

                    icAlarmdisposal.setReservefield2(JSON.toJSONString(apiCallNetFlow));//接口日志

                    icAlarmDisposalService.saveResult(icAlarmdisposal); //保存接口鉴权到告警处置表


                    //告警推送syslog
                    new MonitorRiskAlarmData().sendIcExample(icAlarmdisposal);
                }
            }
        }
    }


    /**
     * 从请求字符串中截取鉴权信息示例
     *
     * @param authenticate
     * @return
     */
    public static String getValue(String authenticate, String url) {
        String type = "";

        String cookie = Const.AUTHMETHOD_COOKIE.replace(":", "");
        String authorization = Const.AUTHMETHOD_AUTHORIZATION.replace(":", "");
        String sign = Const.AUTHMETHOD_SIGN.replace(":", "");
        String token = Const.AUTHMETHOD_TOKEN.replace(":", "");

        try {
            if (url.contains(cookie)) {
                type = getUrlValue(url, cookie);
            } else if (url.contains(authorization)) {
                type = getUrlValue(url, authorization);
            } else if (url.contains(sign)) {
                type = getUrlValue(url, sign);
            } else if (url.contains(token)) {
                type = getUrlValue(url, token);
                //cookie
            } else if (authenticate.indexOf(Const.AUTHMETHOD_COOKIE) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_COOKIE);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
                //Authorization
            } else if (authenticate.indexOf(Const.AUTHMETHOD_AUTHORIZATION) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_AUTHORIZATION);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
                //sign
            } else if (authenticate.indexOf(Const.AUTHMETHOD_SIGN) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_SIGN);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
                //token
            } else if (authenticate.indexOf(Const.AUTHMETHOD_TOKEN) > 0) {
                String[] strArr = authenticate.split(Const.AUTHMETHOD_TOKEN);
                String s = strArr[strArr.length - 1];
                String[] split = s.split(Const.AUTHMETHOD_USER_AGENT);
                type = split[split.length - 2];
            }
        } catch (Exception e) {

        }
        return type;
    }

    /**
     * 判断鉴权方式
     *
     * @param authenticate 请求体
     * @param url          url
     * @return
     */
    public static String whetherAuthenticate(String authenticate, String url) {
        String type = "";
        String cookie = Const.AUTHMETHOD_COOKIE.replace(":", "");
        String authorization = Const.AUTHMETHOD_AUTHORIZATION.replace(":", "");
        String sign = Const.AUTHMETHOD_SIGN.replace(":", "");
        String token = Const.AUTHMETHOD_TOKEN.replace(":", "");
        if (url.contains(cookie)) {
            type = cookie;
        } else if (url.contains(authorization)) {
            type = authorization;
        } else if (url.contains(sign)) {
            type = sign;
        } else if (url.contains(token)) {
            type = token;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_COOKIE) > 0) {
            type = cookie;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_AUTHORIZATION) > 0) {
            type = authorization;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_SIGN) > 0) {
            type = sign;
        } else if (authenticate.indexOf(Const.AUTHMETHOD_TOKEN) > 0) {
            type = token;
        }
        return type;
    }


    /**
     * 从请求字符串中判断请求格式
     *
     * @param format
     * @return
     */
    public static String getFormat(String format) {
        String[] strArr = format.split("/");
        String reqmethod = "";
        try {
            reqmethod = strArr[strArr.length - 2];
            if (format.indexOf(Const.AUTHMETHOD_XML) > 0) {
                reqmethod = Const.AUTHMETHOD_XML;
            } else if (format.indexOf(Const.AUTHMETHOD_JSON) > 0) {
                reqmethod = Const.AUTHMETHOD_JSON;
            }
        } catch (Exception e) {
        }
        return reqmethod;
    }


    /**
     * 获取url值
     *
     * @param url url
     * @param val val
     * @return {@link String}
     */
    public static String getUrlValue(String url, String val) {
        String regEx = "(\\?|&+)(.+?)=([^&]*)";//匹配参数名和参数值的正则表达式
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(url);
        Map<String, String> paramMap = new LinkedHashMap<String, String>();
        while (m.find()) {
            String paramName = m.group(2);//获取参数名
            String paramVal = m.group(3);//获取参数值
            paramMap.put(paramName, paramVal);
        }
        return paramMap.get(val);
    }

}
