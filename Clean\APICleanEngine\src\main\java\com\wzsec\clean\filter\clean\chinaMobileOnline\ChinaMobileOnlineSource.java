package com.wzsec.clean.filter.clean.chinaMobileOnline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 中国移动在线源端ES对象
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChinaMobileOnlineSource {

    @JsonProperty("ABILITY_ID")
    private String abilityId; //能力ID -->接口编码(apicode)

    @JsonProperty("ABILITY_NAME")
    private String abilityName; //能力名称 -->接口名称

    @JsonProperty("ABILITY_SYS")
    private String abilitySys; //能力模块ID -->

    @JsonProperty("ABILITY_SYSNAME")
    private String abilitySysName; // 能力模块名称 -->

    @JsonProperty("CUST_ID")
    private String custId; // 客户ID --> 客户端IP(clientip)

    @JsonProperty("ERROR_MSG_CNTT")
    private String errorMsgCntt; //错误消息

    @JsonProperty("LOG_TYPECD")
    private String logTypecd; // TODO 日志类型(1接入请求,2转接请求,3转接响应,4接入响应,-1接入系统内部异常) 1为请求,4为响应

    @JsonProperty("MONITOR_TYPE")
    private String monitorType; // 监控类型(1接入,2转接;与交易日志关联添加)

    @JsonProperty("MSG_CNTT")
    private String msgCntt; // TODO 交易报文 (请求响应报文)

    @JsonProperty("RTN_TRANSID")
    private String rtnTransid; //平台流水号--> seqnum 请求标识,用于匹配请求响应

    @JsonProperty("SERVICE_ID")
    private String serviceId; //服务ID

    @JsonProperty("SERVICE_NAME")
    private String serviceName; //服务名称

    @JsonProperty("SRV_INVKSTSCD")
    private Integer srvInvkstsCd; // 交易状态ID (0成功,1失败,2无效)

    @JsonProperty("TRDNG_TIME")
    private String trdngTime;  // 交易时间-->请求时间(calltime)
}

