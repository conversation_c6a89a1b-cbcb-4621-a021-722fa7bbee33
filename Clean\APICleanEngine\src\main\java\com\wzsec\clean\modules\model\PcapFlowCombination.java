package com.wzsec.clean.modules.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * pcap流量组合
 *
 * <AUTHOR>
 * @date 2023-11-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PcapFlowCombination {

    /**
     * 接口编码
     */
    private String api_code;

    /**
     * 接口名称
     */
    private String api_name;


    /**
     * 请求数据
     */
    private String request_data;

    /**
     * 响应数据
     */
    private String response_data;

    /**
     * 请求体数据
     */
    private String request_body_data;

    /**
     * 响应体数据
     */
    private String response_body_data;

    /**
     * 请求数据(byte)
     */
    private byte[] request_bytes;

    /**
     * 响应数据(byte)
     */
    private byte[] response_bytes;

    /**
     * 请求体数据(byte)
     */
    private byte[] request_body_bytes;

    /**
     * 响应体数据(byte)
     */
    private byte[] response_body_bytes;

    /**
     * 请求url
     */
    private String request_url;

    /**
     * 客户端IP
     */
    private String client_ip;

    /**
     * 服务端IP
     */
    private String server_ip;

    /**
     * 服务端端口
     */
    private String server_port;

    /**
     * 客户端端口
     */
    private String client_port;

    /**
     * 请求时间
     */
    private String request_time;

    /**
     * 请求标识
     */
    private String seq_num;


    /**
     * 请求方法(GET|POST...)
     */
    private String req_method;

    /**
     * 协议类型
     */
    private String protocol_type;

}
